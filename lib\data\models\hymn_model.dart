import 'dart:convert';

import 'package:get/get.dart';
import 'package:logger/logger.dart';

class HymnCategory {
  final String? id;
  final String? name;

  HymnCategory({this.id, this.name});

  factory HymnCategory.fromJson(Map<String, dynamic> json) {
    return HymnCategory(
      id: json['id']?.toString(),
      name: json['name']?.toString(),
    );
  }

  Map<String, dynamic> toJson() => {'id': id, 'name': name};

  // Helpers for null safety
  bool get hasId => id != null && id!.isNotEmpty;
  bool get hasName => name != null && name!.isNotEmpty;
}

class Lyrics {
  final List<String> verse;
  final List<String> chorus;

  Lyrics({List<String>? verse, List<String>? chorus})
    : verse = verse ?? [],
      chorus = chorus ?? [];

  factory Lyrics.fromJson(Map<String, dynamic> json) {
    return Lyrics(
      verse:
          json['verse'] == null
              ? []
              : List<String>.from(json['verse'].map((x) => x.toString())),
      chorus:
          json['chorus'] == null
              ? []
              : List<String>.from(json['chorus'].map((x) => x.toString())),
    );
  }

  Map<String, dynamic> toJson() => {'verse': verse, 'chorus': chorus};

  // Helper methods
  bool get hasVerse => verse.isNotEmpty;
  bool get hasChorus => chorus.isNotEmpty;
  bool get isEmpty => !hasVerse && !hasChorus;
}

class HymnModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final String? artist;
  final String? album;
  final String? username;
  final Lyrics lyrics;
  final String? mediaUrl;
  final String? organisationId;
  final String? createdByUserId;
  final String? updatedByUserId;
  final List<HymnCategory> categories;

  HymnModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    String? title,
    String? artist,
    String? album,
    this.username,
    Lyrics? lyrics,
    String? mediaUrl,
    this.organisationId,
    this.createdByUserId,
    this.updatedByUserId,
    List<HymnCategory>? categories,
  }) : title = title?.trim(),
       artist = artist?.trim(),
       album = album?.trim(),
       mediaUrl = mediaUrl?.trim(),
       lyrics = lyrics ?? Lyrics(),
       categories = categories ?? [];
  factory HymnModel.fromJson(Map<String, dynamic> json) {
    try {
      return HymnModel(
        id: json['id']?.toString(),
        createdAt:
            json['created_at'] != null
                ? DateTime.tryParse(json['created_at'].toString())
                : null,
        updatedAt:
            json['updated_at'] != null
                ? DateTime.tryParse(json['updated_at'].toString())
                : null,
        deletedAt: json['deleted_at'],
        title: json['title']?.toString(),
        artist: json['artist']?.toString(),
        album: json['album']?.toString(),
        username: json['username']?.toString(),
        lyrics:
            json['lyrics'] != null ? Lyrics.fromJson(json['lyrics']) : Lyrics(),
        mediaUrl: json['media_url']?.toString(),
        organisationId: json['organisation_id']?.toString(),
        createdByUserId: json['created_by_user_id']?.toString(),
        updatedByUserId: json['updated_by_user_id']?.toString(),
        categories:
            json['categories'] != null
                ? List<HymnCategory>.from(
                      json['categories'].map(
                        (x) => HymnCategory.fromJson(x as Map<String, dynamic>),
                      ),
                    )
                    .where((category) => category.hasId || category.hasName)
                    .toList()
                : [],
      );
    } catch (e) {
      Get.find<Logger>().e('Error parsing HymnModel: $e');
      return HymnModel(lyrics: Lyrics(), categories: []);
    }
  }

  // Helper getters
  bool get hasTitle => title != null && title!.isNotEmpty;
  bool get hasArtist => artist != null && artist!.isNotEmpty;
  bool get hasAlbum => album != null && album!.isNotEmpty;
  bool get hasMediaUrl => mediaUrl != null && mediaUrl!.isNotEmpty;
  bool get hasCategories => categories.isNotEmpty;

  Map<String, dynamic> toJson() => {
    'id': id,
    'created_at': createdAt?.toIso8601String(),
    'updated_at': updatedAt?.toIso8601String(),
    'deleted_at': deletedAt,
    'title': title,
    'artist': artist,
    'album': album,
    'username': username,
    'lyrics': lyrics.toJson(),
    'media_url': mediaUrl,
    'organisation_id': organisationId,
    'created_by_user_id': createdByUserId,
    'updated_by_user_id': updatedByUserId,
    'categories': categories.map((x) => x.toJson()).toList(),
  };
}

class CreatedByUser {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? phoneNumber;
  final String? firstName;
  final String? secondName;
  final String? username;
  final String? email;
  final String? idNumber;
  final dynamic balance;
  final String? birthDate;
  final String? countryCode;
  final String? county;
  final String? subCounty;
  final String? ward;
  final String? secondaryNumber;
  final String? profileUrl;
  final String? role;
  final String? status;
  final dynamic addresses;
  final dynamic devices;

  CreatedByUser({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    String? phoneNumber,
    String? firstName,
    String? secondName,
    String? username,
    String? email,
    String? idNumber,
    this.balance,
    String? birthDate,
    String? countryCode,
    String? county,
    String? subCounty,
    String? ward,
    String? secondaryNumber,
    String? profileUrl,
    String? role,
    String? status,
    this.addresses,
    this.devices,
  }) : phoneNumber = phoneNumber?.trim(),
       firstName = firstName?.trim(),
       secondName = secondName?.trim(),
       username = username?.trim(),
       email = email?.trim(),
       idNumber = idNumber?.trim(),
       birthDate = birthDate?.trim(),
       countryCode = countryCode?.trim(),
       county = county?.trim(),
       subCounty = subCounty?.trim(),
       ward = ward?.trim(),
       secondaryNumber = secondaryNumber?.trim(),
       profileUrl = profileUrl?.trim(),
       role = role?.trim(),
       status = status?.trim();

  CreatedByUser copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? phoneNumber,
    String? firstName,
    String? secondName,
    String? username,
    String? email,
    String? idNumber,
    dynamic balance,
    String? birthDate,
    String? countryCode,
    String? county,
    String? subCounty,
    String? ward,
    String? secondaryNumber,
    String? profileUrl,
    String? role,
    String? status,
    dynamic addresses,
    dynamic devices,
  }) => CreatedByUser(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    firstName: firstName ?? this.firstName,
    secondName: secondName ?? this.secondName,
    username: username ?? this.username,
    email: email ?? this.email,
    idNumber: idNumber ?? this.idNumber,
    balance: balance ?? this.balance,
    birthDate: birthDate ?? this.birthDate,
    countryCode: countryCode ?? this.countryCode,
    county: county ?? this.county,
    subCounty: subCounty ?? this.subCounty,
    ward: ward ?? this.ward,
    secondaryNumber: secondaryNumber ?? this.secondaryNumber,
    profileUrl: profileUrl ?? this.profileUrl,
    role: role ?? this.role,
    status: status ?? this.status,
    addresses: addresses ?? this.addresses,
    devices: devices ?? this.devices,
  );

  factory CreatedByUser.fromRawJson(String str) =>
      CreatedByUser.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CreatedByUser.fromJson(Map<String, dynamic> json) {
    try {
      return CreatedByUser(
        id: json["ID"],
        createdAt:
            json["CreatedAt"] != null
                ? DateTime.tryParse(json["CreatedAt"].toString())
                : null,
        updatedAt:
            json["UpdatedAt"] != null
                ? DateTime.tryParse(json["UpdatedAt"].toString())
                : null,
        deletedAt: json["DeletedAt"],
        phoneNumber: json["phone_number"]?.toString(),
        firstName: json["first_name"]?.toString(),
        secondName: json["second_name"]?.toString(),
        username: json["username"]?.toString(),
        email: json["email"]?.toString(),
        idNumber: json["id_number"]?.toString(),
        balance: json["balance"],
        birthDate: json["birth_date"]?.toString(),
        countryCode: json["country_code"]?.toString(),
        county: json["county"]?.toString(),
        subCounty: json["sub_county"]?.toString(),
        ward: json["ward"]?.toString(),
        secondaryNumber: json["secondary_number"]?.toString(),
        profileUrl: json["profile_url"]?.toString(),
        role: json["role"]?.toString(),
        status: json["status"]?.toString(),
        addresses: json["addresses"],
        devices: json["devices"],
      );
    } catch (e) {
      Get.find<Logger>().e('Error parsing CreatedByUser: $e');
      return CreatedByUser();
    }
  }

  Map<String, dynamic> toJson() => {
    "ID": id,
    "CreatedAt": createdAt?.toIso8601String(),
    "UpdatedAt": updatedAt?.toIso8601String(),
    "DeletedAt": deletedAt,
    "phone_number": phoneNumber,
    "first_name": firstName,
    "second_name": secondName,
    "username": username,
    "email": email,
    "id_number": idNumber,
    "balance": balance,
    "birth_date": birthDate,
    "country_code": countryCode,
    "county": county,
    "sub_county": subCounty,
    "ward": ward,
    "secondary_number": secondaryNumber,
    "profile_url": profileUrl,
    "role": role,
    "status": status,
    "addresses": addresses,
    "devices": devices,
  };

  // Helper methods
  bool get hasName => firstName != null && firstName!.isNotEmpty;
  bool get hasFullName =>
      hasName && secondName != null && secondName!.isNotEmpty;
  bool get hasPhoneNumber => phoneNumber != null && phoneNumber!.isNotEmpty;
  bool get hasEmail => email != null && email!.isNotEmpty;
  bool get hasProfileUrl => profileUrl != null && profileUrl!.isNotEmpty;
  bool get hasRole => role != null && role!.isNotEmpty;
  bool get hasLocation => county != null && county!.isNotEmpty;
}
