import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import '../../controllers/staff_roles_controller.dart';

class StaffRoleFilterWidget extends StatefulWidget {
  const StaffRoleFilterWidget({super.key});

  @override
  State<StaffRoleFilterWidget> createState() => _StaffRoleFilterWidgetState();
}

class _StaffRoleFilterWidgetState extends State<StaffRoleFilterWidget> {
  final StaffRolesController controller = Get.find<StaffRolesController>();
  final TextEditingController nameFilterController = TextEditingController();
  final TextEditingController statusFilterController = TextEditingController();

  @override
  void dispose() {
    nameFilterController.dispose();
    statusFilterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Filter Roles',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    labelText: 'Role Name',
                    hintText: 'Filter by role name',

                    controller: nameFilterController,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Status',
                      hintText: 'Filter by status',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.filter_list),
                    ),
                    items: const [
                      DropdownMenuItem<String>(value: '', child: Text('All')),
                      DropdownMenuItem<String>(
                        value: 'ACTIVE',
                        child: Text('Active'),
                      ),
                      DropdownMenuItem<String>(
                        value: 'INACTIVE',
                        child: Text('Inactive'),
                      ),
                    ],
                    onChanged: (value) {
                      statusFilterController.text = value ?? '';
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CustomButton(
                  style: CustomButtonStyle.outlined,
                  onPressed: () {
                    nameFilterController.clear();
                    statusFilterController.clear();
                    //TODO Apply filters (this would update the grid)
                  },
                 
                  label: const Text('Clear All'),
                ),
                const SizedBox(width: 16),
                CustomButton(
                  onPressed: () {
                    //TODO Apply filters (this would update the grid)
                  },
                  label: const Text('Apply Filters'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
