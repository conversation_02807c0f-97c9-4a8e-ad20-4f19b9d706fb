import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'custom_button.dart';

/// Example usage of the improved CustomButton widget
class CustomButtonExample extends StatefulWidget {
  const CustomButtonExample({super.key});

  @override
  State<CustomButtonExample> createState() => _CustomButtonExampleState();
}

class _CustomButtonExampleState extends State<CustomButtonExample> {
  bool isLoading = false;
  bool isEnabled = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('CustomButton Examples'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Basic filled button
            CustomButton(
              text: 'Filled Button',
              onPressed: () => _showMessage('Filled button pressed'),
              style: CustomButtonStyle.filled,
            ),
            const SizedBox(height: 16),

            // Outlined button with custom color
            CustomButton(
              text: 'Outlined Button',
              color: Colors.green,
              onPressed: () => _showMessage('Outlined button pressed'),
              style: CustomButtonStyle.outlined,
            ),
            const SizedBox(height: 16),

            // Text button
            CustomButton(
              text: 'Text Button',
              color: Colors.purple,
              onPressed: () => _showMessage('Text button pressed'),
              style: CustomButtonStyle.text,
            ),
            const SizedBox(height: 16),

            // Elevated button
            CustomButton(
              text: 'Elevated Button',
              color: Colors.orange,
              onPressed: () => _showMessage('Elevated button pressed'),
              style: CustomButtonStyle.elevated,
            ),
            const SizedBox(height: 16),

            // Claymorphism button
            CustomButton(
              text: 'Claymorphism Button',
              onPressed: () => _showMessage('Claymorphism button pressed'),
              style: CustomButtonStyle.claymorphism,
            ),
            const SizedBox(height: 16),

            // Button with icon
            CustomButton(
              text: 'Button with Icon',
              icon: const Icon(IconlyBold.heart),
              color: Colors.red,
              onPressed: () => _showMessage('Icon button pressed'),
              style: CustomButtonStyle.filled,
            ),
            const SizedBox(height: 16),

            // Button with label (overrides text)
            CustomButton(
              text: 'This text will be ignored',
              label: const Text('Label overrides text'),
              color: Colors.blue,
              onPressed: () => _showMessage('Label button pressed'),
              style: CustomButtonStyle.filled,
            ),
            const SizedBox(height: 16),

            // Loading button
            CustomButton(
              text: 'Loading Button',
              isLoading: isLoading,
              onPressed: () => _toggleLoading(),
              style: CustomButtonStyle.filled,
            ),
            const SizedBox(height: 16),

            // Disabled button
            CustomButton(
              text: 'Disabled Button',
              isEnabled: isEnabled,
              onPressed: () => _showMessage('This should not show when disabled'),
              style: CustomButtonStyle.filled,
            ),
            const SizedBox(height: 16),

            // Control buttons
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: isLoading ? 'Stop Loading' : 'Start Loading',
                    onPressed: () => _toggleLoading(),
                    style: CustomButtonStyle.outlined,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomButton(
                    text: isEnabled ? 'Disable' : 'Enable',
                    onPressed: () => _toggleEnabled(),
                    style: CustomButtonStyle.outlined,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _toggleLoading() {
    setState(() {
      isLoading = !isLoading;
    });
  }

  void _toggleEnabled() {
    setState(() {
      isEnabled = !isEnabled;
    });
  }
}
