import 'dart:convert';

// To parse this JSON data, do
//
//     final categoryResponse = categoryResponseFromJson(jsonString);

CategoryResponse categoryResponseFromJson(String str) =>
    CategoryResponse.fromJson(json.decode(str));

String categoryResponseTo<PERSON>son(CategoryResponse data) =>
    json.encode(data.toJson());

class CategoryResponse {
  final bool? status;
  final String? message;
  final CategoryData? data;
  final dynamic errors;

  CategoryResponse({
    this.status,
    this.message,
    this.data,
    this.errors,
  });

  CategoryResponse copyWith({
    bool? status,
    String? message,
    CategoryData? data,
    dynamic errors,
  }) =>
      CategoryResponse(
        status: status ?? this.status,
        message: message ?? this.message,
        data: data ?? this.data,
        errors: errors ?? this.errors,
      );

  factory CategoryResponse.fromJson(Map<String, dynamic> json) =>
      CategoryResponse(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null ? null : CategoryData.from<PERSON>son(json["data"]),
        errors: json["errors"],
      );

  Map<String, dynamic> to<PERSON>son() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
        "errors": errors,
      };
}

class CategoryData {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final String? description;
  final String? organisationId;
  final dynamic organisation;
  final bool? isGeneral;

  CategoryData({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.description,
    this.organisationId,
    this.organisation,
    this.isGeneral,
  });

  CategoryData copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? title,
    String? description,
    String? organisationId,
    dynamic organisation,
    bool? isGeneral,
  }) =>
      CategoryData(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        title: title ?? this.title,
        description: description ?? this.description,
        organisationId: organisationId ?? this.organisationId,
        organisation: organisation ?? this.organisation,
        isGeneral: isGeneral ?? this.isGeneral,
      );

  factory CategoryData.fromJson(Map<String, dynamic> json) => CategoryData(
        id: json["id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        deletedAt: json["deleted_at"],
        title: json["title"],
        description: json["description"],
        organisationId: json["organisation_id"],
        organisation: json["organisation"],
        isGeneral: json["is_general"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt,
        "title": title,
        "description": description,
        "organisation_id": organisationId,
        "organisation": organisation,
        "is_general": isGeneral,
      };
}

// Model for category creation payload
class CreateCategoryPayload {
  final String title;
  final String description;
  final String organisationId;

  CreateCategoryPayload({
    required this.title,
    required this.description,
    required this.organisationId,
  });

  Map<String, dynamic> toJson() => {
        "title": title,
        "description": description,
        "organisation_id": organisationId,
      };
}
