import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:onechurch/features/inventory/controllers/inventory_dashboard_controller.dart';

class QuickActionsWidget extends StatelessWidget {
  const QuickActionsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<InventoryDashboardController>();
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Card(
        elevation: 1,
        shadowColor: colorScheme.shadow.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    IconlyLight.activity,
                    color: colorScheme.primary,
                    size: 20,
                  ),
                  Gap(8.w),
                  Text(
                    'Quick Actions',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.primary,
                    ),
                  ),
                ],
              ),
              Gap(16.h),
              Obx(
                () => Row(
                  children: [
                    if (controller.isItemsView.value) ...[
                      Expanded(
                        child: _buildActionButton(
                          context,
                          'Add Item',
                          'Create new inventory item',
                          IconlyLight.plus,
                          colorScheme.primary,
                          () => controller.navigateToCreateItem(context),
                        ),
                      ),
                      Gap(12.w),
                      Expanded(
                        child: _buildActionButton(
                          context,
                          'Import',
                          'Import items from file',
                          IconlyLight.upload,
                          const Color(0xFF10B981),
                          () => _showImportDialog(context),
                        ),
                      ),
                    ] else ...[
                      Expanded(
                        child: _buildActionButton(
                          context,
                          'Record',
                          'Record inventory movement',
                          IconlyLight.document,
                          colorScheme.primary,
                          () => controller.navigateToRecordInventory(context),
                        ),
                      ),
                      Gap(12.w),
                      Expanded(
                        child: _buildActionButton(
                          context,
                          'Export',
                          'Export records to file',
                          IconlyLight.download,
                          const Color(0xFFF59E0B),
                          () => _showExportDialog(context),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.all(12.r),
        decoration: BoxDecoration(
          color: color.withOpacity(0.08),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: color.withOpacity(0.2), width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 18),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_ios,
                  color: color.withOpacity(0.6),
                  size: 12,
                ),
              ],
            ),
            Gap(8.h),
            Text(
              title,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
            Gap(2.h),
            Text(
              subtitle,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _showImportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Import Items'),
            content: const Text(
              'Import functionality will be available soon. You can upload CSV or Excel files to bulk import inventory items.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showExportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Export Records'),
            content: const Text(
              'Export functionality will be available soon. You can download inventory records as CSV or Excel files.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }
}
