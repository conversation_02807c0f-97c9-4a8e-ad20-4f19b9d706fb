class RegistrationData {
  String? phoneNumber;
  String? firstName;
  String? secondName;
  String? email;
  String? idNumber;
  String? referredByCode;
  String? countryCode;
  String? county;
  String? subCounty;
  double? latitude;
  double? longitude;
  String? profileUrl;

  RegistrationData({
    this.phoneNumber,
    this.firstName,
    this.secondName,
    this.email,
    this.idNumber,
    this.referredByCode,
    this.countryCode,
    this.county,
    this.subCounty,
    this.latitude,
    this.longitude,
    this.profileUrl,
  });

  Map<String, dynamic> toJson() {
    return {
      'phone_number': phoneNumber,
      'first_name': firstName,
      'second_name': secondName,
      'email': email,
      'id_number': idNumber,
      'referred_by_code': referredByCode,
      'country_code': countryCode,
      'county': county,
      'sub_county': subCounty,
      'latitude': latitude,
      'longitude': longitude,
      'profile_url': profileUrl,
    };
  }
} 