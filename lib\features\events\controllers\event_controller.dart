import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../data/models/event_model.dart';
import '../../../features/media_upload/models/media_model.dart';
import '../services/event_service.dart';

class EventController extends GetxController {
  final EventService _eventService = EventService();
  final logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxBool isUpdating = false.obs;
  final RxBool isSubmitting = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<EventModel> events = <EventModel>[].obs;
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalItems = 0.obs;
  final RxInt pageSize = 10.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;
  final RxString searchQuery = ''.obs;
  final TextEditingController searchController = TextEditingController();

  // Media-related variables
  final RxList<MediaModel> mediaItems = <MediaModel>[].obs;
  final RxString mediaTypeValue = ''.obs;

  // Date filter variables
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  // Status filter variable
  final RxString statusFilter = RxString('');

  @override
  void onInit() {
    super.onInit();
    fetchEvents();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Clear form fields and media items
  void clearFormFields() {
    mediaItems.clear();
    mediaTypeValue.value = '';
  }

  // Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    currentPage.value = 0; // Reset to first page when searching
    fetchEvents();
  }

  // Set date filters
  void setDateFilters(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0; // Reset to first page when filtering
    fetchEvents();
  }

  // Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    searchController.clear();
    startDate.value = null;
    endDate.value = null;
    statusFilter.value = '';
    currentPage.value = 0;
    fetchEvents();
  }

  // Search events
  void searchEvents(String query) {
    searchQuery.value = query;
    currentPage.value = 0;
    fetchEvents();
  }

  // Filter events by status
  void filterEventsByStatus(String status) {
    statusFilter.value = status;
    currentPage.value = 0;
    fetchEvents();
  }

  // Filter events by location
  void filterEventsByLocation(String location) {
    searchQuery.value = location;
    currentPage.value = 0;
    fetchEvents();
  }

  // Filter events by date range
  void filterEventsByDateRange(DateTime start, DateTime end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0;
    fetchEvents();
  }

  // Navigate to next page
  void nextPage() {
    if (!isLastPage.value) {
      currentPage.value++;
      fetchEvents();
    }
  }

  // Navigate to previous page
  void previousPage() {
    if (currentPage.value > 0) {
      currentPage.value--;
      fetchEvents();
    }
  }

  // Refresh events
  Future<void> refreshEvents() async {
    currentPage.value = 0;
    await fetchEvents();
    return;
  }

  // Fetch events with pagination and filters
  Future<void> fetchEvents() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      logger.d(
        'Fetching events: page=${currentPage.value}, size=${pageSize.value}',
      );

      final response = await _eventService.fetchEvents(
        page: currentPage.value,
        size: pageSize.value,
        searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
        startDate: startDate.value,
        endDate: endDate.value,
      );

      // Log the raw response for debugging
      logger.d('Raw API response: $response');

      if (response["status"] == true) {
        final data = response["data"];
        logger.d('Response data: $data');

        // Check if the response has the nested events structure
        final eventsData = data["events"] ?? data;
        logger.d('Events data: $eventsData');

        // Parse pagination data
        currentPage.value = eventsData["page"] ?? 0;
        totalPages.value = eventsData["total_pages"] ?? 0;
        totalItems.value = eventsData["total"] ?? 0;
        isLastPage.value = eventsData["last"] ?? false;
        isFirstPage.value = eventsData["first"] ?? true;

        // Parse events data
        final List<dynamic> items = eventsData["items"] ?? [];
        logger.d('Found ${items.length} events in response');

        if (items.isNotEmpty) {
          logger.d('First event in response: ${items.first}');
        }

        // Clear existing events and add new ones
        events.clear();
        final newEvents =
            items.map((item) => EventModel.fromJson(item)).toList();
        events.addAll(newEvents);

        logger.d('Events list updated, now contains ${events.length} events');
      } else {
        errorMessage.value = response["message"] ?? "Failed to fetch events";
        logger.e("Error fetching events: ${response["message"]}");
      }
    } catch (e, stackTrace) {
      errorMessage.value = "An error occurred while fetching events";
      logger.e("Exception fetching events: $e");
      logger.e("Stack trace: $stackTrace");
    } finally {
      isLoading.value = false;
    }
  }

  // Load more events for infinite scrolling
  Future<void> loadMoreEvents() async {
    if (isLastPage.value || isLoading.value) return;

    currentPage.value++;
    isLoading.value = true;

    try {
      final response = await _eventService.fetchEvents(
        page: currentPage.value,
        size: pageSize.value,
        searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
        startDate: startDate.value,
        endDate: endDate.value,
      );

      if (response["status"] ?? false) {
        final data = response["data"];

        // Check if the response has the nested events structure
        final eventsData = data["events"] ?? data;

        // Update pagination data
        totalPages.value = eventsData["total_pages"] ?? 0;
        totalItems.value = eventsData["total"] ?? 0;
        isLastPage.value = eventsData["last"] ?? false;

        // Parse and add new events
        final List<dynamic> items = eventsData["items"] ?? [];
        final newEvents =
            items.map((item) => EventModel.fromJson(item)).toList();
        events.addAll(newEvents);
      } else {
        errorMessage.value =
            response["message"] ?? "Failed to load more events";
        logger.e("Error loading more events: ${response["message"]}");
      }
    } catch (e) {
      errorMessage.value = "An error occurred while loading more events";
      logger.e("Exception loading more events: $e");
    } finally {
      isLoading.value = false;
    }
  }

  // Get upcoming events (events with start date in the future)
  List<EventModel> getUpcomingEvents() {
    final now = DateTime.now();
    return events.where((event) {
      if (event.startDate == null) return false;
      final eventDate = DateTime.parse(event.startDate!);
      return eventDate.isAfter(now);
    }).toList();
  }

  // Get past events (events with end date in the past)
  List<EventModel> getPastEvents() {
    final now = DateTime.now();
    return events.where((event) {
      if (event.endDate == null) return false;
      final eventDate = DateTime.parse(event.endDate!);
      return eventDate.isBefore(now);
    }).toList();
  }

  // Get event by ID
  Future<EventModel?> getEventById(String eventId) async {
    try {
      final response = await _eventService.getEventById(eventId);

      if (response["status"] ?? false) {
        final data = response["data"];
        return EventModel.fromJson(data);
      } else {
        errorMessage.value =
            response["message"] ?? "Failed to fetch event details";
        logger.e("Error fetching event details: ${response["message"]}");
        return null;
      }
    } catch (e) {
      errorMessage.value = "An error occurred while fetching event details";
      logger.e("Exception fetching event details: $e");
      return null;
    }
  }

  // Create a new event
  Future<EventModel?> createEvent(EventModel event) async {
    try {
      isUpdating(true);

      // Create the event data
      final Map<String, dynamic> eventData = {
        'title': event.title,
        'description': event.description,
        'start_date': event.startDate,
        'end_date': event.endDate,
        'location': event.location,
        'frequency': event.frequency,
        'status': event.status,
        'organisation_id': event.organisationId ?? "",
      };

      // Add media items if available
      if (mediaItems.isNotEmpty) {
        // Convert MediaModel objects to JSON format expected by the API
        eventData['media'] = mediaItems.map((media) => media.toJson()).toList();
      }

      logger.d('Creating event with payload: $eventData');

      // Call the service with the prepared data
      final response = await _eventService.createEventWithData(eventData);

      if (response["status"] ?? false) {
        final data = response["data"];
        final newEvent = EventModel.fromJson(data);
        events.add(newEvent);
        return newEvent;
      } else {
        errorMessage.value = response["message"] ?? "Failed to create event";
        logger.e("Error creating event: ${response["message"]}");
        return null;
      }
    } catch (e) {
      errorMessage.value = "An error occurred while creating event";
      logger.e("Exception creating event: $e");
      return null;
    } finally {
      isUpdating(false);
    }
  }

  // Update an existing event
  Future<EventModel?> updateEvent(String eventId, EventModel event) async {
    try {
      isUpdating(true);

      // Create the event data
      final Map<String, dynamic> eventData = {
        'id': eventId,
        'title': event.title,
        'description': event.description,
        'start_date': event.startDate,
        'end_date': event.endDate,
        'location': event.location,
        'frequency': event.frequency,
        'status': event.status,
        'organisation_id': event.organisationId,
      };

      // Add media items if available
      if (mediaItems.isNotEmpty) {
        // Convert MediaModel objects to JSON format expected by the API
        eventData['media'] = mediaItems.map((media) => media.toJson()).toList();
      }

      logger.d('Updating event with payload: $eventData');

      // Call the service with the prepared data
      final response = await _eventService.updateEventWithData(
        eventId,
        eventData,
      );

      if (response["status"] ?? false) {
        final data = response["data"];
        final updatedEvent = EventModel.fromJson(data);

        // Update the event in the list
        final index = events.indexWhere((e) => e.id == eventId);
        if (index != -1) {
          events[index] = updatedEvent;
        }

        return updatedEvent;
      } else {
        errorMessage.value = response["message"] ?? "Failed to update event";
        logger.e("Error updating event: ${response["message"]}");
        return null;
      }
    } catch (e) {
      errorMessage.value = "An error occurred while updating event";
      logger.e("Exception updating event: $e");
      return null;
    } finally {
      isUpdating(false);
    }
  }

  // Delete an event
  Future<bool> deleteEvent(String eventId) async {
    try {
      final response = await _eventService.deleteEvent(eventId);

      if (response["status"] ?? false) {
        // Remove the event from the list
        events.removeWhere((event) => event.id == eventId);
        return true;
      } else {
        errorMessage.value = response["message"] ?? "Failed to delete event";
        logger.e("Error deleting event: ${response["message"]}");
        return false;
      }
    } catch (e) {
      errorMessage.value = "An error occurred while deleting event";
      logger.e("Exception deleting event: $e");
      return false;
    }
  }
}
