import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import '../services/media_services.dart';
import '../models/media_model.dart';

class MediaController extends GetxController {
  final MediaService _mediaService = Get.put(MediaService());
  final logger = Get.find<Logger>();

  // Observable variables
  final RxList<MediaModel> mediaItems = <MediaModel>[].obs;
  final RxList<MediaModel> selectedMedia = <MediaModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isUploading = false.obs;
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxInt pageSize = 20.obs;
  final RxBool hasMoreData = true.obs;
  final RxString selectedCategory = 'all'.obs;

  // Search and filter
  final titleController = TextEditingController();
  final RxString selectedType = 'all'.obs;

  @override
  void onInit() {
    super.onInit();
    fetchMedia();
  }

  @override
  void onClose() {
    titleController.dispose();
    super.onClose();
  }

  // Fetch media with pagination
  Future<void> fetchMedia({bool refresh = false}) async {
    if (refresh) {
      currentPage.value = 1;
      mediaItems.clear();
      hasMoreData.value = true;
    }

    if (!hasMoreData.value && !refresh) return;

    isLoading.value = true;

    try {
      final response = await _mediaService.getMedia(
        page: currentPage.value,
        size: pageSize.value,
        title: titleController.text.isEmpty ? null : titleController.text,
        type: selectedType.value == 'all' ? null : selectedType.value,
        category:
            selectedCategory.value == 'all' ? null : selectedCategory.value,
      );

      final data = response['data'] as Map<String, dynamic>;
      final items = data['items'] as List<dynamic>? ?? [];
      final pagination =
          data['pagination'] as Map<String, dynamic>? ?? {'total_pages': 1};

      final List<MediaModel> newItems = _mediaService.parseMedia(items);

      if (refresh) {
        mediaItems.value = newItems;
      } else {
        mediaItems.addAll(newItems);
      }

      totalPages.value = pagination['total_pages'] as int? ?? 1;
      hasMoreData.value = currentPage.value < totalPages.value;

      if (hasMoreData.value) {
        currentPage.value++;
      }
    } catch (e) {
      logger.e('Error fetching media: $e');
      // Return empty array on error
      if (refresh) {
        mediaItems.value = [];
      }
      ToastUtils.showSuccessToast('Error', 'Failed to load media items');
    } finally {
      isLoading.value = false;
    }
  }

  // Load more media items when scrolling
  void loadMore() {
    if (!isLoading.value && hasMoreData.value) {
      fetchMedia();
    }
  }

  // Upload a single media file from XFile
  Future<MediaModel?> uploadSingleMedia(XFile file) async {
    // Prevent multiple uploads at the same time
    if (isUploading.value) {
      ToastUtils.showInfoToast(
        'Upload in Progress',
        'Please wait for the current upload to complete',
      );
      return null;
    }

    isUploading.value = true;
    update();

    try {
      MediaModel media;
      final originalFileName = file.name;
      final filePath = file.path;

      // Remove all whitespace from filename
      final fileName = originalFileName
          .replaceAll(' ', '')
          .replaceAll(RegExp(r'\s+'), '');
      final mediaType = getMediaTypeFromExtension(fileName);

      final organisationId = getOrganizationId();
      final category = getCategory();
      final formattedFileName =
          '/$organisationId/$category/$mediaType/$fileName';

      // Enhanced logging for debugging with debugPrint for visibility
      debugPrint('=== MediaController.uploadSingleMedia START ===');
      debugPrint('Original file name: $originalFileName');
      debugPrint('Cleaned file name: $fileName');
      debugPrint('Original file path: $filePath');
      debugPrint('Detected media type: $mediaType');
      debugPrint('Organisation ID: $organisationId');
      debugPrint('Category: $category');
      debugPrint('Formatted filename: $formattedFileName');
      debugPrint('Platform: ${kIsWeb ? "Web" : "Mobile"}');

      if (kIsWeb) {
        // For web, read as bytes
        final bytes = await file.readAsBytes();
        logger.d('Web upload - File size in bytes: ${bytes.length}');

        media = await _mediaService.uploadWebMedia(
          bytes: bytes,
          fileName: formattedFileName,
          title: fileName,
          type: mediaType,
          category: category,
          organisationId: organisationId,
          sizeBytes: bytes.length,
        );
      } else {
        // For mobile platforms
        final fileToUpload = File(file.path);
        final fileSize = await fileToUpload.length();

        logger.d('Mobile upload - File path: ${fileToUpload.path}');
        logger.d('Mobile upload - File size: $fileSize bytes');
        logger.d('Mobile upload - File exists: ${await fileToUpload.exists()}');

        media = await _mediaService.uploadMedia(
          file: fileToUpload,
          title: fileName,
          fileName: formattedFileName,
          type: mediaType,
          category: category,
          organisationId: organisationId,
          sizeBytes: fileSize,
        );
      }

      // Add to the beginning of the list
      mediaItems.insert(0, media);

      // Select the newly uploaded media
      if (!selectedMedia.contains(media)) {
        selectedMedia.add(media);
      }

      // Enhanced logging for successful upload
      logger.d('=== MediaController.uploadSingleMedia SUCCESS ===');
      logger.d('Uploaded media ID: ${media.id}');
      logger.d('Uploaded media URL: ${media.mediaUrl}');
      logger.d('Uploaded media type: ${media.type}');
      logger.d('Uploaded media category: ${media.category}');
      logger.d('Uploaded media size: ${media.sizeBytes} bytes');

      ToastUtils.showSuccessToast('Success', 'Media uploaded successfully');

      update();
      return media;
    } catch (e) {
      logger.e('=== MediaController.uploadSingleMedia ERROR ===');
      logger.e('Error uploading media: $e');
      logger.e('Stack trace: ${StackTrace.current}');
      ToastUtils.showErrorToast(
        'Error',
        'Failed to upload media: ${e.toString()}',
      );
      return null;
    } finally {
      isUploading.value = false;
      update();
      logger.d('=== MediaController.uploadSingleMedia END ===');
    }
  }

  // Upload multiple media files from XFile list
  Future<List<MediaModel>> uploadMultipleMedia(List<XFile> files) async {
    // Prevent multiple uploads at the same time
    if (isUploading.value) {
      ToastUtils.showInfoToast(
        'Upload in Progress',
        'Please wait for the current upload to complete',
      );
      return [];
    }

    isUploading.value = true;
    update();

    final List<MediaModel> uploadedMedia = [];

    try {
      final organisationId = getOrganizationId();
      final category = getCategory();

      // Enhanced logging for batch upload
      logger.d('=== MediaController.uploadMultipleMedia START ===');
      logger.d('Number of files to upload: ${files.length}');
      logger.d('Organisation ID: $organisationId');
      logger.d('Category: $category');

      for (int i = 0; i < files.length; i++) {
        final file = files[i];
        try {
          final originalFileName = file.name;
          final filePath = file.path;

          // Remove all whitespace from filename
          final fileName = originalFileName
              .replaceAll(' ', '')
              .replaceAll(RegExp(r'\s+'), '');
          final mediaType = getMediaTypeFromExtension(fileName);
          final formattedFileName =
              '/$organisationId/$category/$mediaType/$fileName';

          logger.d('--- Processing file ${i + 1}/${files.length} ---');
          logger.d('Original file name: $originalFileName');
          logger.d('Cleaned file name: $fileName');
          logger.d('File path: $filePath');
          logger.d('Media type: $mediaType');
          logger.d('Formatted filename: $formattedFileName');

          MediaModel media;
          if (kIsWeb) {
            final bytes = await file.readAsBytes();
            media = await _mediaService.uploadWebMedia(
              bytes: bytes,
              fileName: formattedFileName,
              title: fileName,
              type: mediaType,
              category: category,
              organisationId: organisationId,
              sizeBytes: bytes.length,
            );
          } else {
            final fileToUpload = File(file.path);
            final fileSize = await fileToUpload.length();
            media = await _mediaService.uploadMedia(
              file: fileToUpload,
              title: fileName,
              fileName: formattedFileName,
              type: mediaType,
              category: category,
              organisationId: organisationId,
              sizeBytes: fileSize,
            );
          }

          // Add to the beginning of the list
          mediaItems.insert(0, media);

          // Select the newly uploaded media
          if (!selectedMedia.contains(media)) {
            selectedMedia.add(media);
          }

          uploadedMedia.add(media);
        } catch (e) {
          logger.e('Error uploading media file ${file.name}: $e');
          // Continue with other files even if one fails
        }
      }

      if (uploadedMedia.isNotEmpty) {
        ToastUtils.showSuccessToast(
          'Success',
          'Uploaded ${uploadedMedia.length} of ${files.length} media files',
        );
      } else {
        ToastUtils.showErrorToast('Error', 'Failed to upload any media files');
      }

      update();
      return uploadedMedia;
    } catch (e) {
      logger.e('Error in batch upload: $e');
      ToastUtils.showErrorToast(
        'Error',
        'Failed to complete batch upload: ${e.toString()}',
      );
      return uploadedMedia;
    } finally {
      isUploading.value = false;
      update();
    }
  }

  // Upload file directly
  Future<MediaModel?> uploadFile(File file) async {
    final fileName = file.path.split('/').last;
    return await uploadSingleMedia(XFile(file.path, name: fileName));
  }

  // Pick image from gallery or camera
  Future<File?> pickImage(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: source,
        imageQuality: 80,
      );

      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      logger.e('Error picking image: $e');
      ToastUtils.showErrorToast(
        'Error',
        'Failed to pick image: ${e.toString()}',
      );
      return null;
    }
  }

  // Delete media
  Future<bool> deleteMedia(String mediaId) async {
    try {
      await _mediaService.deleteMedia(mediaId);

      // Remove from lists
      mediaItems.removeWhere((item) => item.id?.toString() == mediaId);
      selectedMedia.removeWhere((item) => item.id?.toString() == mediaId);

      ToastUtils.showSuccessToast('Success', 'Media deleted successfully');

      return true;
    } catch (e) {
      logger.e('Error deleting media: $e');
      ToastUtils.showErrorToast(
        'Error',
        'Failed to delete media: ${e.toString()}',
      );
      return false;
    }
  }

  // Toggle media selection
  void toggleMediaSelection(MediaModel media, bool isMultipleSelect) {
    if (isMultipleSelect) {
      if (selectedMedia.contains(media)) {
        selectedMedia.remove(media);
      } else {
        selectedMedia.add(media);
      }
    } else {
      selectedMedia.clear();
      selectedMedia.add(media);
    }
    // Trigger UI update
    update();
  }

  // Select a single media item, clearing any previous selections
  void selectSingleMedia(MediaModel media) {
    selectedMedia.clear();
    selectedMedia.add(media);
    update();
  }

  // Delete selected media items
  Future<bool> deleteSelectedMedia() async {
    if (selectedMedia.isEmpty) return false;

    isLoading.value = true;
    update();

    try {
      // Create a copy of the selected media list to avoid modification during iteration
      final List<MediaModel> mediaToDelete = List.from(selectedMedia);
      bool allSuccessful = true;

      for (final media in mediaToDelete) {
        if (media.id != null) {
          final success = await deleteMedia(media.id.toString());
          if (!success) {
            allSuccessful = false;
          }
        }
      }

      // Clear selection after deletion
      selectedMedia.clear();
      update();

      if (allSuccessful) {
        ToastUtils.showSuccessToast(
          'Success',
          'All selected media deleted successfully',
        );
      } else {
        ToastUtils.showInfoToast(
          'Warning',
          'Some media items could not be deleted',
        );
      }

      return allSuccessful;
    } catch (e) {
      logger.e('Error deleting selected media: $e');
      ToastUtils.showErrorToast(
        'Error',
        'Failed to delete selected media: ${e.toString()}',
      );
      return false;
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // Check if media is selected
  bool isMediaSelected(MediaModel media) {
    return selectedMedia.contains(media);
  }

  // Clear all selected media
  void clearSelection() {
    selectedMedia.clear();
    update();
  }

  // Getter for mediaList to use in UI
  List<MediaModel> get mediaList => mediaItems.toList();

  // Apply filters
  void applyFilters() {
    fetchMedia(refresh: true);
  }

  // Reset filters
  void resetFilters() {
    titleController.clear();
    selectedType.value = 'all';
    selectedCategory.value = 'all';
    fetchMedia(refresh: true);
  }

  // Helper method to determine media type from file extension
  String getMediaTypeFromExtension(String fileName) {
    final fileExtension = fileName.split('.').last.toLowerCase();

    // Define file type mappings
    const imageExtensions = {'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'};
    const videoExtensions = {'mp4', 'mov', 'avi', 'wmv', 'flv', 'webm'};
    const audioExtensions = {'mp3', 'wav', 'ogg', 'aac', 'm4a'};
    const documentExtensions = {
      'pdf',
      'doc',
      'docx',
      'xls',
      'xlsx',
      'ppt',
      'pptx',
      'txt',
    };

    if (imageExtensions.contains(fileExtension)) {
      return 'image';
    } else if (videoExtensions.contains(fileExtension)) {
      return 'video';
    } else if (audioExtensions.contains(fileExtension)) {
      return 'audio';
    } else if (documentExtensions.contains(fileExtension)) {
      return 'document';
    }

    return 'image'; // Default fallback
  }

  // Helper method to get organization ID
  String? getOrganizationId() {
    try {
      final authController = Get.find<AuthController>();
      return authController.currentOrg.value?.id;
    } catch (e) {
      logger.e('Error getting organisation ID: $e');
      return null;
    }
  }

  // Helper method to get category
  String getCategory() {
    return selectedCategory.value != 'all'
        ? selectedCategory.value
        : 'uncategorized';
  }
}
