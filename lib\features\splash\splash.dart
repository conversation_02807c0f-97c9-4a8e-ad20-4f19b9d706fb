import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'dart:math';

import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/constants/routes.dart';
import 'package:onechurch/core/app/services/asset_urls.dart';

class SplashScreenView extends StatefulWidget {
  const SplashScreenView({super.key});

  @override
  State<SplashScreenView> createState() => _SplashScreenViewState();
}

class _SplashScreenViewState extends State<SplashScreenView>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  ui.Image? _logoImage;

  @override
  void initState() {
    super.initState();

    _controller =
        AnimationController(vsync: this, duration: const Duration(seconds: 3))
          ..addListener(() {
            setState(() {});
          })
          ..forward().whenComplete(() {
            context.go(Routes.HOME);
          });

    _loadLogoImage();
  }

  Future<void> _loadLogoImage() async {
    final data = await DefaultAssetBundle.of(context).load(AssetUrls.logo2);
    final bytes = data.buffer.asUint8List();
    final image = await decodeImageFromList(bytes);
    setState(() {
      _logoImage = image;
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Center(
          child: Stack(
            alignment: Alignment.center,
            children: [
              if (_logoImage != null)
                SizedBox(
                  width: 400,
                  height: 400,
                  child: CustomPaint(
                    painter: CircularProgressPainter(
                      progress: _controller.value,
                      logoImage: _logoImage!,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class CircularProgressPainter extends CustomPainter {
  final double progress;
  final ui.Image logoImage;

  CircularProgressPainter({required this.progress, required this.logoImage});

  @override
  void paint(Canvas canvas, Size size) {
    final strokeWidth = 8.0;
    final radius = (size.width / 2) - strokeWidth / 2;

    final color = Colors.blue;

    final paint =
        Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = strokeWidth
          ..strokeCap = StrokeCap.round
          ..color = color;

    // Calculate the sweep angle
    final sweepAngle = progress * 2 * pi;

    // Draw the arc
    canvas.drawArc(
      Rect.fromCircle(
        center: Offset(size.width / 2, size.height / 2),
        radius: radius,
      ),
      -pi / 2, // Start angle
      sweepAngle, // Sweep angle
      false,
      paint,
    );

    // Draw the logo at the center
    final logoSize = size.width * 0.3; // Adjust logo size as needed
    final logoOffset = Offset(
      (size.width - logoSize) / 2,
      (size.height - logoSize) / 2,
    );

    canvas.drawImageRect(
      logoImage,
      Rect.fromLTWH(
        0,
        0,
        logoImage.width.toDouble(),
        logoImage.height.toDouble(),
      ),
      Rect.fromLTWH(logoOffset.dx, logoOffset.dy, logoSize, logoSize),
      Paint(),
    );
  }

  @override
  bool shouldRepaint(CircularProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.logoImage != logoImage;
  }
}
