import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../auth/controllers/auth_controller.dart';
import '../models/inventory_item_model.dart';
import '../models/inventory_category_model.dart';
import '../models/unit_of_measure_model.dart';
import '../services/inventory_item_service.dart';

class InventoryItemController extends GetxController {
  final InventoryItemService _inventoryItemService = InventoryItemService();
  final Logger logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<InventoryItemModel> inventoryItems = <InventoryItemModel>[].obs;
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalItems = 0.obs;
  final RxInt pageSize = 10.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;

  // Filter variables
  final RxString searchQuery = ''.obs;
  final RxString categoryFilter = ''.obs;
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  // Controllers
  final TextEditingController searchController = TextEditingController();

  // Dropdown options
  final RxList<InventoryCategory> categories = <InventoryCategory>[].obs;
  final RxList<String> conditions = <String>[].obs;
  final RxList<String> inventoryTypes = <String>[].obs;
  final RxList<UnitOfMeasureModel> unitsOfMeasure = <UnitOfMeasureModel>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initializeDropdownOptions();
    fetchInventoryItems();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Initialize dropdown options
  void _initializeDropdownOptions() {
    _loadCategories();
    conditions.value = _inventoryItemService.getInventoryConditions();
    inventoryTypes.value = _inventoryItemService.getInventoryTypes();
    _loadUnitsOfMeasure();
  }

  // Load units of measure from enums or fallback to common units
  void _loadUnitsOfMeasure() {
    unitsOfMeasure.value = CommonUnitsOfMeasure.getUnitsFromEnums();
  }

  // Load categories
  Future<void> _loadCategories() async {
    try {
      categories.value = await _inventoryItemService.getInventoryCategories();
    } catch (e) {
      logger.e('Error loading categories: $e');
    }
  }

  // Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    currentPage.value = 0;
  }

  // Set category filter
  void setCategoryFilter(String category) {
    categoryFilter.value = category;
    currentPage.value = 0;
  }

  // Set date range
  void setDateRange(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0;
  }

  // Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    categoryFilter.value = '';
    startDate.value = null;
    endDate.value = null;
    searchController.clear();
    currentPage.value = 0;
    fetchInventoryItems();
  }

  // Fetch inventory items with pagination and filters
  Future<void> fetchInventoryItems() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _inventoryItemService.fetchInventoryItems(
        page: currentPage.value,
        size: pageSize.value,
        searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
        category: categoryFilter.value.isEmpty ? null : categoryFilter.value,
        startDate: startDate.value,
        endDate: endDate.value,
        organisationId: Get.find<AuthController>().currentOrg.value?.id,
      );

      if (response["status"] ?? false) {
        final data = response["data"];
        logger.d('Raw API response data: $data');

        // Handle the specific API response structure
        // Expected: { "data": { "data": { "items": [...], "page": 0, ... } } }
        if (data != null && data is Map) {
          final innerData = data["data"];

          if (innerData != null && innerData is Map) {
            // Check if items are under "items" key
            if (innerData.containsKey("items")) {
              final itemsList = innerData["items"] as List;
              inventoryItems.value =
                  itemsList
                      .map((item) => InventoryItemModel.fromJson(item))
                      .toList();

              logger.d('Loaded ${inventoryItems.length} inventory items');

              // Update pagination info
              totalPages.value = innerData["total_pages"] ?? 1;
              totalItems.value = innerData["total"] ?? itemsList.length;
              isLastPage.value = innerData["last"] ?? true;
              isFirstPage.value = innerData["first"] ?? true;
            } else if (innerData is List) {
              // Fallback: if innerData is directly a list
              inventoryItems.value =
                  (innerData as List)
                      .map((item) => InventoryItemModel.fromJson(item))
                      .toList();
            }
          } else if (data is List) {
            // Fallback: if data is directly a list
            inventoryItems.value =
                (data as List)
                    .map((item) => InventoryItemModel.fromJson(item))
                    .toList();
          }
        }
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to fetch inventory items';
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to fetch inventory items: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
  }

  // Get inventory item by ID
  Future<InventoryItemModel?> getInventoryItemById(String id) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _inventoryItemService.getInventoryItemById(id);

      if (response["status"] ?? false) {
        final data = response["data"];
        return InventoryItemModel.fromJson(data);
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to fetch inventory item';
        return null;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to fetch inventory item: ${e.toString()}';
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // Create new inventory item
  Future<bool> createInventoryItem({
    required String title,
    required String categoryId,
    required String unitOfMeasureId,
    String? barcode,
    String? description,
    List<Map<String, dynamic>>? media,
  }) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _inventoryItemService.createInventoryItem(
        title: title,
        categoryId: categoryId,
        unitOfMeasureId: unitOfMeasureId,
        barcode: barcode,
        description: description,
        organisationId: Get.find<AuthController>().currentOrg.value?.id ?? '',
        media: media,
      );

      if (response["status"] ?? false) {
        await fetchInventoryItems(); // Refresh the list
        return true;
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to create inventory item';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to create inventory item: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Update inventory item
  Future<bool> updateInventoryItem({
    required String id,
    required String title,
    required String category,
    required String unitOfMeasureId,
    String? barcode,
    String? description,
    String? status,
    List<Map<String, dynamic>>? media,
  }) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _inventoryItemService.updateInventoryItem(
        id: id,
        title: title,
        category: category,
        unitOfMeasureId: unitOfMeasureId,
        barcode: barcode,
        description: description,
        organisationId: Get.find<AuthController>().currentOrg.value?.id ?? '',
        status: status,
        media: media,
      );

      if (response["status"] ?? false) {
        await fetchInventoryItems(); // Refresh the list
        return true;
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to update inventory item';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to update inventory item: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Delete inventory item
  Future<bool> deleteInventoryItem(String id) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _inventoryItemService.deleteInventoryItem(id);

      if (response["status"] ?? false) {
        await fetchInventoryItems(); // Refresh the list
        return true;
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to delete inventory item';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to delete inventory item: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Pagination methods
  void nextPage() {
    if (!isLastPage.value) {
      currentPage.value++;
      fetchInventoryItems();
    }
  }

  void previousPage() {
    if (!isFirstPage.value) {
      currentPage.value--;
      fetchInventoryItems();
    }
  }

  void goToPage(int page) {
    if (page >= 0 && page < totalPages.value) {
      currentPage.value = page;
      fetchInventoryItems();
    }
  }

  // Helper methods for dropdowns
  List<String> get categoryOptions =>
      categories.map((cat) => cat.title ?? '').toList();
  List<String> get conditionOptions => conditions.toList();
  List<String> get inventoryTypeOptions => inventoryTypes.toList();
  List<UnitOfMeasureModel> get unitOfMeasureOptions => unitsOfMeasure.toList();

  // Get unit of measure by ID
  UnitOfMeasureModel? getUnitOfMeasureById(String id) {
    try {
      return unitsOfMeasure.firstWhere((unit) => unit.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get category display name
  String getCategoryDisplayName(String? category) {
    return category ?? 'Unknown Category';
  }
}
