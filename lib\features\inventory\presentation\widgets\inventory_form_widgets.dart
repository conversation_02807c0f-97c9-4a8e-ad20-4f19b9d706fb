import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import '../../../../core/app/widgets/custom_text_field.dart';
import '../../../../core/app/widgets/custom_textformfield.dart';
import '../../models/unit_of_measure_model.dart';

class InventoryDropdownField extends StatelessWidget {
  final String labelText;
  final String? value;
  final List<String> items;
  final Function(String?) onChanged;
  final String? hintText;
  final bool isRequired;

  const InventoryDropdownField({
    super.key,
    required this.labelText,
    required this.value,
    required this.items,
    required this.onChanged,
    this.hintText,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 16,
        ),
      ),
      items:
          items
              .map(
                (item) =>
                    DropdownMenuItem<String>(value: item, child: Text(item)),
              )
              .toList(),
      onChanged: onChanged,
      validator:
          isRequired
              ? (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select $labelText';
                }
                return null;
              }
              : null,
    );
  }
}

class UnitOfMeasureDropdownField extends StatelessWidget {
  final String labelText;
  final String? value;
  final List<UnitOfMeasureModel> units;
  final Function(String?) onChanged;
  final String? hintText;
  final bool isRequired;

  const UnitOfMeasureDropdownField({
    super.key,
    required this.labelText,
    required this.value,
    required this.units,
    required this.onChanged,
    this.hintText,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 16,
        ),
      ),
      items:
          units
              .map(
                (unit) => DropdownMenuItem<String>(
                  value: unit.id,
                  child: Text(unit.fullDisplay),
                ),
              )
              .toList(),
      onChanged: onChanged,
      validator:
          isRequired
              ? (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select $labelText';
                }
                return null;
              }
              : null,
    );
  }
}

class InventoryDateField extends StatelessWidget {
  final String labelText;
  final DateTime? value;
  final Function(DateTime?) onChanged;
  final String? hintText;
  final bool isRequired;
  final DateTime? firstDate;
  final DateTime? lastDate;

  const InventoryDateField({
    super.key,
    required this.labelText,
    required this.value,
    required this.onChanged,
    this.hintText,
    this.isRequired = false,
    this.firstDate,
    this.lastDate,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      controller: TextEditingController(
        text:
            value != null
                ? '${value!.year}-${value!.month.toString().padLeft(2, '0')}-${value!.day.toString().padLeft(2, '0')}'
                : '',
      ),
      labelText: labelText,
      hintText: hintText ?? 'Select date',
      readOnly: true,
      prefixIcon: const Icon(IconlyLight.calendar),
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: value ?? DateTime.now(),
          firstDate: firstDate ?? DateTime(2020),
          lastDate:
              lastDate ?? DateTime.now().add(const Duration(days: 365 * 5)),
        );

        if (date != null) {
          onChanged(date);
        }
      },
      validator:
          isRequired
              ? (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select $labelText';
                }
                return null;
              }
              : null,
    );
  }
}

class InventoryNumberField extends StatelessWidget {
  final String labelText;
  final TextEditingController controller;
  final String? hintText;
  final bool isRequired;
  final bool isDecimal;
  final String? prefix;
  final String? suffix;

  const InventoryNumberField({
    super.key,
    required this.labelText,
    required this.controller,
    this.hintText,
    this.isRequired = false,
    this.isDecimal = false,
    this.prefix,
    this.suffix,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextFormField(
      controller: controller,
      labelText: labelText,
      hintText: hintText,
      keyboardType:
          isDecimal
              ? const TextInputType.numberWithOptions(decimal: true)
              : TextInputType.number,
      validator:
          isRequired
              ? (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter $labelText';
                }
                if (isDecimal) {
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                } else {
                  if (int.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                }
                return null;
              }
              : (value) {
                if (value != null && value.isNotEmpty) {
                  if (isDecimal) {
                    if (double.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                  } else {
                    if (int.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                  }
                }
                return null;
              },
    );
  }
}

class InventoryTextArea extends StatelessWidget {
  final String labelText;
  final TextEditingController controller;
  final String? hintText;
  final bool isRequired;
  final int maxLines;

  const InventoryTextArea({
    super.key,
    required this.labelText,
    required this.controller,
    this.hintText,
    this.isRequired = false,
    this.maxLines = 3,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextFormField(
      controller: controller,
      labelText: labelText,
      hintText: hintText,
      maxLines: maxLines,
      validator:
          isRequired
              ? (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter $labelText';
                }
                return null;
              }
              : null,
    );
  }
}

class InventoryCheckboxField extends StatelessWidget {
  final String labelText;
  final bool value;
  final Function(bool?) onChanged;
  final String? subtitle;

  const InventoryCheckboxField({
    super.key,
    required this.labelText,
    required this.value,
    required this.onChanged,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return CheckboxListTile(
      title: Text(labelText, style: Theme.of(context).textTheme.bodyMedium),
      subtitle:
          subtitle != null
              ? Text(
                subtitle!,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
              )
              : null,
      value: value,
      onChanged: onChanged,
      controlAffinity: ListTileControlAffinity.leading,
      contentPadding: EdgeInsets.zero,
    );
  }
}

class InventoryFormSection extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final IconData? icon;

  const InventoryFormSection({
    super.key,
    required this.title,
    required this.children,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (icon != null) ...[
                  Icon(icon, size: 20, color: Theme.of(context).primaryColor),
                  const SizedBox(width: 8),
                ],
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }
}
