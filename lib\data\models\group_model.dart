import 'dart:convert';

import '../../features/media_upload/models/media_model.dart';

class GroupModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final String? description;
  final String? status;
  final String? organisationId;
  final int? createdByUserId;
  final CreatedByUser? createdByUser;
  final List<MemberElement>? members;
  final List<MediaModel>? media;

  GroupModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.description,
    this.status,
    this.organisationId,
    this.createdByUserId,
    this.createdByUser,
    this.members,
    this.media,
  });

  GroupModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? title,
    String? description,
    String? status,
    String? organisationId,
    int? createdByUserId,
    CreatedByUser? createdByUser,
    List<MemberElement>? members,
    List<MediaModel>? media,
  }) => GroupModel(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    title: title ?? this.title,
    description: description ?? this.description,
    status: status ?? this.status,
    organisationId: organisationId ?? this.organisationId,
    createdByUserId: createdByUserId ?? this.createdByUserId,
    createdByUser: createdByUser ?? this.createdByUser,
    members: members ?? this.members,
    media: media ?? this.media,
  );

  factory GroupModel.fromRawJson(String str) =>
      GroupModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory GroupModel.fromJson(Map<String, dynamic> json) => GroupModel(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    title: json["title"],
    description: json["description"],
    status: json["status"],
    organisationId: json["organisation_id"],
    createdByUserId: json["created_by_user_id"],
    createdByUser:
        json["created_by_user"] == null
            ? null
            : CreatedByUser.fromJson(json["created_by_user"]),
    members:
        json["members"] == null
            ? []
            : List<MemberElement>.from(
              json["members"]!.map((x) => MemberElement.fromJson(x)),
            ),
    media: json["media"] == null ? [] : List<MediaModel>.from(
              json["media"]!.map((x) => MediaModel.fromJson(x)),
            ),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "title": title,
    "description": description,
    "status": status,
    "organisation_id": organisationId,
    "created_by_user_id": createdByUserId,
    "created_by_user": createdByUser?.toJson(),
    "members":
        members == null
            ? []
            : List<dynamic>.from(members!.map((x) => x.toJson())),
    "media": media,
  };
}

class CreatedByUser {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? phoneNumber;
  final String? firstName;
  final String? secondName;
  final String? email;
  final String? idNumber;
  final int? balance;
  final String? birthDate;
  final String? countryCode;
  final String? county;
  final String? subCounty;
  final String? ward;
  final String? secondaryNumber;
  final String? profileUrl;
  final String? role;
  final String? status;
  final dynamic addresses;
  final dynamic devices;

  CreatedByUser({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.phoneNumber,
    this.firstName,
    this.secondName,
    this.email,
    this.idNumber,
    this.balance,
    this.birthDate,
    this.countryCode,
    this.county,
    this.subCounty,
    this.ward,
    this.secondaryNumber,
    this.profileUrl,
    this.role,
    this.status,
    this.addresses,
    this.devices,
  });

  CreatedByUser copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? phoneNumber,
    String? firstName,
    String? secondName,
    String? email,
    String? idNumber,
    int? balance,
    String? birthDate,
    String? countryCode,
    String? county,
    String? subCounty,
    String? ward,
    String? secondaryNumber,
    String? profileUrl,
    String? role,
    String? status,
    dynamic addresses,
    dynamic devices,
  }) => CreatedByUser(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    firstName: firstName ?? this.firstName,
    secondName: secondName ?? this.secondName,
    email: email ?? this.email,
    idNumber: idNumber ?? this.idNumber,
    balance: balance ?? this.balance,
    birthDate: birthDate ?? this.birthDate,
    countryCode: countryCode ?? this.countryCode,
    county: county ?? this.county,
    subCounty: subCounty ?? this.subCounty,
    ward: ward ?? this.ward,
    secondaryNumber: secondaryNumber ?? this.secondaryNumber,
    profileUrl: profileUrl ?? this.profileUrl,
    role: role ?? this.role,
    status: status ?? this.status,
    addresses: addresses ?? this.addresses,
    devices: devices ?? this.devices,
  );

  factory CreatedByUser.fromRawJson(String str) =>
      CreatedByUser.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CreatedByUser.fromJson(Map<String, dynamic> json) => CreatedByUser(
    id: json["ID"],
    createdAt:
        json["CreatedAt"] == null ? null : DateTime.parse(json["CreatedAt"]),
    updatedAt:
        json["UpdatedAt"] == null ? null : DateTime.parse(json["UpdatedAt"]),
    deletedAt: json["DeletedAt"],
    phoneNumber: json["phone_number"],
    firstName: json["first_name"],
    secondName: json["second_name"],
    email: json["email"],
    idNumber: json["id_number"],
    balance: json["balance"],
    birthDate: json["birth_date"],
    countryCode: json["country_code"],
    county: json["county"],
    subCounty: json["sub_county"],
    ward: json["ward"],
    secondaryNumber: json["secondary_number"],
    profileUrl: json["profile_url"],
    role: json["role"],
    status: json["status"],
    addresses: json["addresses"],
    devices: json["devices"],
  );

  Map<String, dynamic> toJson() => {
    "ID": id,
    "CreatedAt": createdAt?.toIso8601String(),
    "UpdatedAt": updatedAt?.toIso8601String(),
    "DeletedAt": deletedAt,
    "phone_number": phoneNumber,
    "first_name": firstName,
    "second_name": secondName,
    "email": email,
    "id_number": idNumber,
    "balance": balance,
    "birth_date": birthDate,
    "country_code": countryCode,
    "county": county,
    "sub_county": subCounty,
    "ward": ward,
    "secondary_number": secondaryNumber,
    "profile_url": profileUrl,
    "role": role,
    "status": status,
    "addresses": addresses,
    "devices": devices,
  };
}

class MemberElement {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? groupId;
  final dynamic group;
  final String? memberId;
  final MemberMember? member;
  final String? status;
  final String? role;

  MemberElement({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.groupId,
    this.group,
    this.memberId,
    this.member,
    this.status,
    this.role,
  });

  MemberElement copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? groupId,
    dynamic group,
    String? memberId,
    MemberMember? member,
    String? status,
    String? role,
  }) => MemberElement(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    groupId: groupId ?? this.groupId,
    group: group ?? this.group,
    memberId: memberId ?? this.memberId,
    member: member ?? this.member,
    status: status ?? this.status,
    role: role ?? this.role,
  );

  factory MemberElement.fromRawJson(String str) =>
      MemberElement.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MemberElement.fromJson(Map<String, dynamic> json) => MemberElement(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    groupId: json["group_id"],
    group: json["group"],
    memberId: json["member_id"],
    member:
        json["member"] == null ? null : MemberMember.fromJson(json["member"]),
    status: json["status"],
    role: json["role"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "group_id": groupId,
    "group": group,
    "member_id": memberId,
    "member": member?.toJson(),
    "status": status,
    "role": role,
  };
}

class MemberMember {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? phoneNumber;
  final String? secondaryNumber;
  final String? internalAccountNumber;
  final String? profileUrl;
  final String? firstName;
  final String? secondName;
  final String? email;
  final String? address;
  final dynamic userId;
  final String? idNumber;
  final int? balance;
  final String? organisationId;
  final String? verificationStatus;
  final String? status;
  final dynamic memberCategoryId;

  MemberMember({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.phoneNumber,
    this.secondaryNumber,
    this.internalAccountNumber,
    this.profileUrl,
    this.firstName,
    this.secondName,
    this.email,
    this.address,
    this.userId,
    this.idNumber,
    this.balance,
    this.organisationId,
    this.verificationStatus,
    this.status,
    this.memberCategoryId,
  });

  MemberMember copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? phoneNumber,
    String? secondaryNumber,
    String? internalAccountNumber,
    String? profileUrl,
    String? firstName,
    String? secondName,
    String? email,
    String? address,
    dynamic userId,
    String? idNumber,
    int? balance,
    String? organisationId,
    String? verificationStatus,
    String? status,
    dynamic memberCategoryId,
  }) => MemberMember(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    secondaryNumber: secondaryNumber ?? this.secondaryNumber,
    internalAccountNumber: internalAccountNumber ?? this.internalAccountNumber,
    profileUrl: profileUrl ?? this.profileUrl,
    firstName: firstName ?? this.firstName,
    secondName: secondName ?? this.secondName,
    email: email ?? this.email,
    address: address ?? this.address,
    userId: userId ?? this.userId,
    idNumber: idNumber ?? this.idNumber,
    balance: balance ?? this.balance,
    organisationId: organisationId ?? this.organisationId,
    verificationStatus: verificationStatus ?? this.verificationStatus,
    status: status ?? this.status,
    memberCategoryId: memberCategoryId ?? this.memberCategoryId,
  );

  factory MemberMember.fromRawJson(String str) =>
      MemberMember.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MemberMember.fromJson(Map<String, dynamic> json) => MemberMember(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    phoneNumber: json["phone_number"],
    secondaryNumber: json["secondary_number"],
    internalAccountNumber: json["internal_account_number"],
    profileUrl: json["profile_url"],
    firstName: json["first_name"],
    secondName: json["second_name"],
    email: json["email"],
    address: json["address"],
    userId: json["user_id"],
    idNumber: json["id_number"],
    balance: json["balance"],
    organisationId: json["organisation_id"],
    verificationStatus: json["verification_status"],
    status: json["status"],
    memberCategoryId: json["member_category_id"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "phone_number": phoneNumber,
    "secondary_number": secondaryNumber,
    "internal_account_number": internalAccountNumber,
    "profile_url": profileUrl,
    "first_name": firstName,
    "second_name": secondName,
    "email": email,
    "address": address,
    "user_id": userId,
    "id_number": idNumber,
    "balance": balance,
    "organisation_id": organisationId,
    "verification_status": verificationStatus,
    "status": status,
    "member_category_id": memberCategoryId,
  };
}
