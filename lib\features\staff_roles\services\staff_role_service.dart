import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../models/staff_role_model.dart';
import '../models/permission_model.dart';
import '../models/staff_role_assignment_model.dart';

class StaffRoleService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();

  // Initialize the service
  StaffRoleService() {
    _httpService.initializeDio();
  }

  // Fetch all staff roles
  Future<List<StaffRoleModel>> fetchStaffRoles() async {
    try {
      final response = await _httpService.request(
        url: ApiUrls.getStaffRoles,
        method: Method.GET,
      );

      logger.d('API Response for staff roles: ${response.data}');

      if (response.data['status'] == true && response.data['data'] != null) {
        final List<dynamic> rolesData = response.data['data'];
        logger.d('Roles data length: ${rolesData.length}');

        // Log the first role to see its structure
        if (rolesData.isNotEmpty) {
          logger.d('First role data: ${rolesData.first}');
        }

        final roles =
            rolesData.map((role) => StaffRoleModel.fromJson(role)).toList();
        logger.d('Parsed ${roles.length} staff roles');

        // Log the first parsed role
        if (roles.isNotEmpty) {
          logger.d(
            'First parsed role: Name=${roles.first.name}, ID=${roles.first.id}, Permissions=${roles.first.permissions?.length ?? 0}',
          );
        }

        return roles;
      } else {
        logger.e('API returned status=false: ${response.data['message']}');
        throw Exception(
          'Failed to fetch staff roles: ${response.data['message']}',
        );
      }
    } catch (e) {
      logger.e('Error fetching staff roles: $e');
      rethrow;
    }
  }

  // Create a new staff role
  Future<StaffRoleModel> createStaffRole({
    required String name,
    required String description,
    required String? organisationId,
    required List<Map<String, dynamic>> permissions,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'name': name,
        'description': description,
        'organisation_id': organisationId,
        'permissions': permissions,
      };

      final response = await _httpService.request(
        url: ApiUrls.createStaffRole,
        method: Method.POST,
        params: data,
      );

      if (response.data['status'] == true && response.data['data'] != null) {
        return StaffRoleModel.fromJson(response.data['data']);
      } else {
        throw Exception(
          'Failed to create staff role: ${response.data['message']}',
        );
      }
    } catch (e) {
      logger.e('Error creating staff role: $e');
      rethrow;
    }
  }

  // Update an existing staff role
  Future<StaffRoleModel> updateStaffRole({
    required String id,
    required String name,
    required String description,
    required String? organisationId,
    required List<Map<String, dynamic>> permissions,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'name': name,
        'description': description,
        'organisation_id': organisationId,
        'permissions': permissions,
      };

      final response = await _httpService.request(
        url: '${ApiUrls.updateStaffRole}$id/',
        method: Method.PUT,
        params: data,
      );

      if (response.data['status'] == true && response.data['data'] != null) {
        return StaffRoleModel.fromJson(response.data['data']);
      } else {
        throw Exception(
          'Failed to update staff role: ${response.data['message']}',
        );
      }
    } catch (e) {
      logger.e('Error updating staff role: $e');
      rethrow;
    }
  }

  // Delete a staff role
  Future<bool> deleteStaffRole(String id) async {
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.deleteStaffRole}$id/',
        method: Method.DELETE,
      );

      if (response.data['status'] == true) {
        return true;
      } else {
        throw Exception(
          'Failed to delete staff role: ${response.data['message']}',
        );
      }
    } catch (e) {
      logger.e('Error deleting staff role: $e');
      rethrow;
    }
  }

  // Add permission to role
  Future<bool> addPermissionToRole({
    required String roleId,
    required String permissionId,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'role_id': roleId,
        'permission_id': permissionId,
      };

      final response = await _httpService.request(
        url: ApiUrls.addPermissionToRole,
        method: Method.POST,
        params: data,
      );

      if (response.data['status'] == true) {
        return true;
      } else {
        throw Exception(
          'Failed to add permission to role: ${response.data['message']}',
        );
      }
    } catch (e) {
      logger.e('Error adding permission to role: $e');
      rethrow;
    }
  }

  // Remove permission from role
  Future<bool> removePermissionFromRole({
    required String roleId,
    required String permissionId,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'role_id': roleId,
        'permission_id': permissionId,
      };

      final response = await _httpService.request(
        url: ApiUrls.removePermissionToRole,
        method: Method.POST,
        params: data,
      );

      if (response.data['status'] == true) {
        return true;
      } else {
        throw Exception(
          'Failed to remove permission from role: ${response.data['message']}',
        );
      }
    } catch (e) {
      logger.e('Error removing permission from role: $e');
      rethrow;
    }
  }

  // Assign role to staff
  Future<StaffRoleAssignmentModel> assignRoleToStaff({
    required String roleId,
    required String staffId,
    required DateTime assignedAt,
    required String description,
    DateTime? expiresAt,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'role_id': roleId,
        'staff_id': staffId,
        'assigned_at': assignedAt.toUtc().toIso8601String(),
        'description': description,
      };

      if (expiresAt != null) {
        data['expires_at'] = expiresAt.toUtc().toIso8601String();
      }

      final response = await _httpService.request(
        url: ApiUrls.addStaffToRole,
        method: Method.POST,
        params: data,
      );

      if (response.data['status'] == true && response.data['data'] != null) {
        return StaffRoleAssignmentModel.fromJson(response.data['data']);
      } else {
        throw Exception(
          'Failed to assign role to staff: ${response.data['message']}',
        );
      }
    } catch (e) {
      logger.e('Error assigning role to staff: $e');
      rethrow;
    }
  }

  // Get staff role assignments
  Future<List<StaffRoleAssignmentModel>> getStaffRoleAssignments(
    String staffId,
  ) async {
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.getStaffRoleAssignment}?staff_id=$staffId',
        method: Method.GET,
      );

      if (response.data['status'] == true && response.data['data'] != null) {
        final List<dynamic> assignmentsData = response.data['data'];
        return assignmentsData
            .map((assignment) => StaffRoleAssignmentModel.fromJson(assignment))
            .toList();
      } else {
        throw Exception(
          'Failed to get staff role assignments: ${response.data['message']}',
        );
      }
    } catch (e) {
      logger.e('Error getting staff role assignments: $e');
      rethrow;
    }
  }

  // Update staff role assignment
  Future<StaffRoleAssignmentModel> updateStaffRoleAssignment({
    required String id,
    required String staffId,
    required String roleId,
    required DateTime assignedAt,
    required String description,
    required bool isActive,
    DateTime? expiresAt,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'id': id,
        'staff_id': staffId,
        'role_id': roleId,
        'assigned_at': assignedAt.toUtc().toIso8601String(),
        'description': description,
        'is_active': isActive,
      };

      if (expiresAt != null) {
        data['expires_at'] = expiresAt.toUtc().toIso8601String();
      }

      final response = await _httpService.request(
        url: ApiUrls.updateStaffToRole,
        method: Method.PUT,
        params: data,
      );

      if (response.data['status'] == true && response.data['data'] != null) {
        return StaffRoleAssignmentModel.fromJson(response.data['data']);
      } else {
        throw Exception(
          'Failed to update staff role assignment: ${response.data['message']}',
        );
      }
    } catch (e) {
      logger.e('Error updating staff role assignment: $e');
      rethrow;
    }
  }

  // Get all permissions
  Future<List<PermissionModel>> getAllPermissions() async {
    try {
      final response = await _httpService.request(
        url: ApiUrls.getAllPermissions,
        method: Method.GET,
      );

      if (response.data['status'] == true && response.data['data'] != null) {
        final List<dynamic> permissionsData = response.data['data'];
        return permissionsData
            .map((permission) => PermissionModel.fromJson(permission))
            .toList();
      } else {
        throw Exception(
          'Failed to get all permissions: ${response.data['message']}',
        );
      }
    } catch (e) {
      logger.e('Error getting all permissions: $e');
      rethrow;
    }
  }
}
