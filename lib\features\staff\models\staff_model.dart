import 'dart:convert';

class StaffModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? phoneNumber;
  final String? firstName;
  final String? secondName;
  final String? code;
  final String? idNumber;
  final String? email;
  final String? status;
  final dynamic organisation;
  final String? organisationId;
  final dynamic assignedLocations;
  final dynamic member;
  final dynamic memberId;
  final dynamic user;
  final int? userId;
  final bool? isMaker;
  final bool? isChecker;
  final bool? isSignatory;
  final List<dynamic>? roleAssignments;

  StaffModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.phoneNumber,
    this.firstName,
    this.secondName,
    this.code,
    this.idNumber,
    this.email,
    this.status,
    this.organisation,
    this.organisationId,
    this.assignedLocations,
    this.member,
    this.memberId,
    this.user,
    this.userId,
    this.isMaker,
    this.isChecker,
    this.isSignatory,
    this.roleAssignments,
  });

  StaffModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? phoneNumber,
    String? firstName,
    String? secondName,
    String? code,
    String? idNumber,
    String? email,
    String? status,
    dynamic organisation,
    String? organisationId,
    dynamic assignedLocations,
    dynamic member,
    dynamic memberId,
    dynamic user,
    int? userId,
    bool? isMaker,
    bool? isChecker,
    bool? isSignatory,
    List<dynamic>? roleAssignments,
  }) => StaffModel(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    firstName: firstName ?? this.firstName,
    secondName: secondName ?? this.secondName,
    code: code ?? this.code,
    idNumber: idNumber ?? this.idNumber,
    email: email ?? this.email,
    status: status ?? this.status,
    organisation: organisation ?? this.organisation,
    organisationId: organisationId ?? this.organisationId,
    assignedLocations: assignedLocations ?? this.assignedLocations,
    member: member ?? this.member,
    memberId: memberId ?? this.memberId,
    user: user ?? this.user,
    userId: userId ?? this.userId,
    isMaker: isMaker ?? this.isMaker,
    isChecker: isChecker ?? this.isChecker,
    isSignatory: isSignatory ?? this.isSignatory,
    roleAssignments: roleAssignments ?? this.roleAssignments,
  );

  factory StaffModel.fromRawJson(String str) =>
      StaffModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StaffModel.fromJson(Map<String, dynamic> json) => StaffModel(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    phoneNumber: json["phone_number"],
    firstName: json["first_name"],
    secondName: json["second_name"],
    code: json["code"],
    idNumber: json["id_number"],
    email: json["email"],
    status: json["status"],
    organisation: json["organisation"],
    organisationId: json["organisation_id"],
    assignedLocations: json["assigned_locations"],
    member: json["member"],
    memberId: json["member_id"],
    user: json["user"],
    userId: json["user_id"],
    isMaker: json["is_maker"],
    isChecker: json["is_checker"],
    isSignatory: json["is_signatory"],
    roleAssignments:
        json["role_assignments"] == null
            ? []
            : List<dynamic>.from(json["role_assignments"]!.map((x) => x)),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "phone_number": phoneNumber,
    "first_name": firstName,
    "second_name": secondName,
    "code": code,
    "id_number": idNumber,
    "email": email,
    "status": status,
    "organisation": organisation,
    "organisation_id": organisationId,
    "assigned_locations": assignedLocations,
    "member": member,
    "member_id": memberId,
    "user": user,
    "user_id": userId,
    "is_maker": isMaker,
    "is_checker": isChecker,
    "is_signatory": isSignatory,
    "role_assignments":
        roleAssignments == null
            ? []
            : List<dynamic>.from(roleAssignments!.map((x) => x)),
  };
}
