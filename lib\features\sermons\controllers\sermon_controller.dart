import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import 'package:onechurch/features/media_upload/models/media_model.dart';
import '../../../data/models/sermon_model.dart';
import '../services/sermon_service.dart';

class SermonController extends GetxController {
  final SermonService _sermonService = SermonService();
  final logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<SermonModel> sermons = <SermonModel>[].obs;
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalItems = 0.obs;
  final RxInt pageSize = 10.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;
  final RxString searchQuery = ''.obs;
  final RxString statusFilter = ''.obs;
  final TextEditingController searchController = TextEditingController();

  // Date filter variables
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  // Form controllers for create/edit
  final TextEditingController titleFormController = TextEditingController();
  final TextEditingController descriptionFormController =
      TextEditingController();
  final TextEditingController categoryController = TextEditingController();
  final RxList<MediaModel> mediaItems = <MediaModel>[].obs;
  final TextEditingController mediaTitleController = TextEditingController();
  final TextEditingController mediaUrlController = TextEditingController();
  final RxString mediaTypeValue = 'IMAGE'.obs;
  final RxString statusFormValue = 'active'.obs;
  final RxBool isSubmitting = false.obs;

  // Categories list
  final categories = <String>[].obs;

  // Add a reactive variable for editing description
  final RxString editingDescription = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchSermons();
  }

  @override
  void onClose() {
    searchController.dispose();
    titleFormController.dispose();
    descriptionFormController.dispose();
    categoryController.dispose();
    mediaTitleController.dispose();
    mediaUrlController.dispose();
    super.onClose();
  }

  // Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    currentPage.value = 0; // Reset to first page when searching
    fetchSermons();
  }

  // Set date filters
  void setDateFilters(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0; // Reset to first page when filtering
    fetchSermons();
  }

  // Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    searchController.clear();
    statusFilter.value = '';
    startDate.value = null;
    endDate.value = null;
    currentPage.value = 0;
    fetchSermons();
  }

  // Navigate to next page
  void nextPage() {
    if (!isLastPage.value) {
      currentPage.value++;
      fetchSermons();
    }
  }

  // Navigate to previous page
  void previousPage() {
    if (currentPage.value > 0) {
      currentPage.value--;
      fetchSermons();
    }
  }

  // Fetch sermons with pagination and filters
  Future<void> fetchSermons() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _sermonService.fetchSermons(
        page: currentPage.value,
        size: pageSize.value,
        searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
        status: statusFilter.value.isEmpty ? null : statusFilter.value,
        startDate: startDate.value,
        endDate: endDate.value,
        organisationId: Get.find<AuthController>().currentOrg.value?.id,
      );

      if (response["status"] ?? false) {
        final data = response["data"];

        // The API response has a nested data structure
        final innerData = data["data"];

        if (innerData != null) {
          // Parse pagination data
          currentPage.value = innerData["page"] ?? 0;
          pageSize.value = innerData["size"] ?? 10;
          totalPages.value = innerData["total_pages"] ?? 0;
          totalItems.value = innerData["total"] ?? 0;
          isLastPage.value = innerData["last"] ?? false;
          isFirstPage.value = innerData["first"] ?? true;

          // Parse sermon items
          final List<dynamic> items = innerData["items"] ?? [];
          logger.d('Sermon items found: ${items.length}');
          sermons.value = _sermonService.parseSermons(items);
          sermons.refresh();

          // Set categories if available
          if (data["categories"] != null) {
            categories.value = List<String>.from(data["categories"]);
          }
        } else {
          logger.e('Inner data structure is null');
          errorMessage.value = 'Invalid data structure in response';
        }
      } else {
        errorMessage.value = response["message"] ?? 'Failed to fetch sermons';
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to fetch sermons: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
    update();
  }

  // Refresh sermons
  Future<void> refreshSermons() async {
    currentPage.value = 0;
    await fetchSermons();
  }

  // Get sermon by ID
  Future<SermonModel?> getSermonById(String id) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _sermonService.getSermonById(id);

      if (response["status"] ?? false) {
        final data = response["data"];
        // Directly create SermonModel from JSON data
        return SermonModel.fromJson(data);
      } else {
        errorMessage.value = response["message"] ?? 'Failed to fetch sermon';
        return null;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to fetch sermon: ${e.toString()}';
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // Create a new sermon
  Future<bool> createSermon() async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      if (titleFormController.text.isEmpty ||
          descriptionFormController.text.isEmpty) {
        errorMessage.value = 'Title and description are required';
        return false;
      }

      await _sermonService.createSermon(
        title: titleFormController.text,
        description: descriptionFormController.text,
        category:
            categoryController.text.isEmpty ? null : categoryController.text,
        status: statusFormValue.value,
        media:
            mediaItems.isNotEmpty
                ? mediaItems.map((e) => e.toJson()).toList()
                : null,
      );

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to create sermon: ${e.toString()}';
      logger.e('Error in createSermon: $e');
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Update an existing sermon
  Future<bool> updateSermon(String id) async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      if (titleFormController.text.isEmpty ||
          descriptionFormController.text.isEmpty) {
        errorMessage.value = 'Title and description are required';
        return false;
      }

      await _sermonService.updateSermon(
        id: id,
        title: titleFormController.text,
        description: descriptionFormController.text,
        category:
            categoryController.text.isEmpty ? null : categoryController.text,
        status: statusFormValue.value,
        media:
            mediaItems.isNotEmpty
                ? mediaItems.map((e) => e.toJson()).toList()
                : null,
      );

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to update sermon: ${e.toString()}';
      logger.e('Error in updateSermon: $e');
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Delete a sermon
  Future<bool> deleteSermon(String id) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      await _sermonService.deleteSermon(id);

      refreshSermons();
      return true;
    } catch (e) {
      errorMessage.value = 'Failed to delete sermon: ${e.toString()}';
      logger.e('Error in deleteSermon: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Load sermon data into form for editing
  void loadSermonForEdit(SermonModel sermon) {
    titleFormController.text = sermon.title ?? '';
    descriptionFormController.text = sermon.description ?? '';
    categoryController.text = sermon.category ?? '';
    statusFormValue.value = sermon.status ?? 'active';

    // Set the editing description for the markup editor
    editingDescription.value = sermon.description ?? '';

    // Clear and load media items
    mediaItems.clear();
    if (sermon.media != null && sermon.media!.isNotEmpty) {
      for (var item in sermon.media!) {
        mediaItems.add(item);
      }
    }
  }

  // Clear form fields
  void clearFormFields() {
    titleFormController.clear();
    descriptionFormController.clear();
    categoryController.clear();
    statusFormValue.value = 'active';
    mediaTypeValue.value = 'IMAGE';
    mediaItems.clear();
    mediaTitleController.clear();
    mediaUrlController.clear();
    editingDescription.value = '';
  }

  // Remove media item from the list
  void removeMediaItem(int index) {
    if (index >= 0 && index < mediaItems.length) {
      mediaItems.removeAt(index);
    }
  }
}
