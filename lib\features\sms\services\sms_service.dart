import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../data/models/sms_model.dart';

class SmsService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();

  // Initialize the service
  SmsService() {
    _httpService.initializeDio();
  }

  // Fetch SMS messages with pagination and filters
  Future<Map<String, dynamic>> fetchMessages({
    required int page,
    required int size,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? organisationId,
    String? phoneNumber,
    String? message,
  }) async {
    try {
      // Build URL with query parameters
      String url = "${ApiUrls.getOrganisationSms}?";

      url += "page=$page";
      url += "&size=$size";

      // Add optional filters to URL
      if (searchQuery != null && searchQuery.isNotEmpty) {
        url += "&search=${Uri.encodeComponent(searchQuery)}";
      }

      if (startDate != null) {
        // Format date with time
        final formattedStart = startDate.toUtc().toIso8601String();
        url += "&start_date=${Uri.encodeComponent(formattedStart)}";
      }

      if (endDate != null) {
        // Format date with time
        final formattedEnd = endDate.toUtc().toIso8601String();
        url += "&end_date=${Uri.encodeComponent(formattedEnd)}";
      }

      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        url += "&phone_number=${Uri.encodeComponent(phoneNumber)}";
      }

      if (message != null && message.isNotEmpty) {
        url += "&message=${Uri.encodeComponent(message)}";
      }

      final response = await _httpService.request(url: url, method: Method.GET);

      return response.data;
    } catch (e) {
      logger.e('Error fetching SMS messages: $e');
      rethrow;
    }
  }

  // Send SMS preview
  Future<Map<String, dynamic>> sendSmsPreview({
    required String message,
    required List<String> recipientsPhone,
    required String? organisationId,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'organisation_id': organisationId,
        'message': message,
        'is_all_customers': false,
        'customer_ids': [],
        'notification_group_id': 1,
        'recipients_phone': recipientsPhone,
      };

      final response = await _httpService.request(
        url: ApiUrls.sendSmsNotification,
        method: Method.POST,
        params: data,
      );

      return response.data;
    } catch (e) {
      logger.e('Error sending SMS preview: $e');
      rethrow;
    }
  }

  // Send actual SMS
  Future<Map<String, dynamic>> sendSms({
    required String message,
    required List<String> recipientsPhone,
    required String? organisationId,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'organisation_id': organisationId,
        'message': message,
        'is_all_customers': false,
        'customer_ids': [],
        'notification_group_id': 1,
        'recipients_phone': recipientsPhone,
      };

      final response = await _httpService.request(
        url: ApiUrls.sendSmsNotificationActual,
        method: Method.POST,
        params: data,
      );

      return response.data;
    } catch (e) {
      logger.e('Error sending SMS: $e');
      rethrow;
    }
  }

  // Parse SMS items from API response
  List<SmsModel> parseMessages(List<dynamic> items) {
    return items.map((item) => SmsModel.fromJson(item)).toList();
  }
}
