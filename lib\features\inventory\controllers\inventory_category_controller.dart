import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import '../models/inventory_category_model.dart';
import '../services/inventory_category_service.dart';

class InventoryCategoryController extends GetxController {
  final InventoryCategoryService _categoryService = InventoryCategoryService();
  final logger = Get.find<Logger>();

  // Observable state variables
  final RxBool isLoading = false.obs;
  final RxBool isSubmitting = false.obs;
  final RxList<InventoryCategory> categories = <InventoryCategory>[].obs;
  final RxString searchQuery = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchCategories();
  }

  // Fetch categories with search
  Future<void> fetchCategories({String? search, bool resetList = true}) async {
    isLoading.value = true;

    try {
      final result = await _categoryService.fetchInventoryCategories(
        search: search,
      );

      if (result['status'] == true && result['data'] != null) {
        final List<dynamic> data = result['data'] ?? [];

        final newCategories = _categoryService.parseCategories(data);

        if (resetList) {
          categories.clear();
        }

        categories.addAll(newCategories);

        logger.i('Fetched ${newCategories.length} inventory categories');
      } else {
        logger.e('Error fetching categories: ${result['message']}');
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to load categories',
        );
      }
    } catch (e) {
      logger.e('Exception fetching categories: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
    } finally {
      isLoading.value = false;
    }
  }

  // Refresh categories
  Future<void> refreshCategories() async {
    searchQuery.value = '';
    await fetchCategories(resetList: true);
  }

  // Search categories
  Future<void> searchCategories(String query) async {
    searchQuery.value = query;
    await fetchCategories(search: query);
  }

  // Create a new category
  Future<bool> createCategory({
    required String title,
    required String description,
    required String code,
    required bool isGeneral,
  }) async {
    isSubmitting.value = true;

    try {
      final result = await _categoryService.createInventoryCategory(
        title: title,
        description: description,
        code: code,
        isGeneral: isGeneral,
      );

      if (result['status'] == true) {
        ToastUtils.showSuccessToast('Success', 'Category created successfully');

        // Refresh categories list
        await fetchCategories();
        return true;
      } else {
        logger.e('Error creating category: ${result['message']}');
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to create category',
        );
        return false;
      }
    } catch (e) {
      logger.e('Exception creating category: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Update an existing category
  Future<bool> updateCategory({
    required String id,
    required String title,
    required String description,
    required String code,
  }) async {
    isSubmitting.value = true;

    try {
      final result = await _categoryService.updateInventoryCategory(
        id: id,
        title: title,
        description: description,
        code: code,
      );

      if (result['status'] == true) {
        ToastUtils.showSuccessToast('Success', 'Category updated successfully');

        // Refresh categories list
        await fetchCategories();
        return true;
      } else {
        logger.e('Error updating category: ${result['message']}');
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to update category',
        );
        return false;
      }
    } catch (e) {
      logger.e('Exception updating category: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Delete a category
  Future<bool> deleteCategory(String id) async {
    try {
      final result = await _categoryService.deleteInventoryCategory(id);

      if (result['status'] == true) {
        ToastUtils.showSuccessToast('Success', 'Category deleted successfully');

        // Refresh categories list
        await fetchCategories();
        return true;
      } else {
        logger.e('Error deleting category: ${result['message']}');
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to delete category',
        );
        return false;
      }
    } catch (e) {
      logger.e('Exception deleting category: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      return false;
    }
  }

  // Get category by ID
  InventoryCategory? getCategoryById(String id) {
    try {
      return categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }
}
