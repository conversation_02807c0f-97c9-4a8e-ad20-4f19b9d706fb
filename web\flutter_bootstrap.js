// The proper bootstrap script for Flutter Web
// This ensures the main.dart.js script is loaded with the correct renderer configuration

// Use HTML renderer instead of CanvasKit for better compatibility
window.flutterWebRenderer = "html";

// Handle SPA routing
// This ensures that direct navigation to routes works correctly
window.addEventListener('popstate', function() {
  // Store the current path when navigation happens
  if (window.location.pathname !== '/' && 
      !window.location.pathname.match(/\.(js|html|css|png|jpg|jpeg|gif|svg|ico|json|woff|woff2|ttf|eot)$/)) {
    window.sessionStorage.setItem('flutter_route', window.location.pathname);
  }
});

// Load the main.dart.js script
var scriptLoaderCallback = function () {
  var scriptTag = document.createElement('script');
  scriptTag.src = 'main.dart.js';
  scriptTag.type = 'application/javascript';
  document.body.appendChild(scriptTag);
};

// Wait for the DOM to be ready before loading Flutter
if (document.readyState === 'loading') {
  window.addEventListener('DOMContentLoaded', scriptLoaderCallback);
} else {
  scriptLoaderCallback();
}