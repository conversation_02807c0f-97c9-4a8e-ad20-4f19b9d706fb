import 'dart:convert';

class EventModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final String? startDate;
  final String? endDate;
  final String? status;
  final String? username;
  final String? location;
  final String? description;
  final String? organisationId;
  final dynamic organisation;
  final int? createdByUserId;
  final CreatedByUser? createdByUser;
  final dynamic media;
  final String? frequency;

  EventModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.startDate,
    this.endDate,
    this.status,
    this.username,
    this.location,
    this.description,
    this.organisationId,
    this.organisation,
    this.createdByUserId,
    this.createdByUser,
    this.media,
    this.frequency,
  });

  EventModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? title,
    String? startDate,
    String? endDate,
    String? status,
    String? username,
    String? location,
    String? description,
    String? organisationId,
    dynamic organisation,
    int? createdByUserId,
    CreatedByUser? createdByUser,
    dynamic media,
    String? frequency,
  }) => EventModel(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    title: title ?? this.title,
    startDate: startDate ?? this.startDate,
    endDate: endDate ?? this.endDate,
    status: status ?? this.status,
    username: username ?? this.username,
    location: location ?? this.location,
    description: description ?? this.description,
    organisationId: organisationId ?? this.organisationId,
    organisation: organisation ?? this.organisation,
    createdByUserId: createdByUserId ?? this.createdByUserId,
    createdByUser: createdByUser ?? this.createdByUser,
    media: media ?? this.media,
    frequency: frequency ?? this.frequency,
  );

  factory EventModel.fromRawJson(String str) =>
      EventModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory EventModel.fromJson(Map<String, dynamic> json) {
    return EventModel(
      id: json["id"]?.toString(),
      createdAt:
          json["created_at"] == null
              ? null
              : DateTime.parse(json["created_at"]),
      updatedAt:
          json["updated_at"] == null
              ? null
              : DateTime.parse(json["updated_at"]),
      deletedAt: json["deleted_at"],
      title: json["title"]?.toString() ?? 'No Title',
      startDate: json["start_date"]?.toString(),
      endDate: json["end_date"]?.toString(),
      status: json["status"]?.toString() ?? 'unknown',
      username: json["username"]?.toString(),
      // Try multiple possible field names for location
      location:
          json["location_name"]?.toString() ??
          json["location"]?.toString() ??
          '',
      description: json["description"]?.toString(),
      organisationId: json["organisation_id"]?.toString(),
      organisation: json["organisation"],
      createdByUserId:
          json["created_by_user_id"] is String
              ? int.tryParse(json["created_by_user_id"])
              : json["created_by_user_id"],
      frequency: json["frequency"]?.toString(),
      createdByUser:
          json["created_by_user"] == null
              ? null
              : CreatedByUser.fromJson(json["created_by_user"]),
      media: json["media"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "title": title,
    "start_date": startDate,
    "end_date": endDate,
    "status": status,
    "username": username,
    "location": location,
    "description": description,
    "organisation_id": organisationId,
    "organisation": organisation,
    "created_by_user_id": createdByUserId,
    "created_by_user": createdByUser?.toJson(),
    "media": media,
    "frequency": frequency,
  };
}

class CreatedByUser {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? phoneNumber;
  final String? firstName;
  final String? secondName;
  final String? email;
  final String? idNumber;
  final int? balance;
  final String? birthDate;
  final String? countryCode;
  final String? county;
  final String? subCounty;
  final String? ward;
  final String? secondaryNumber;
  final String? profileUrl;
  final String? role;
  final String? status;
  final dynamic addresses;
  final dynamic devices;

  CreatedByUser({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.phoneNumber,
    this.firstName,
    this.secondName,
    this.email,
    this.idNumber,
    this.balance,
    this.birthDate,
    this.countryCode,
    this.county,
    this.subCounty,
    this.ward,
    this.secondaryNumber,
    this.profileUrl,
    this.role,
    this.status,
    this.addresses,
    this.devices,
  });

  CreatedByUser copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? phoneNumber,
    String? firstName,
    String? secondName,
    String? email,
    String? idNumber,
    int? balance,
    String? birthDate,
    String? countryCode,
    String? county,
    String? subCounty,
    String? ward,
    String? secondaryNumber,
    String? profileUrl,
    String? role,
    String? status,
    dynamic addresses,
    dynamic devices,
  }) => CreatedByUser(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    firstName: firstName ?? this.firstName,
    secondName: secondName ?? this.secondName,
    email: email ?? this.email,
    idNumber: idNumber ?? this.idNumber,
    balance: balance ?? this.balance,
    birthDate: birthDate ?? this.birthDate,
    countryCode: countryCode ?? this.countryCode,
    county: county ?? this.county,
    subCounty: subCounty ?? this.subCounty,
    ward: ward ?? this.ward,
    secondaryNumber: secondaryNumber ?? this.secondaryNumber,
    profileUrl: profileUrl ?? this.profileUrl,
    role: role ?? this.role,
    status: status ?? this.status,
    addresses: addresses ?? this.addresses,
    devices: devices ?? this.devices,
  );

  factory CreatedByUser.fromRawJson(String str) =>
      CreatedByUser.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CreatedByUser.fromJson(Map<String, dynamic> json) => CreatedByUser(
    id: json["ID"],
    createdAt:
        json["CreatedAt"] == null ? null : DateTime.parse(json["CreatedAt"]),
    updatedAt:
        json["UpdatedAt"] == null ? null : DateTime.parse(json["UpdatedAt"]),
    deletedAt: json["DeletedAt"],
    phoneNumber: json["phone_number"],
    firstName: json["first_name"],
    secondName: json["second_name"],
    email: json["email"],
    idNumber: json["id_number"],
    balance: json["balance"],
    birthDate: json["birth_date"],
    countryCode: json["country_code"],
    county: json["county"],
    subCounty: json["sub_county"],
    ward: json["ward"],
    secondaryNumber: json["secondary_number"],
    profileUrl: json["profile_url"],
    role: json["role"],
    status: json["status"],
    addresses: json["addresses"],
    devices: json["devices"],
  );

  Map<String, dynamic> toJson() => {
    "ID": id,
    "CreatedAt": createdAt?.toIso8601String(),
    "UpdatedAt": updatedAt?.toIso8601String(),
    "DeletedAt": deletedAt,
    "phone_number": phoneNumber,
    "first_name": firstName,
    "second_name": secondName,
    "email": email,
    "id_number": idNumber,
    "balance": balance,
    "birth_date": birthDate,
    "country_code": countryCode,
    "county": county,
    "sub_county": subCounty,
    "ward": ward,
    "secondary_number": secondaryNumber,
    "profile_url": profileUrl,
    "role": role,
    "status": status,
    "addresses": addresses,
    "devices": devices,
  };
}
