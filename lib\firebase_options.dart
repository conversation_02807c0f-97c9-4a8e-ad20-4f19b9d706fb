// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAN2L5lVqNWpGv-Vw3zWqO_dOjsSxn4cgg',
    appId: '1:891422507690:web:bed5cd1a02640289dc86d3',
    messagingSenderId: '891422507690',
    projectId: 'church-africa',
    authDomain: 'church-africa.firebaseapp.com',
    storageBucket: 'church-africa.firebasestorage.app',
    measurementId: 'G-4QJE8QXXDK',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC-PxUfP7jC71iEyj5Am88h5HUUyV-TAdo',
    appId: '1:891422507690:android:ed4da346f30f1a87dc86d3',
    messagingSenderId: '891422507690',
    projectId: 'church-africa',
    storageBucket: 'church-africa.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAMccFrg6RD6E-9X0SeCIUsJyaZ_PHOHow',
    appId: '1:891422507690:ios:19b82d0de2e233b3dc86d3',
    messagingSenderId: '891422507690',
    projectId: 'church-africa',
    storageBucket: 'church-africa.firebasestorage.app',
    iosBundleId: 'com.techliana.onechurch.onechurch',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAMccFrg6RD6E-9X0SeCIUsJyaZ_PHOHow',
    appId: '1:891422507690:ios:19b82d0de2e233b3dc86d3',
    messagingSenderId: '891422507690',
    projectId: 'church-africa',
    storageBucket: 'church-africa.firebasestorage.app',
    iosBundleId: 'com.techliana.onechurch.onechurch',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAN2L5lVqNWpGv-Vw3zWqO_dOjsSxn4cgg',
    appId: '1:891422507690:web:44819fed79564d0bdc86d3',
    messagingSenderId: '891422507690',
    projectId: 'church-africa',
    authDomain: 'church-africa.firebaseapp.com',
    storageBucket: 'church-africa.firebasestorage.app',
    measurementId: 'G-B1PCELNDN8',
  );
}
