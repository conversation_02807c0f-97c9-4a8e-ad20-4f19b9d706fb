import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/features/finances/controllers/finance_controller.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';

class CreateCategoryFormWidget extends StatelessWidget {
  const CreateCategoryFormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    return GetBuilder<FinanceController>(
      init: Get.put(FinanceController()),
      builder: (controller) {
        return Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Form Card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: EdgeInsets.all(20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Category Information',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      Gap(16.h),

                      // Title Field
                      CustomTextFormField(
                        controller: controller.titleController,
                        labelText: 'Category Title',
                        hintText: 'Enter category title (e.g., Donation)',
                        prefixIcon: const Icon(IconlyLight.category),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a category title';
                          }
                          if (value.length < 3) {
                            return 'Title must be at least 3 characters';
                          }
                          return null;
                        },
                        textCapitalization: TextCapitalization.words,
                      ),

                      Gap(16.h),

                      // Description Field
                      CustomTextFormField(
                        controller: controller.descriptionController,
                        labelText: 'Description',
                        hintText:
                            'Enter category description (e.g., Church donations)',
                        prefixIcon: const Icon(IconlyLight.document),
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a description';
                          }
                          if (value.length < 10) {
                            return 'Description must be at least 10 characters';
                          }
                          return null;
                        },
                        textCapitalization: TextCapitalization.sentences,
                      ),

                      Gap(20.h),

                      // Info Section
                      Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          color: Colors.amber.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.amber.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              IconlyLight.infoSquare,
                              color: Colors.amber.shade700,
                              size: 20,
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: Text(
                                'This category will be used to organize your sub-accounts. You can create multiple sub-accounts under this category.',
                                style: TextStyle(
                                  fontSize: 13,
                                  color: Colors.amber.shade800,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              Gap(24.h),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      style: CustomButtonStyle.outlined,
                      onPressed: () {
                        controller.clearForm();
                        context.pop();
                      },
                      label: Text('Cancel', style: TextStyle(fontSize: 16)),
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    flex: 2,
                    child: Obx(
                      () => CustomButton(
                        onPressed:
                            controller.isSubmitting.value
                                ? null
                                : () async {
                                  if (formKey.currentState!.validate()) {
                                    final success =
                                        await controller.createCategory();
                                    if (success) {
                                      context.pop();
                                    }
                                  }
                                },
                        isLoading: controller.isSubmitting.value,
                        label: Text(
                          'Create Category',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              Gap(16.h),

              // Error Message
              Obx(
                () =>
                    controller.message.value.isNotEmpty
                        ? Container(
                          padding: EdgeInsets.all(12.w),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red.shade200),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                IconlyLight.dangerTriangle,
                                color: Colors.red.shade700,
                                size: 20,
                              ),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: Text(
                                  controller.message.value,
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.red.shade800,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                        : const SizedBox.shrink(),
              ),
            ],
          ),
        );
      },
    );
  }
}
