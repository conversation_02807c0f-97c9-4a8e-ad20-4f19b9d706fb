

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/utils/screen_breakpoints.dart';
import 'package:onechurch/data/models/group_model.dart';
import 'package:onechurch/data/models/member_model.dart';
import 'package:onechurch/features/group/controllers/group_controller.dart';
import 'package:onechurch/features/group/presentation/view_groups.dart';
import 'package:onechurch/features/members/presentation/screens/view_member/widgets/section_header.dart';

class ShowMemberGroups extends StatelessWidget {
  final MemberModel member;

  const ShowMemberGroups({super.key, required this.member});
  

  @override
  Widget build(BuildContext context) {
    
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < ScreenBreakpoints.mobile;
    final isTablet =
        screenWidth >= ScreenBreakpoints.mobile &&
        screenWidth < ScreenBreakpoints.tablet;
    final isDesktop = screenWidth >= ScreenBreakpoints.tablet;

    // Get the group controller
    final groupController = Get.find<GroupController>();
    groupController.memberId.value = member.id??'';
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 16.h, bottom: 16.h),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Theme.of(context).dividerColor.withOpacity(0.1),
          width: 1.r,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Obx(() {
        groupController.groups.value =
            member.groups ?? <GroupModel>[].obs;
        if (groupController.groups.isEmpty) {
          return Column(
            children: [
              Padding(
                padding: EdgeInsets.all(
                  isDesktop ? 24.r : (isMobile ? 16.r : 20.r),
                ),
                child: SectionHeader(title:  'Member Groups'),
              ),
              SizedBox(
                height: 200.h,
                child: const Center(
                  child: Text('Member is not part of any groups yet'),
                ),
              ),
            ],
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.all(
                isDesktop ? 24.r : (isMobile ? 16.r : 20.r),
              ),
              child: SectionHeader(title: 'Member Groups'),
            ),
            SizedBox(
              height: isDesktop ? 600.h : (isTablet ? 500.h : 400.h),
              child: ViewGroupsGrid(),
            ),
          ],
        );
      }),
    );
  }
}

  