import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/constants/enums.dart';
import 'package:onechurch/core/app/widgets/custom_dropdown.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/core/widgets/html/markup_editor_widget.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import 'package:onechurch/features/media_upload/presentation/widgets/media_upload_widget.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import '../../../../core/app/utils/size_config.dart';
import '../../controllers/event_controller.dart';
import '../../../../data/models/event_model.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';

class CreateEventScreen extends StatefulWidget {
  final EventModel? initialEvent;

  const CreateEventScreen({super.key, this.initialEvent});

  @override
  State<CreateEventScreen> createState() => _CreateEventScreenState();
}

class _CreateEventScreenState extends State<CreateEventScreen> {
  // Event controller
  final EventController _eventController = Get.find<EventController>();

  // Form key for validation
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Focus nodes for form fields
  late FocusNode _titleFocusNode;
  late FocusNode _locationFocusNode;

  // Controllers for text fields
  late TextEditingController _titleController;
  late TextEditingController _locationController;
  late TextEditingController _frequencyController;

  // Description variable for markup editor
  String _description = '';

  // Date and status variables
  late DateTime _startDate;
  late DateTime _endDate;
  String _status = 'Active';

  @override
  void initState() {
    super.initState();

    // Initialize focus nodes
    _titleFocusNode = FocusNode();
    _locationFocusNode = FocusNode();
    _frequencyController = TextEditingController(text: 'ONETIME');

    // Clear any previous media items
    _eventController.clearFormFields();

    // Initialize controllers with initial event data if available
    _titleController = TextEditingController(
      text: widget.initialEvent?.title ?? '',
    );
    _locationController = TextEditingController(
      text: widget.initialEvent?.location ?? '',
    );

    // Initialize description with initial event data if available
    _description = widget.initialEvent?.description ?? '';

    // Initialize dates with current date or initial event dates
    _startDate =
        widget.initialEvent?.startDate != null
            ? DateTime.parse(widget.initialEvent!.startDate!)
            : DateTime.now();
    _endDate =
        widget.initialEvent?.endDate != null
            ? DateTime.parse(widget.initialEvent!.endDate!)
            : DateTime.now().add(const Duration(days: 1));

    // Initialize status with initial event status or 'draft'
    _status = widget.initialEvent?.status ?? 'draft';
  }

  @override
  void dispose() {
    // Dispose controllers
    _titleController.dispose();
    _locationController.dispose();

    // Dispose focus nodes
    _titleFocusNode.dispose();
    _locationFocusNode.dispose();

    super.dispose();
  }

  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
        // Adjust end date if it's before the new start date
        if (_endDate.isBefore(picked)) {
          _endDate = picked.add(const Duration(days: 1));
        }
      });
    }
  }

  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate,
      firstDate: _startDate,
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = picked;
      });
    }
  }

  FocusNode? _findFirstErrorField() {
    if (_titleController.text.isEmpty) return _titleFocusNode;
    if (_locationController.text.isEmpty) return _locationFocusNode;
    return null;
  }

  Future<void> _saveEvent() async {
    // Validate form
    if (!_formKey.currentState!.validate()) {
      final firstErrorField = _findFirstErrorField();
      if (firstErrorField != null) {
        Scrollable.ensureVisible(
          firstErrorField.context!,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        firstErrorField.requestFocus();
      }
      return;
    }

    // Validate date range
    if (_endDate.isBefore(_startDate)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('End date must be after start date'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Validate frequency
    if (_frequencyController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a frequency'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Unfocus all text fields
    FocusScope.of(context).unfocus();

    try {
      // Create or update event
      if (widget.initialEvent == null) {
        // Create a new event
        final EventModel event = EventModel(
          title: _titleController.text,
          description: _description,
          location: _locationController.text,
          startDate: _startDate.toIso8601String(),
          endDate: _endDate.toIso8601String(),
          organisationId: Get.find<AuthController>().currentOrg.value?.id,
          frequency: _frequencyController.text,
          status: _status,
        );
        await _eventController.createEvent(event);
      } else {
        // Update existing event
        final EventModel updatedEvent = EventModel(
          id: widget.initialEvent!.id,
          title: _titleController.text,
          description: _description,
          location: _locationController.text,
          startDate: _startDate.toIso8601String(),
          endDate: _endDate.toIso8601String(),
          frequency: _frequencyController.text,
          status: _status,
          createdAt: widget.initialEvent!.createdAt,
          updatedAt: widget.initialEvent!.updatedAt,
          createdByUserId: widget.initialEvent!.createdByUserId,
        );
        await _eventController.updateEvent(
          widget.initialEvent!.id!,
          updatedEvent,
        );
      }

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            widget.initialEvent == null
                ? 'Event created successfully'
                : 'Event updated successfully',
          ),
          backgroundColor: Colors.green,
        ),
      );

      // Navigate back to events list
      context.go('/events');
    } catch (e) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Failed to ${widget.initialEvent == null ? 'create' : 'update'} event: $e',
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.initialEvent == null ? 'Create Event' : 'Edit Event',
        ),
        actions: [
          Obx(
            () =>
                _eventController.isLoading.value
                    ? const Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.0),
                        child: SizedBox(
                          width: 24,
                          height: 24,
                          child: CircleLoadingAnimation(strokeWidth: 2),
                        ),
                      ),
                    )
                    : TextButton.icon(
                      onPressed: _saveEvent,
                      icon: const Icon(IconlyLight.tickSquare),
                      label: const Text('Save'),
                    ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: SizeConfig.screenWidth * 0.03,
            vertical: SizeConfig.screenHeight * 0.1,
          ),
          child: Center(
            child: Card(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 8.h),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.initialEvent == null
                            ? "Create Event"
                            : "Edit Event",
                        style: theme.textTheme.titleLarge,
                      ),
                      Gap(10.h),
                      Text(
                        "Title of the event",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      CustomTextFormField(
                        controller: _titleController,
                        focusNode: _titleFocusNode,
                        hintText: 'Enter event title',
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter an event title';
                          }
                          return null;
                        },
                      ),
                      Gap(16.h),

                      // Description Field with Markup Editor
                      MarkupEditorWidget(
                        label: "Description",
                        isRequired: true,
                        hintText:
                            "Enter event description with rich formatting",
                        height: 250.h,
                        initialValue: _description,
                        onChanged: (content) {
                          _description = content;
                        },
                      ),
                      Gap(16.h),
                      Text(
                        "Location",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      CustomTextFormField(
                        controller: _locationController,
                        focusNode: _locationFocusNode,
                        hintText: 'Enter event location',

                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter an event location';
                          }
                          return null;
                        },
                      ),
                      Gap(16.h),
                      Text(
                        "Event Dates",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: CustomButton(
                              onPressed: _selectStartDate,
                             label: Text(
                                'Start: ${_startDate.toLocal().toString().split(' ')[0]}',
                              ),
                            ),
                          ),
                          Gap(16.w),
                          Expanded(
                            child: CustomButton(
                              onPressed: _selectEndDate,
                             label: Text(
                                'End: ${_endDate.toLocal().toString().split(' ')[0]}',
                              ),
                            ),
                          ),
                        ],
                      ),
                      Gap(16.h),
                      Text(
                        "Frequency",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      CustomDropdown(
                        value:
                            _frequencyController.text.isNotEmpty
                                ? _frequencyController.text
                                : null,

                        items:
                            Frequency.values
                                .map(
                                  (frequency) => DropdownMenuItem<String>(
                                    value: frequency.displayName,
                                    child: Text(frequency.displayName),
                                  ),
                                )
                                .toList(),
                        onChanged: (value) {
                          setState(() {
                            _frequencyController.text = value!;
                          });
                        },
                        labelText: 'Frequency',
                      ),

                      Gap(16.h),
                      Card(
                        elevation: 0,
                        color: Colors.grey.shade50,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: Colors.grey.shade300),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(IconlyLight.image),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Event Media',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              Gap(12.h),
                              Column(
                                children: [
                                  MediaUploadWidget(
                                    category: 'EVENT',
                                    multipleSelect: true,
                                    onMediaSelected: (selectedMedia) {
                                      if (selectedMedia.isNotEmpty) {
                                        _eventController.mediaTypeValue.value =
                                            selectedMedia[0].type ?? '';
                                        _eventController.mediaItems.clear();
                                        _eventController.mediaItems.addAll(
                                          selectedMedia,
                                        );
                                      }
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),

                      if (widget.initialEvent != null) ...[
                        Gap(16.h),
                        Text(
                          "Status",
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        DropdownButtonFormField<String>(
                          value: _status,
                          decoration: InputDecoration(
                            hintText: 'Select event status',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          items:
                              ['draft', 'active', 'inactive']
                                  .map(
                                    (status) => DropdownMenuItem(
                                      value: status,
                                      child: Text(status.capitalize),
                                    ),
                                  )
                                  .toList(),
                          onChanged: (value) {
                            setState(() {
                              _status = value!;
                            });
                          },
                        ),
                      ],

                      Gap(24.h),
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: SizeConfig.screenWidth * 0.05,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Expanded(
                              child: CustomButton(
                                style: CustomButtonStyle.outlined,
                                onPressed: () {
                                  context.go('/events');
                                },
                                icon: const Icon(IconlyLight.arrowLeft),
                                label: const Text('Cancel'),
                              ),
                            ),
                            Gap(16.w),
                            Expanded(
                              child: CustomButton(
                                onPressed:
                                    _eventController.isUpdating.value
                                        ? null
                                        : _saveEvent,
                                icon:
                                    _eventController.isUpdating.value
                                        ? SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircleLoadingAnimation(
                                            strokeWidth: 2,
                                          ),
                                        )
                                        : const Icon(IconlyLight.tickSquare),
                                label: Text(
                                  widget.initialEvent == null
                                      ? 'Create'
                                      : 'Update',
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
