import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

class HtmlDisplayWidget extends StatelessWidget {
  final String htmlContent;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final String? emptyMessage;
  final TextStyle? emptyMessageStyle;
  final Map<String, Style>? customStyles;
  final bool showBorder;
  final Color? borderColor;
  final Color? backgroundColor;

  const HtmlDisplayWidget({
    super.key,
    required this.htmlContent,
    this.height,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.emptyMessage,
    this.emptyMessageStyle,
    this.customStyles,
    this.showBorder = false,
    this.borderColor,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Check if content is empty
    if (htmlContent.trim().isEmpty) {
      return Container(
        height: height,
        padding: padding ?? EdgeInsets.all(16.w),
        decoration:
            showBorder
                ? BoxDecoration(
                  border: Border.all(
                    color: borderColor ?? theme.dividerColor,
                    width: 1.0,
                  ),
                  borderRadius: BorderRadius.circular(8.0),
                  color: backgroundColor ?? theme.cardColor,
                )
                : null,
        child: Center(
          child: Text(
            emptyMessage ?? 'No content to display',
            style:
                emptyMessageStyle ??
                theme.textTheme.bodyMedium?.copyWith(
                  color: theme.hintColor,
                  fontStyle: FontStyle.italic,
                ),
          ),
        ),
      );
    }

    Widget htmlWidget = Html(
      data: htmlContent,
      style: _getHtmlStyles(theme),
      onLinkTap: (url, attributes, element) {
        if (url != null) {
          _launchUrl(url);
        }
      },
      // onImageTap: (src, attributes, element) {
      //   // Handle image taps if needed
      //   if (src != null) {
      //     _showImageDialog(context, src);
      //   }
      // },
    );

    // Wrap in container if height is specified or border is needed
    if (height != null || showBorder || backgroundColor != null) {
      return Container(
        height: height,
        padding: padding ?? EdgeInsets.all(12.w),
        decoration:
            showBorder || backgroundColor != null
                ? BoxDecoration(
                  border:
                      showBorder
                          ? Border.all(
                            color: borderColor ?? theme.dividerColor,
                            width: 1.0,
                          )
                          : null,
                  borderRadius: BorderRadius.circular(8.0),
                  color: backgroundColor ?? theme.cardColor,
                )
                : null,
        child:
            shrinkWrap
                ? htmlWidget
                : SingleChildScrollView(physics: physics, child: htmlWidget),
      );
    }

    // Return with padding if specified
    if (padding != null) {
      return Padding(padding: padding!, child: htmlWidget);
    }

    return htmlWidget;
  }

  /// Get default HTML styles that match the app theme
  Map<String, Style> _getHtmlStyles(ThemeData theme) {
    final defaultStyles = {
      "body": Style(
        fontSize: FontSize(16.0),
        lineHeight: LineHeight.number(1.5),
        margin: Margins.zero,
        padding: HtmlPaddings.zero,
        color: theme.textTheme.bodyLarge?.color,
      ),
      "p": Style(margin: Margins.only(bottom: 16.0)),
      "h1": Style(
        fontSize: FontSize(28.0),
        fontWeight: FontWeight.bold,
        margin: Margins.only(bottom: 16.0, top: 24.0),
        color: theme.textTheme.headlineLarge?.color,
      ),
      "h2": Style(
        fontSize: FontSize(24.0),
        fontWeight: FontWeight.bold,
        margin: Margins.only(bottom: 14.0, top: 20.0),
        color: theme.textTheme.headlineMedium?.color,
      ),
      "h3": Style(
        fontSize: FontSize(20.0),
        fontWeight: FontWeight.bold,
        margin: Margins.only(bottom: 12.0, top: 16.0),
        color: theme.textTheme.headlineSmall?.color,
      ),
      "h4": Style(
        fontSize: FontSize(18.0),
        fontWeight: FontWeight.bold,
        margin: Margins.only(bottom: 10.0, top: 14.0),
        color: theme.textTheme.titleLarge?.color,
      ),
      "h5": Style(
        fontSize: FontSize(16.0),
        fontWeight: FontWeight.bold,
        margin: Margins.only(bottom: 8.0, top: 12.0),
        color: theme.textTheme.titleMedium?.color,
      ),
      "h6": Style(
        fontSize: FontSize(14.0),
        fontWeight: FontWeight.bold,
        margin: Margins.only(bottom: 6.0, top: 10.0),
        color: theme.textTheme.titleSmall?.color,
      ),
      "ul": Style(
        margin: Margins.only(bottom: 16.0, left: 20.0),
        padding: HtmlPaddings.zero,
      ),
      "ol": Style(
        margin: Margins.only(bottom: 16.0, left: 20.0),
        padding: HtmlPaddings.zero,
      ),
      "li": Style(margin: Margins.only(bottom: 8.0), display: Display.listItem),
      "blockquote": Style(
        margin: Margins.only(left: 16.0, bottom: 16.0, top: 16.0),
        padding: HtmlPaddings.only(left: 16.0, top: 8.0, bottom: 8.0),
        border: Border(left: BorderSide(color: theme.primaryColor, width: 4.0)),
        backgroundColor: theme.primaryColor.withOpacity(0.05),
        fontStyle: FontStyle.italic,
      ),
      "code": Style(
        backgroundColor: theme.colorScheme.surfaceVariant,
        color: theme.colorScheme.onSurfaceVariant,
        padding: HtmlPaddings.symmetric(horizontal: 6.0, vertical: 2.0),
        fontFamily: 'monospace',
        fontSize: FontSize(14.0),
        border: Border.all(color: theme.colorScheme.outline),
        // borderRadius: BorderRadius.circular(4.0),
      ),
      "pre": Style(
        backgroundColor: theme.colorScheme.surfaceVariant,
        color: theme.colorScheme.onSurfaceVariant,
        padding: HtmlPaddings.all(12.0),
        margin: Margins.only(bottom: 16.0),
        fontFamily: 'monospace',
        fontSize: FontSize(14.0),
        // borderRadius: BorderRadius.circular(8.0),
        whiteSpace: WhiteSpace.pre,
      ),
      "a": Style(
        color: theme.primaryColor,
        textDecoration: TextDecoration.underline,
      ),
      "strong": Style(fontWeight: FontWeight.bold),
      "b": Style(fontWeight: FontWeight.bold),
      "em": Style(fontStyle: FontStyle.italic),
      "i": Style(fontStyle: FontStyle.italic),
      "u": Style(textDecoration: TextDecoration.underline),
      "strike": Style(textDecoration: TextDecoration.lineThrough),
      "sub": Style(fontSize: FontSize(12.0), verticalAlign: VerticalAlign.sub),
      "sup": Style(fontSize: FontSize(12.0), verticalAlign: VerticalAlign.sup),
      "table": Style(
        border: Border.all(color: theme.dividerColor),
        margin: Margins.only(bottom: 16.0),
        width: Width(100, Unit.percent),
      ),
      "th": Style(
        border: Border.all(color: theme.dividerColor),
        padding: HtmlPaddings.all(8.0),
        fontWeight: FontWeight.bold,
        backgroundColor: theme.colorScheme.surfaceVariant,
        textAlign: TextAlign.left,
      ),
      "td": Style(
        border: Border.all(color: theme.dividerColor),
        padding: HtmlPaddings.all(8.0),
        textAlign: TextAlign.left,
      ),
      "hr": Style(
        margin: Margins.symmetric(vertical: 16.0),
        border: Border(
          bottom: BorderSide(color: theme.dividerColor, width: 1.0),
        ),
      ),
    };

    // Merge with custom styles if provided
    if (customStyles != null) {
      defaultStyles.addAll(customStyles!);
    }

    return defaultStyles;
  }

  /// Launch URL in browser
  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }
}

/// A card-style HTML display widget with optional actions
class HtmlDisplayCard extends StatelessWidget {
  final String htmlContent;
  final String? title;
  final List<Widget>? actions;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final double? elevation;
  final Color? backgroundColor;
  final String? emptyMessage;
  final bool showBorder;
  final VoidCallback? onTap;

  const HtmlDisplayCard({
    super.key,
    required this.htmlContent,
    this.title,
    this.actions,
    this.margin,
    this.padding,
    this.elevation,
    this.backgroundColor,
    this.emptyMessage,
    this.showBorder = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: margin ?? EdgeInsets.all(8.w),
      elevation: elevation ?? 2.0,
      color: backgroundColor,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (title != null || actions != null)
              Padding(
                padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 8.h),
                child: Row(
                  children: [
                    if (title != null)
                      Expanded(
                        child: Text(
                          title!,
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    if (actions != null) ...actions!,
                  ],
                ),
              ),
            HtmlDisplayWidget(
              htmlContent: htmlContent,
              padding: padding ?? EdgeInsets.fromLTRB(16.w, 8.h, 16.w, 16.h),
              emptyMessage: emptyMessage,
              showBorder: false, // Card already provides border
            ),
          ],
        ),
      ),
    );
  }
}

/// A compact HTML display widget for lists and previews
class HtmlDisplayPreview extends StatelessWidget {
  final String htmlContent;
  final int maxLines;
  final String? moreText;
  final VoidCallback? onShowMore;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry? padding;

  const HtmlDisplayPreview({
    super.key,
    required this.htmlContent,
    this.maxLines = 3,
    this.moreText,
    this.onShowMore,
    this.textStyle,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Strip HTML tags for preview
    final plainText =
        htmlContent
            .replaceAll(RegExp(r'<[^>]*>'), '')
            .replaceAll(RegExp(r'\s+'), ' ')
            .trim();

    if (plainText.isEmpty) {
      return Padding(
        padding: padding ?? EdgeInsets.zero,
        child: Text(
          'No content available',
          style:
              textStyle ??
              theme.textTheme.bodyMedium?.copyWith(
                color: theme.hintColor,
                fontStyle: FontStyle.italic,
              ),
        ),
      );
    }

    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            plainText,
            style: textStyle ?? theme.textTheme.bodyMedium,
            maxLines: maxLines,
            overflow: TextOverflow.ellipsis,
          ),
          if (plainText.length > 100 && onShowMore != null) ...[
            SizedBox(height: 4.h),
            GestureDetector(
              onTap: onShowMore,
              child: Text(
                moreText ?? 'Show more',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
