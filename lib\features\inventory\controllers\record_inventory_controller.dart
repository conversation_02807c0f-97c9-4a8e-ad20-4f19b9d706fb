import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../../../core/app/constants/routes.dart';
import '../../../core/app/utils/show_toast.dart';
import '../../../data/models/member_model.dart';
import '../../auth/controllers/auth_controller.dart';
import '../../media_upload/models/media_model.dart';
import '../models/inventory_item_model.dart';
import '../services/inventory_item_service.dart';
import 'inventory_controller.dart';

class RecordInventoryController extends GetxController {
  // Dependencies
  final InventoryController _inventoryController =
      Get.find<InventoryController>();
  final InventoryItemService _inventoryItemService = InventoryItemService();

  // Form controllers
  late final TextEditingController quantityController;
  late final TextEditingController batchNoController;
  late final TextEditingController estimatedValueController;
  late final TextEditingController costController;
  late final TextEditingController notesController;
  late final TextEditingController fullNamesController;
  late final TextEditingController emailController;
  late final TextEditingController phoneNumberController;
  late final TextEditingController countyController;
  late final TextEditingController cityController;
  late final TextEditingController addressController;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // Reactive state variables
  final RxString selectedInventoryItemId = RxString('');
  final RxString selectedMemberId = RxString('');
  final RxString selectedCondition = RxString('');
  final RxString selectedInventoryType = RxString('');
  final Rx<DateTime?> receivedAt = Rx<DateTime?>(DateTime.now());
  final Rx<DateTime?> expiryDate = Rx<DateTime?>(null);
  final Rx<DateTime?> purchaseDate = Rx<DateTime?>(null);
  final Rx<DateTime?> warrantyExpiry = Rx<DateTime?>(null);
  final RxBool isLoading = false.obs;
  final RxBool isAnonymous = false.obs;
  final RxList<MediaModel> mediaItems = <MediaModel>[].obs;

  // Data lists
  final RxList<InventoryItemModel> inventoryItems = <InventoryItemModel>[].obs;
  final Rx<MemberModel?> selectedMember = Rx<MemberModel?>(null);
  final RxBool isLoadingItems = false.obs;
  final RxList<String> conditions = <String>[].obs;
  final RxList<String> inventoryTypes = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initializeControllers();
    _loadInitialData();
  }

  @override
  void onReady() {
    super.onReady();
    // Ensure data is loaded when the controller is ready
    if (conditions.isEmpty || inventoryTypes.isEmpty) {
      _loadDropdownData();
    }
    if (inventoryItems.isEmpty && !isLoadingItems.value) {
      _loadInventoryItems();
    }
  }

  @override
  void onClose() {
    quantityController.dispose();
    batchNoController.dispose();
    estimatedValueController.dispose();
    costController.dispose();
    notesController.dispose();
    fullNamesController.dispose();
    emailController.dispose();
    phoneNumberController.dispose();
    countyController.dispose();
    cityController.dispose();
    addressController.dispose();
    super.onClose();
  }

  void _initializeControllers() {
    quantityController = TextEditingController();
    batchNoController = TextEditingController();
    estimatedValueController = TextEditingController();
    costController = TextEditingController();
    notesController = TextEditingController();
    fullNamesController = TextEditingController();
    emailController = TextEditingController();
    phoneNumberController = TextEditingController();
    countyController = TextEditingController();
    cityController = TextEditingController();
    addressController = TextEditingController();
  }

  void _loadInitialData() {
    _loadInventoryItems();
    _loadDropdownData();
  }

  void _loadDropdownData() {
    // Load conditions and inventory types
    conditions.value = _inventoryItemService.getInventoryConditions();
    inventoryTypes.value = _inventoryItemService.getInventoryTypes();

    debugPrint('Loaded ${conditions.length} conditions: ${conditions.value}');
    debugPrint(
      'Loaded ${inventoryTypes.length} inventory types: ${inventoryTypes.value}',
    );
  }

  // Method to refresh all data
  Future<void> refreshData() async {
    await _loadInventoryItems();
    _loadDropdownData();
  }

  Future<void> _loadInventoryItems() async {
    isLoadingItems.value = true;
    try {
      final response = await _inventoryItemService.fetchInventoryItems(
        page: 0,
        size: 1000, // Load all items for selection
        organisationId: Get.find<AuthController>().currentOrg.value?.id,
      );

      if (response['status'] == true && response['data'] != null) {
        final data = response['data'];

        // Handle different response structures
        List<dynamic> itemsData = [];

        if (data is List) {
          // Direct list response
          itemsData = data;
        } else if (data is Map) {
          // Check for nested data structures
          if (data.containsKey('data') && data['data'] is Map) {
            final innerData = data['data'];
            if (innerData.containsKey('items')) {
              itemsData = innerData['items'] ?? [];
            } else if (innerData.containsKey('content')) {
              itemsData = innerData['content'] ?? [];
            } else if (innerData.containsKey('data')) {
              itemsData = innerData['data'] ?? [];
            }
          } else if (data.containsKey('content')) {
            itemsData = data['content'] ?? [];
          } else if (data.containsKey('items')) {
            itemsData = data['items'] ?? [];
          }
        }

        inventoryItems.value =
            itemsData.map((item) => InventoryItemModel.fromJson(item)).toList();

        debugPrint('Loaded ${inventoryItems.length} inventory items');
      } else {
        debugPrint('Failed to load inventory items: ${response['message']}');
        ToastUtils.showErrorToast(
          response['message'] ?? 'Failed to load inventory items',
          null,
        );
      }
    } catch (e) {
      debugPrint('Error loading inventory items: $e');
      ToastUtils.showErrorToast('Failed to load inventory items: $e', null);
    } finally {
      isLoadingItems.value = false;
    }
  }

  // Setters for reactive variables
  void setSelectedInventoryItem(String? itemId) {
    selectedInventoryItemId.value = itemId ?? '';
  }

  void setSelectedMember(String? memberId) {
    selectedMemberId.value = memberId ?? '';
  }

  // Navigate to member selection screen
  Future<void> selectMember(BuildContext context) async {
    final result = await context.push(
      Routes.MEMBERS,
      extra: {'isSelect': true},
    );

    if (result != null && result is List<MemberModel> && result.isNotEmpty) {
      selectedMember.value = result.first;
      selectedMemberId.value = result.first.id ?? '';
    }
  }

  void setSelectedCondition(String? condition) {
    selectedCondition.value = condition ?? '';
  }

  void setSelectedInventoryType(String? type) {
    selectedInventoryType.value = type ?? '';
  }

  void setReceivedAt(DateTime? date) {
    receivedAt.value = date;
  }

  void setExpiryDate(DateTime? date) {
    expiryDate.value = date;
  }

  void setPurchaseDate(DateTime? date) {
    purchaseDate.value = date;
  }

  void setWarrantyExpiry(DateTime? date) {
    warrantyExpiry.value = date;
  }

  void setIsAnonymous(bool value) {
    isAnonymous.value = value;
    if (value) {
      selectedMemberId.value = '';
    } else {
      // Clear anonymous fields
      fullNamesController.clear();
      emailController.clear();
      phoneNumberController.clear();
      countyController.clear();
      cityController.clear();
      addressController.clear();
    }
  }

  void setMediaItems(List<MediaModel> media) {
    mediaItems.value = media;
  }

  void removeMediaItem(int index) {
    if (index >= 0 && index < mediaItems.length) {
      mediaItems.removeAt(index);
    }
  }

  void clearForm() {
    quantityController.clear();
    batchNoController.clear();
    estimatedValueController.clear();
    costController.clear();
    notesController.clear();
    fullNamesController.clear();
    emailController.clear();
    phoneNumberController.clear();
    countyController.clear();
    cityController.clear();
    addressController.clear();

    selectedInventoryItemId.value = '';
    selectedMemberId.value = '';
    selectedCondition.value = '';
    selectedInventoryType.value = '';
    receivedAt.value = DateTime.now();
    expiryDate.value = null;
    purchaseDate.value = null;
    warrantyExpiry.value = null;
    isAnonymous.value = false;
    mediaItems.clear();
  }

  // Validation helpers
  String? validateQuantity(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Please enter quantity';
    }
    final quantity = int.tryParse(value.trim());
    if (quantity == null || quantity <= 0) {
      return 'Please enter a valid quantity';
    }
    return null;
  }

  String? validateInventoryItem(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please select an inventory item';
    }
    return null;
  }

  String? validateCondition(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please select a condition';
    }
    return null;
  }

  String? validateInventoryType(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please select an inventory type';
    }
    return null;
  }

  String? validateMemberOrAnonymous() {
    if (!isAnonymous.value && selectedMemberId.value.isEmpty) {
      return 'Please select a member or enable anonymous recording';
    }
    if (isAnonymous.value && fullNamesController.text.trim().isEmpty) {
      return 'Please enter full names for anonymous recording';
    }
    return null;
  }

  String? validateEstimatedValue(String? value) {
    if (value != null && value.trim().isNotEmpty) {
      final amount = double.tryParse(value.trim());
      if (amount == null || amount < 0) {
        return 'Please enter a valid amount';
      }
    }
    return null;
  }

  String? validateCost(String? value) {
    if (value != null && value.trim().isNotEmpty) {
      final amount = double.tryParse(value.trim());
      if (amount == null || amount < 0) {
        return 'Please enter a valid cost';
      }
    }
    return null;
  }

  Future<void> submitForm(BuildContext context) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    // Additional validation for member/anonymous
    final memberValidation = validateMemberOrAnonymous();
    if (memberValidation != null) {
      ToastUtils.showErrorToast(memberValidation, null);
      return;
    }

    if (selectedInventoryItemId.value.isEmpty) {
      ToastUtils.showErrorToast('Please select an inventory item', null);
      return;
    }

    if (selectedCondition.value.isEmpty) {
      ToastUtils.showErrorToast('Please select a condition', null);
      return;
    }

    if (selectedInventoryType.value.isEmpty) {
      ToastUtils.showErrorToast('Please select an inventory type', null);
      return;
    }

    if (receivedAt.value == null) {
      ToastUtils.showErrorToast('Please select a received date', null);
      return;
    }

    isLoading.value = true;

    try {
      final success = await _inventoryController.recordInventory(
        memberId: isAnonymous.value ? '' : selectedMemberId.value,
        inventoryItemId: selectedInventoryItemId.value,
        quantity: int.parse(quantityController.text.trim()),
        condition: selectedCondition.value,
        inventoryType: selectedInventoryType.value,
        receivedAt: receivedAt.value!,
        expiryDate: expiryDate.value,
        batchNo:
            batchNoController.text.trim().isEmpty
                ? null
                : batchNoController.text.trim(),
        estimatedValue:
            estimatedValueController.text.trim().isEmpty
                ? null
                : double.tryParse(estimatedValueController.text.trim()),
        purchaseDate: purchaseDate.value,
        cost:
            costController.text.trim().isEmpty
                ? null
                : double.tryParse(costController.text.trim()),
        warrantyExpiry: warrantyExpiry.value,
        notes:
            notesController.text.trim().isEmpty
                ? null
                : notesController.text.trim(),
        media:
            mediaItems.isNotEmpty
                ? mediaItems.map((media) => media.toJson()).toList()
                : null,
        isAnonymous: isAnonymous.value,
        fullNames: isAnonymous.value ? fullNamesController.text.trim() : null,
        email: isAnonymous.value ? emailController.text.trim() : null,
        phoneNumber:
            isAnonymous.value ? phoneNumberController.text.trim() : null,
        county: isAnonymous.value ? countyController.text.trim() : null,
        city: isAnonymous.value ? cityController.text.trim() : null,
        address: isAnonymous.value ? addressController.text.trim() : null,
      );

      if (success) {
        ToastUtils.showSuccessToast(
          'Inventory record created successfully',
          null,
        );
        context.go(Routes.INVENTORY);
      } else {
        ToastUtils.showErrorToast(
          _inventoryController.errorMessage.value,
          null,
        );
      }
    } catch (e) {
      ToastUtils.showErrorToast('Failed to record inventory: $e', null);
    } finally {
      isLoading.value = false;
    }
  }

  // Helper methods
  InventoryItemModel? getSelectedInventoryItem() {
    if (selectedInventoryItemId.value.isEmpty) return null;
    try {
      return inventoryItems.firstWhere(
        (item) => item.id == selectedInventoryItemId.value,
      );
    } catch (e) {
      return null;
    }
  }

  MemberModel? getSelectedMember() {
    return selectedMember.value;
  }
}
