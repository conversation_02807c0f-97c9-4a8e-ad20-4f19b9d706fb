import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../controllers/group_controller.dart';
import '../../../../data/models/group_model.dart';
import '../../../../core/app/constants/routes.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
class GroupDetailScreen extends GetView<GroupController> {
  final String groupId;
  final GroupModel? group;

  const GroupDetailScreen({super.key, required this.groupId, this.group});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // If group is provided, set it directly; otherwise fetch by ID
    if (group != null) {
      // Set the group directly without fetching
      controller.selectedGroup.value = group;
    } else {
      // Only fetch if group is not provided
      controller.fetchGroupById(groupId);
    }

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Group Details',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading:
            Navigator.canPop(context)
                ? IconButton(
                  icon: const Icon(IconlyLight.arrowLeft2),
                  onPressed: () => Navigator.of(context).pop(),
                )
                : null,
        actions: [
          // Edit button
          Obx(() {
            final currentGroup = controller.selectedGroup.value;
            if (currentGroup != null) {
              return IconButton(
                icon: const Icon(IconlyLight.edit),
                onPressed: () {
                  context.go(
                    Routes.GROUP_EDIT.replaceFirst(
                      ':id',
                      currentGroup.id ?? '',
                    ),
                    extra: currentGroup,
                  );
                },
                tooltip: 'Edit Group',
              );
            }
            return const SizedBox.shrink();
          }),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.fetchGroupById(groupId),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Obx(() {
        // Only show loading if we don't have group data and are fetching
        if (group == null && controller.isLoadingGroupDetail.value) {
          return const Center(child: CircleLoadingAnimation());
        }

        final currentGroup = controller.selectedGroup.value;
        if (currentGroup == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(IconlyLight.document, size: 48),
                const SizedBox(height: 16),
                Text('Group not found', style: theme.textTheme.titleMedium),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Go back'),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Group Header
              _buildGroupHeader(context, currentGroup),
              const SizedBox(height: 24),

              // Group Description
              _buildSectionTitle(context, 'Description'),
              const SizedBox(height: 8),
              Text(
                currentGroup.description ?? 'No description available',
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 24),

              // Group Details
              _buildSectionTitle(context, 'Details'),
              const SizedBox(height: 8),
              _buildDetailItem(
                context,
                'Status',
                currentGroup.status ?? 'Unknown',
              ),
              _buildDetailItem(
                context,
                'Created',
                currentGroup.createdAt != null
                    ? DateFormat('MMM dd, yyyy').format(currentGroup.createdAt!)
                    : 'Unknown',
              ),
              _buildDetailItem(
                context,
                'Created By',
                '${currentGroup.createdByUser?.firstName ?? ''} ${currentGroup.createdByUser?.secondName ?? ''}',
              ),
              const SizedBox(height: 24),

              // Group Members
              _buildSectionTitle(context, 'Members'),
              const SizedBox(height: 8),
              _buildMembersList(context, currentGroup),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildGroupHeader(BuildContext context, GroupModel group) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Group Title
        Text(
          group.title ?? 'Unnamed Group',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),

        // Group Status Chip
        Chip(
          label: Text(
            group.status ?? 'Unknown',
            style: TextStyle(
              color: _getStatusColor(group.status ?? ''),
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: _getStatusColor(group.status ?? '').withOpacity(0.1),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    final theme = Theme.of(context);

    return Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
    );
  }

  Widget _buildDetailItem(BuildContext context, String label, String value) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ),
          Expanded(child: Text(value, style: theme.textTheme.bodyMedium)),
        ],
      ),
    );
  }

  Widget _buildMembersList(BuildContext context, GroupModel group) {
    final theme = Theme.of(context);

    if (group.members == null || group.members!.isEmpty) {
      return Text(
        'No members in this group',
        style: theme.textTheme.bodyMedium,
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: group.members!.length,
      itemBuilder: (context, index) {
        final member = group.members![index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: theme.colorScheme.primary.withOpacity(0.2),
            child: Text(
              member.member?.firstName?.substring(0, 1).toUpperCase() ?? 'U',
              style: TextStyle(color: theme.colorScheme.primary),
            ),
          ),
          title: Text(
            "${member.member?.firstName ?? ''} ${member.member?.secondName ?? ''}",
          ),
          subtitle: Text(member.role ?? 'Member'),
          contentPadding: EdgeInsets.zero,
        );
      },
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'inactive':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}
