import 'package:flutter/material.dart';
import 'text_styles.dart';
import 'icon_styles.dart';

class AppTheme {
  // Helper method for responsive padding
  static EdgeInsets _getResponsivePadding({
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    final width =
        MediaQueryData.fromView(WidgetsBinding.instance.window).size.width;
    final isTablet = width >= 768 && width < 1024;
    final isDesktop = width >= 1024;

    final padding =
        isDesktop && desktop != null
            ? desktop
            : isTablet && tablet != null
            ? tablet
            : mobile;

    return EdgeInsets.symmetric(vertical: padding, horizontal: padding);
  }

  static const Color primaryColor = Colors.red; // Color(0xFFD9E7F2);
  static const Color secondaryColor = Color(0xFF2A2A2A);
  static const Color accentColor = Color(0xFF4285F4);
  static const Color backgroundColor = Colors.white;
  static const Color errorColor = Color(0xFFE53935);

  // Dark theme colors
  static const Color primaryColorDark = Color(0xFF1F1F1F);
  static const Color secondaryColorDark = Color(0xFFD9E7F2);
  static const Color accentColorDark = Color(0xFF4285F4);
  static const Color backgroundColorDark = Color(0xFF121212);
  static const Color errorColorDark = Color(0xFFEF5350);

  static ThemeData lightTheme() {
    return ThemeData(
      primaryColor: primaryColor,
      colorScheme: ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        onPrimary: Colors.black,
        surface: backgroundColor,
        error: errorColor,
      ),
      scaffoldBackgroundColor: const Color(0xffF9FAFB),
      fontFamily: 'Alata',
      appBarTheme: const AppBarTheme(
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.black,
        titleTextStyle: TextStyle(
          fontFamily: 'Alata',
          color: Colors.black,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.black,
          textStyle: const TextStyle(
            fontFamily: 'Alata',
            fontWeight: FontWeight.w600,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: BorderSide(color: Colors.grey.shade400),
          ),
          padding: _getResponsivePadding(mobile: 15, tablet: 18, desktop: 20),
        ),
      ),
      textTheme: AppTextStyles.getLightTextTheme(),
      iconTheme: AppIconStyles.getLightIconTheme(),
    );
  }

  static ThemeData darkTheme() {
    return ThemeData(
      primaryColor: primaryColorDark,
      colorScheme: ColorScheme.dark(
        primary: primaryColorDark,
        secondary: secondaryColorDark,
        onPrimary: Colors.white,
        surface: backgroundColorDark,
        error: errorColorDark,
      ),
      scaffoldBackgroundColor: backgroundColorDark,
      fontFamily: 'Alata',
      iconTheme: AppIconStyles.getDarkIconTheme(),
      appBarTheme: const AppBarTheme(
        elevation: 0,
        backgroundColor: primaryColorDark,
        foregroundColor: Colors.white,
        titleTextStyle: TextStyle(
          fontFamily: 'Alata',
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: accentColorDark,
          foregroundColor: Colors.white,
          textStyle: const TextStyle(
            fontFamily: 'Alata',
            fontWeight: FontWeight.w600,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: BorderSide(color: Colors.grey.shade600),
          ),
          padding: _getResponsivePadding(mobile: 15, tablet: 18, desktop: 20),
        ),
      ),
      textTheme: AppTextStyles.getDarkTextTheme(),
    );
  }
}
