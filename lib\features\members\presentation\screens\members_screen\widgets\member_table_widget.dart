import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/features/members/controllers/member_controller.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../../../../../../core/app/constants/routes.dart';
import '../../../../../../../core/app/utils/show_toast.dart';
import '../../../../../../../data/models/member_model.dart';
import 'action_buttons_widget.dart';
import 'member_name_cell_widget.dart';
import 'status_chip_widget.dart';

class MemberTableWidget extends StatefulWidget {
  final bool isSelectMode;
  final Function(PlutoGridStateManager) onStateManagerCreated;
  final RxBool showBulkActions;

  const MemberTableWidget({
    super.key,
    required this.isSelectMode,
    required this.onStateManagerCreated,
    required this.showBulkActions,
  });

  @override
  State<MemberTableWidget> createState() => _MemberTableWidgetState();
}

class _MemberTableWidgetState extends State<MemberTableWidget> {
  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  final controller = Get.find<MemberController>();
  late PlutoGridStateManager stateManager;

  @override
  void initState() {
    super.initState();
    setColumns();
  }

  String _getCombinedPhoneNumbers(MemberModel member) {
    final primaryPhone = member.phoneNumber ?? '';
    final secondaryPhone = member.secondaryNumber ?? '';
    final email = member.email ?? '';

    List<String> contactInfo = [];

    if (primaryPhone.isNotEmpty) {
      contactInfo.add(primaryPhone);
    }
    if (secondaryPhone.isNotEmpty) {
      contactInfo.add(secondaryPhone);
    }
    if (email.isNotEmpty) {
      contactInfo.add(email);
    }

    return contactInfo.join('\n');
  }

  List<PlutoRow> setupRows(List<MemberModel> members) {
    return members.map((member) {
      final bool isSelected = controller.selectedMembers.any(
        (m) => m.id == member.id,
      );

      final Map<String, PlutoCell> cells = {
        "member": PlutoCell(value: member),
        'id': PlutoCell(value: member.id ?? ''),
        'account_number': PlutoCell(value: member.accountNumber ?? ''),
        'name': PlutoCell(
          value: '${member.firstName ?? ''} ${member.secondName ?? ''}',
        ),
        'phone': PlutoCell(value: _getCombinedPhoneNumbers(member)),
        'profile': PlutoCell(value: member.profileUrl ?? ''),
        'gender': PlutoCell(value: member.gender ?? ''),
        'marital_status': PlutoCell(value: member.maritalStatus ?? ''),
        'member_category': PlutoCell(
          value:
              member.memberCategory != null
                  ? (member.memberCategory is String
                      ? member.memberCategory
                      : member.memberCategory?.title ?? 'N/A')
                  : member.categoryName ?? 'N/A',
        ),
        'join_date': PlutoCell(
          value:
              member.joinDate != null
                  ? DateFormat('dd MMM yyyy').format(member.joinDate!)
                  : 'N/A',
        ),
        'status': PlutoCell(value: member.status ?? 'INACTIVE'),
        'actions': PlutoCell(value: member.id),
      };

      cells['select'] = PlutoCell(value: isSelected);

      return PlutoRow(checked: isSelected, cells: cells);
    }).toList();
  }

  void setColumns() {
    columns = [];

    columns.add(
      PlutoColumn(
        title: 'select',
        field: 'select',
        type: PlutoColumnType.text(),
        enableRowChecked: true,
        checkReadOnly: (row, cell) {
          return false;
        },
        width: 60,
        minWidth: 60,
      ),
    );

    columns.addAll([
      PlutoColumn(
        title: 'member',
        field: 'member',
        type: PlutoColumnType.text(),
        hide: true,
        width: 2,
      ),
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.text(),
        width: 100,
        minWidth: 80,
        renderer: (rendererContext) {
          return SelectableText(rendererContext.cell.value.toString());
        },
      ),
      PlutoColumn(
        title: 'Name',
        field: 'name',
        type: PlutoColumnType.text(),
        width: 200,
        minWidth: 150,
        renderer: (rendererContext) {
          return MemberNameCellWidget(rendererContext: rendererContext);
        },
      ),
      PlutoColumn(
        title: 'Contact Info',
        field: 'phone',
        type: PlutoColumnType.text(),
        width: 200,
        minWidth: 150,
        renderer: (rendererContext) {
          final contactInfo = rendererContext.cell.value.toString();
          return Container(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            child: Text(
              contactInfo,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Color.fromARGB(255, 64, 64, 64),
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Account #',
        field: 'account_number',
        type: PlutoColumnType.text(),
        width: 120,
        minWidth: 100,
      ),
      PlutoColumn(
        title: 'Gender',
        field: 'gender',
        type: PlutoColumnType.text(),
        width: 100,
        minWidth: 80,
      ),
      PlutoColumn(
        title: 'Marital Status',
        field: 'marital_status',
        type: PlutoColumnType.text(),
        width: 120,
        minWidth: 100,
      ),
      PlutoColumn(
        title: 'Member Category',
        field: 'member_category',
        type: PlutoColumnType.text(),
        width: 150,
        minWidth: 120,
      ),
      PlutoColumn(
        title: 'Join Date',
        field: 'join_date',
        type: PlutoColumnType.text(),
        width: 120,
        minWidth: 100,
      ),
      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
        minWidth: 80,
        renderer: (rendererContext) {
          return StatusChipWidget(rendererContext: rendererContext);
        },
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 120,
        minWidth: 100,
        renderer: (rendererContext) {
          return ActionButtonsWidget(rendererContext: rendererContext);
        },
      ),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Theme.of(context).secondaryHeaderColor,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      elevation: 4,
      child: PlutoGrid(
        mode:
            widget.isSelectMode
                ? PlutoGridMode.multiSelect
                : PlutoGridMode.selectWithOneTap,
        columns: columns,
        rows: rows,
        onLoaded: (PlutoGridOnLoadedEvent event) {
          stateManager = event.stateManager;
          widget.onStateManagerCreated(stateManager);
          event.stateManager.setShowColumnFilter(true);
          event.stateManager.setSelectingMode(PlutoGridSelectingMode.row);

          Future.microtask(() {
            stateManager.resetCurrentState();
            stateManager.notifyListeners();
          });

          if (kDebugMode) {
            debugPrint("Grid loaded");
          }
        },
        onSelected: (event) {
          if (event.row == null) return;

          final memberId = event.row!.cells['id']?.value as String?;
          if (memberId == null) return;
          MemberModel? selected =
              event.row!.cells['member']?.value as MemberModel?;

          if (event.cell?.column.field == 'select') {
            return;
          }

          if (widget.isSelectMode) {
            final isSelected = controller.selectedMembers.any(
              (m) => m.id == memberId,
            );

            if (!isSelected) {
              if (selected != null) {
                controller.selectedMembers.add(selected);

                stateManager.setRowChecked(event.row!, true);
              }
            } else {
              controller.selectedMembers.removeWhere((m) => m.id == memberId);

              stateManager.setRowChecked(event.row!, false);
            }

            stateManager.notifyListeners();
          } else {
            if (kDebugMode) {
              print('Navigating to member details with ID: $memberId');
            }
            context.go(Routes.VIEW_MEMBER.replaceFirst(':id', memberId));
          }
        },
        onRowDoubleTap: (PlutoGridOnRowDoubleTapEvent event) {
          if (widget.isSelectMode) return;

          final memberId = event.row.cells['id']?.value as String?;
          if (memberId == null) return;

          if (kDebugMode) {
            print('Double-tap navigating to member details with ID: $memberId');
          }
          context.go(Routes.VIEW_MEMBER.replaceFirst(':id', memberId));
        },
        configuration: PlutoGridConfiguration(
          enableMoveDownAfterSelecting: true,
          style: PlutoGridStyleConfig(
            activatedColor: const Color.fromARGB(255, 165, 205, 253),
            cellTextStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Color.fromARGB(255, 64, 64, 64),
            ),
            columnTextStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blueGrey,
            ),
            rowHeight: 45,
          ),
          columnSize: const PlutoGridColumnSizeConfig(
            autoSizeMode: PlutoAutoSizeMode.scale,
            resizeMode: PlutoResizeMode.normal,
          ),
          columnFilter: const PlutoGridColumnFilterConfig(
            filters: [...FilterHelper.defaultFilters],
          ),
        ),
        onRowChecked: (event) {
          Logger().f(event.row?.cells["member"]?.value);

          if (event.row == null) return;
          final memberId = event.row!.cells['id']?.value as String?;
          if (memberId == null) return;

          MemberModel? selected =
              event.row!.cells['member']?.value as MemberModel?;

          if (event.isChecked ?? false) {
            if (!controller.selectedMembers.any((m) => m.id == memberId)) {
              if (selected != null) {
                controller.selectedMembers.add(selected);
                stateManager.currentSelectingRows.add(event.row!);
              }
            }

            if (controller.selectedMembers.isNotEmpty &&
                !widget.showBulkActions.value &&
                !widget.isSelectMode) {
              widget.showBulkActions(true);
            }
          } else {
            controller.selectedMembers.removeWhere((m) => m.id == memberId);

            if (controller.selectedMembers.isEmpty &&
                widget.showBulkActions.value &&
                !widget.isSelectMode) {
              widget.showBulkActions(false);
            }
          }
        },
        createFooter: (stateManager) {
          return PlutoLazyPagination(
            initialPage: 0,
            initialFetch: true,
            fetchWithSorting: true,
            fetchWithFiltering: true,
            pageSizeToMove: null,
            fetch: (pagReq) async {
              controller.currentPage.value = pagReq.page;
              debugPrint("Fetching page: ${pagReq.page}");

              await controller.fetchMembers();
              if (controller.errorMessage.isNotEmpty) {
                ToastUtils.showErrorToast(controller.errorMessage.value, null);
              }
              return Future.value(
                PlutoLazyPaginationResponse(
                  totalPage: controller.totalPages.value,
                  rows: setupRows(controller.members),
                ),
              );
            },
            stateManager: stateManager,
          );
        },
      ),
    );
  }
}
