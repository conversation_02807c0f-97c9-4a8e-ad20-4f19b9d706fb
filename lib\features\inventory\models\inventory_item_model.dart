// To parse this JSON data, do
//
//     final inventoryItemModel = inventoryItemModelFromJson(jsonString);

import 'dart:convert';
import 'package:onechurch/features/media_upload/models/media_model.dart';

import 'unit_of_measure_model.dart';
import '../../../data/models/user_model.dart';

List<InventoryItemModel> inventoryItemModelFromJson(String str) =>
    List<InventoryItemModel>.from(
      json.decode(str).map((x) => InventoryItemModel.fromJson(x)),
    );

String inventoryItemModelToJson(List<InventoryItemModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class InventoryItemModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final dynamic itemCategoryId;
  final dynamic itemCategory;
  final UnitOfMeasureModel? unitOfMeasure;
  final String? unitOfMeasureId;
  final String? barcode;
  final String? description;
  final int? quantity;
  final String? organisationId;
  final dynamic organisation;
  final int? createdByUserId;
  final dynamic logs;
  final List<MediaModel>? media;
  final UserModel? createdByUser;

  InventoryItemModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.itemCategoryId,
    this.itemCategory,
    this.unitOfMeasure,
    this.unitOfMeasureId,
    this.barcode,
    this.description,
    this.quantity,
    this.organisationId,
    this.organisation,
    this.media,
    this.createdByUserId,
    this.logs,
    this.createdByUser,
  });

  InventoryItemModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? title,
    dynamic itemCategoryId,
    dynamic itemCategory,
    UnitOfMeasureModel? unitOfMeasure,
    String? unitOfMeasureId,
    String? barcode,
    String? description,
    int? quantity,
    String? organisationId,
    dynamic organisation,
    List<MediaModel>? media,
    int? createdByUserId,
    dynamic logs,
    UserModel? createdByUser,
  }) => InventoryItemModel(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    title: title ?? this.title,
    itemCategoryId: itemCategoryId ?? this.itemCategoryId,
    itemCategory: itemCategory ?? this.itemCategory,
    unitOfMeasure: unitOfMeasure ?? this.unitOfMeasure,
    unitOfMeasureId: unitOfMeasureId ?? this.unitOfMeasureId,
    barcode: barcode ?? this.barcode,
    description: description ?? this.description,
    quantity: quantity ?? this.quantity,
    organisationId: organisationId ?? this.organisationId,
    organisation: organisation ?? this.organisation,
    media: media ?? this.media,
    createdByUserId: createdByUserId ?? this.createdByUserId,
    logs: logs ?? this.logs,
    createdByUser: createdByUser ?? this.createdByUser,
  );

  factory InventoryItemModel.fromJson(
    Map<String, dynamic> json,
  ) => InventoryItemModel(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    title: json["title"],
    itemCategoryId: json["item_category_id"],
    itemCategory: json["item_category"],
    unitOfMeasure:
        json["unit_of_measure"] == null
            ? null
            : UnitOfMeasureModel.fromJson(json["unit_of_measure"]),
    unitOfMeasureId: json["unit_of_measure_id"],
    barcode: json["barcode"],
    description: json["description"],
    quantity: json["quantity"],
    organisationId: json["organisation_id"],
    organisation: json["organisation"],
    media:
        json["media"] == null
            ? []
            : List<MediaModel>.from(json["media"]!.map((x) => x)),
    createdByUserId: json["created_by_user_id"],
    logs: json["logs"],
    createdByUser:
        json["created_by_user"] == null
            ? null
            : UserModel.fromJson(json["created_by_user"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "title": title,
    "item_category_id": itemCategoryId,
    "item_category": itemCategory,
    "unit_of_measure": unitOfMeasure?.toJson(),
    "unit_of_measure_id": unitOfMeasureId,
    "barcode": barcode,
    "description": description,
    "quantity": quantity,
    "organisation_id": organisationId,
    "organisation": organisation,
    "media": media == null ? [] : List<MediaModel>.from(media!.map((x) => x)),
    "created_by_user_id": createdByUserId,
    "logs": logs,
    "created_by_user": createdByUser?.toJson(),
  };

  // Helper methods
  bool get hasMedia => media != null && media!.isNotEmpty;

  String? get firstMediaUrl => hasMedia ? media!.first.mediaUrl : null;

  String? get category => itemCategory?.toString();

  String get status => deletedAt == null ? 'active' : 'inactive';
}
