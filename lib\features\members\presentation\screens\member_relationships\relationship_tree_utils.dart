import 'package:flutter/material.dart';
import '../../../models/relationship_models.dart';

/// Relationship hierarchy levels
enum RelationshipHierarchyLevel {
  core, // Self, spouse, partner
  parent, // Parents, guardians
  child, // Children, wards, dependents
  sibling, // Brothers, sisters
  extended, // Grandparents, uncles, aunts
  distant, // Co<PERSON><PERSON>, friends, housemates
}

/// Cache for relationship hierarchy levels to avoid repeated string operations
final Map<String, RelationshipHierarchyLevel> _hierarchyCache = {};

/// Get hierarchy level for a relationship type (cached for performance)
RelationshipHierarchyLevel getRelationshipHierarchyLevel(
  String relationshipType,
) {
  // Check cache first
  if (_hierarchyCache.containsKey(relationshipType)) {
    return _hierarchyCache[relationshipType]!;
  }

  final type = relationshipType.toLowerCase();
  RelationshipHierarchyLevel level;

  // Core family
  if (type.contains('spouse') ||
      type.contains('partner') ||
      type.contains('husband') ||
      type.contains('wife')) {
    level = RelationshipHierarchyLevel.core;
  }
  // Parents
  else if (type.contains('father') ||
      type.contains('mother') ||
      type.contains('parent') ||
      type.contains('guardian')) {
    level = RelationshipHierarchyLevel.parent;
  }
  // Children
  else if (type.contains('son') ||
      type.contains('daughter') ||
      type.contains('child') ||
      type.contains('ward') ||
      type.contains('dependent')) {
    level = RelationshipHierarchyLevel.child;
  }
  // Siblings
  else if (type.contains('brother') ||
      type.contains('sister') ||
      type.contains('sibling')) {
    level = RelationshipHierarchyLevel.sibling;
  }
  // Extended family
  else if (type.contains('grandparent') ||
      type.contains('grandfather') ||
      type.contains('grandmother') ||
      type.contains('grandchild') ||
      type.contains('uncle') ||
      type.contains('aunt')) {
    level = RelationshipHierarchyLevel.extended;
  }
  // Distant relationships
  else if (type.contains('cousin') ||
      type.contains('friend') ||
      type.contains('housemate')) {
    level = RelationshipHierarchyLevel.distant;
  }
  // Default
  else {
    level = RelationshipHierarchyLevel.distant;
  }

  // Cache the result
  _hierarchyCache[relationshipType] = level;
  return level;
}

/// Cache for position offsets to avoid repeated calculations
final Map<String, Map<int, Offset>> _positionCache = {};

/// Get position offset based on relationship hierarchy (cached for performance)
/// Index is used to spread out multiple nodes of the same type
Offset getRelationshipPositionOffset(String relationshipType, int index) {
  // Check cache first
  final typeCache = _positionCache[relationshipType];
  if (typeCache != null && typeCache.containsKey(index)) {
    return typeCache[index]!;
  }

  final level = getRelationshipHierarchyLevel(relationshipType);
  const spacing =
      300.0; // Base spacing between nodes - increased for better visibility

  late Offset offset;

  switch (level) {
    case RelationshipHierarchyLevel.core:
      // Spouse/partner at the same level but to the right
      offset = Offset(spacing + (index * 50), 0);
      break;

    case RelationshipHierarchyLevel.parent:
      // Parents above, spread horizontally
      offset = Offset(-spacing + (index * spacing), -spacing * 1.2);
      break;

    case RelationshipHierarchyLevel.child:
      // Children below, spread horizontally
      offset = Offset(-spacing + (index * spacing), spacing * 1.2);
      break;

    case RelationshipHierarchyLevel.sibling:
      // Siblings to the right, spread vertically
      offset = Offset(spacing * 1.2, -spacing / 2 + (index * spacing / 2));
      break;

    case RelationshipHierarchyLevel.extended:
      // Extended family (grandparents, uncles, aunts) to the left
      offset = Offset(-spacing * 1.5, -spacing / 2 + (index * spacing / 2));
      break;

    case RelationshipHierarchyLevel.distant:
      // Distant relationships (cousins, friends) below and spread
      offset = Offset(-spacing + (index * spacing / 1.5), spacing * 1.8);
      break;
  }

  // Cache the result
  _positionCache.putIfAbsent(relationshipType, () => {})[index] = offset;
  return offset;
}

/// Cache for relationship colors to avoid repeated calculations
final Map<String, Color> _colorCache = {};

/// Get color for relationship type (cached for performance)
Color getRelationshipColor(
  BuildContext context,
  MemberRelationship relationship,
) {
  final category = relationship.relationshipType?.category?.toLowerCase() ?? '';
  final title = relationship.relationshipType?.title?.toLowerCase() ?? '';
  final cacheKey = '$category-$title';

  // Check cache first (excluding theme-dependent colors)
  if (_colorCache.containsKey(cacheKey) &&
      category.isNotEmpty &&
      title.isNotEmpty) {
    return _colorCache[cacheKey]!;
  }

  Color color;

  // Family relationships
  if (category.contains('family')) {
    if (title.contains('parent') ||
        title.contains('father') ||
        title.contains('mother')) {
      color = Colors.green.shade700;
    } else if (title.contains('child') ||
        title.contains('son') ||
        title.contains('daughter')) {
      color = Colors.green.shade400;
    } else if (title.contains('sibling') ||
        title.contains('brother') ||
        title.contains('sister')) {
      color = Colors.teal;
    } else if (title.contains('spouse') ||
        title.contains('husband') ||
        title.contains('wife')) {
      color = Colors.red;
    } else if (title.contains('grandparent') ||
        title.contains('grandfather') ||
        title.contains('grandmother')) {
      color = Colors.deepPurple;
    } else if (title.contains('grandchild')) {
      color = Colors.purple.shade300;
    } else if (title.contains('aunt') || title.contains('uncle')) {
      color = Colors.indigo;
    } else {
      color = Colors.green;
    }
  }
  // Friend relationships
  else if (category.contains('friend')) {
    color = Colors.blue;
  }
  // Work relationships
  else if (category.contains('work')) {
    color = Colors.orange;
  }
  // Church relationships
  else if (category.contains('church') || category.contains('ministry')) {
    color = Colors.deepOrange;
  }
  // Default color (theme-dependent, don't cache)
  else {
    return Theme.of(context).colorScheme.primary;
  }

  // Cache the result (only for non-theme-dependent colors)
  if (category.isNotEmpty && title.isNotEmpty) {
    _colorCache[cacheKey] = color;
  }

  return color;
}

/// Clear all caches (useful for memory management)
void clearRelationshipCaches() {
  _hierarchyCache.clear();
  _positionCache.clear();
  _colorCache.clear();
}
