import 'dart:io';
import 'package:csv/csv.dart';
import 'package:excel/excel.dart' as ex;
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle, ByteData;
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
// Conditional import for web platform
// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html if (dart.library.html) 'dart:html';
import '../../../data/models/sms_model.dart';
import '../../../data/models/group_model.dart';
import '../services/sms_service.dart';
import '../../../features/group/services/group_service.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
class SmsController extends GetxController {
  final SmsService _smsService = SmsService();
  final GroupService _groupService = GroupService();
  final logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<SmsModel> messages = <SmsModel>[].obs;
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalItems = 0.obs;
  final RxInt pageSize = 20.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;
  final RxString searchQuery = ''.obs;
  final TextEditingController searchController = TextEditingController();
  final RxBool isSending = false.obs;

  // Message form controllers
  final TextEditingController messageController = TextEditingController();
  final RxList<String> recipients = <String>[].obs;
  final TextEditingController recipientController = TextEditingController();

  // Group selection
  final RxList<String> selectedGroups = <String>[].obs;
  final RxList<GroupModel> availableGroups = <GroupModel>[].obs;
  final RxBool isLoadingGroups = false.obs;

  // Filter variables
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);
  final RxString phoneNumberFilter = ''.obs;
  final RxString messageFilter = ''.obs;
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController messageFilterController = TextEditingController();

  // Toggle states for UI sections
  final RxBool showDashboard = false.obs;
  final RxBool showFilters = false.obs;

  @override
  void onInit() {
    super.onInit();
    fetchMessages();
    fetchGroups();
  }

  @override
  void onClose() {
    searchController.dispose();
    messageController.dispose();
    recipientController.dispose();
    phoneNumberController.dispose();
    messageFilterController.dispose();
    super.onClose();
  }

  // Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    currentPage.value = 0; // Reset to first page when searching
    fetchMessages();
  }

  // Set date filters
  void setDateFilters(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0; // Reset to first page when filtering
    fetchMessages();
  }

  // Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    searchController.clear();
    startDate.value = null;
    endDate.value = null;
    phoneNumberFilter.value = '';
    phoneNumberController.clear();
    messageFilter.value = '';
    messageFilterController.clear();
    currentPage.value = 0;
    fetchMessages();
  }

  // Toggle methods for UI sections
  void toggleDashboard() {
    showDashboard.value = !showDashboard.value;
  }

  void toggleFilters() {
    showFilters.value = !showFilters.value;
  }

  // Toggle all sections at once
  void toggleAllSections() {
    final anyVisible = showDashboard.value || showFilters.value;
    final newState = !anyVisible;

    showDashboard.value = newState;
    showFilters.value = newState;
  }

  // Check if any section is visible
  bool get hasVisibleSections => showDashboard.value || showFilters.value;

  // Navigate to next page
  void nextPage() {
    if (!isLastPage.value) {
      currentPage.value++;
      fetchMessages();
    }
  }

  // Navigate to previous page
  void previousPage() {
    if (currentPage.value > 0) {
      currentPage.value--;
      fetchMessages();
    }
  }

  // Add recipient
  void addRecipient(String phone) {
    if (phone.isNotEmpty && !recipients.contains(phone)) {
      recipients.add(phone);
      recipientController.clear();
    }
  }

  // Add group
  void addGroup(String groupId) {
    if (!selectedGroups.contains(groupId)) {
      selectedGroups.add(groupId);
      // Extract phone numbers from the selected group and add to recipients
      extractPhoneNumbersFromGroup(groupId);
    }
  }

  // Remove group
  void removeGroup(String groupId) {
    selectedGroups.remove(groupId);
    // Refresh recipients list after removing a group
    refreshRecipientsFromGroups();
  }

  // Extract phone numbers from a group
  void extractPhoneNumbersFromGroup(String groupId) {
    final group = availableGroups.firstWhere(
      (g) => g.id == groupId,
      orElse: () => GroupModel(),
    );

    if (group.members != null) {
      for (var member in group.members!) {
        if (member.member?.phoneNumber != null &&
            member.member!.phoneNumber!.isNotEmpty) {
          addRecipient(member.member!.phoneNumber!);
        }
      }
    }
  }

  // Refresh recipients list based on selected groups
  void refreshRecipientsFromGroups() {
    // Clear current recipients
    recipients.clear();

    // Re-add recipients from all selected groups
    for (var groupId in selectedGroups) {
      extractPhoneNumbersFromGroup(groupId);
    }
  }

  // Fetch available groups
  Future<void> fetchGroups() async {
    isLoadingGroups.value = true;

    try {
      final response = await _groupService.fetchGroups(page: 0, size: 100);

      if (response["status"] ?? false) {
        final data = response["data"];
        final List<dynamic> items = data["items"] ?? [];
        availableGroups.value = _groupService.parseGroups(items);
        availableGroups.refresh();
      }
    } catch (e) {
      logger.e(e);
    } finally {
      isLoadingGroups.value = false;
    }
  }

  // Remove recipient
  void removeRecipient(String phone) {
    recipients.remove(phone);
  }

  // Update fetch messages to handle loading state properly
  Future<void> fetchMessages() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _smsService.fetchMessages(
        page: currentPage.value,
        size: pageSize.value,
        searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
        startDate: startDate.value,
        endDate: endDate.value,
        phoneNumber:
            phoneNumberFilter.value.isEmpty ? null : phoneNumberFilter.value,
        message: messageFilter.value.isEmpty ? null : messageFilter.value,
        organisationId: Get.find<AuthController>().currentOrg.value?.id,
      );

      if (response["status"] ?? false) {
        final data = response["data"];

        // Parse pagination data
        currentPage.value = data["page"] ?? 0;
        pageSize.value = data["size"] ?? 10;
        totalPages.value = data["total_pages"] ?? 0;
        totalItems.value = data["total"] ?? 0;
        isLastPage.value = data["last"] ?? false;
        isFirstPage.value = data["first"] ?? true;

        // Parse SMS items
        final List<dynamic> items = data["items"] ?? [];
        messages.value = _smsService.parseMessages(items);
        messages.refresh();
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to fetch SMS messages';
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to fetch SMS messages: ${e.toString()}';
    } finally {
      isLoading.value = false;
      update(); // Ensure UI updates
    }
  }

  // Refresh messages
  Future<void> refreshMessages() async {
    currentPage.value = 0;
    await fetchMessages();
  }

  // Send SMS preview
  Future<Map<String, dynamic>> sendSmsPreview(
    String message,
    List<String> phoneNumbers,
  ) async {
    isSending.value = true;
    try {
      // Ensure we have recipients
      if (phoneNumbers.isEmpty && recipients.isNotEmpty) {
        phoneNumbers = recipients.toList();
      }

      // Validate phone numbers
      if (phoneNumbers.isEmpty) {
        return {
          "status": false,
          "message":
              "No recipients selected. Please add recipients or select a group.",
        };
      }

      final response = await _smsService.sendSmsPreview(
        message: message,
        recipientsPhone: phoneNumbers,
        organisationId: Get.find<AuthController>().currentOrg.value?.id,
      );
      return response;
    } catch (e) {
      logger.e(e);
      return {
        "status": false,
        "message": "Failed to send SMS preview: ${e.toString()}",
      };
    } finally {
      isSending.value = false;
    }
  }

  // Send actual SMS
  Future<Map<String, dynamic>> sendSms(
    String message,
    List<String> phoneNumbers,
  ) async {
    isSending.value = true;
    try {
      // Ensure we have recipients
      if (phoneNumbers.isEmpty && recipients.isNotEmpty) {
        phoneNumbers = recipients.toList();
      }

      // Validate phone numbers
      if (phoneNumbers.isEmpty) {
        return {
          "status": false,
          "message":
              "No recipients selected. Please add recipients or select a group.",
        };
      }

      final response = await _smsService.sendSms(
        message: message,
        recipientsPhone: phoneNumbers,
        organisationId: Get.find<AuthController>().currentOrg.value?.id,
      );
      if (response["status"] ?? false) {
        // Clear form after successful send
        messageController.clear();
        recipients.clear();
        selectedGroups.clear();
        // Refresh messages list
        await refreshMessages();
      }
      return response;
    } catch (e) {
      logger.e(e);
      return {
        "status": false,
        "message": "Failed to send SMS: ${e.toString()}",
      };
    } finally {
      isSending.value = false;
    }
  }

  /// Downloads the sample CSV template for contacts import.
  ///
  /// This provides users with a properly formatted template to fill with their contacts.
  /// The template includes columns for First Name, Last Name, Phone Number, Email, and Address.
  Future<void> downloadSampleCsvTemplate(BuildContext context) async {
    try {
      // Get the template from assets
      final ByteData data = await rootBundle.load(
        'assets/templates/contacts_template.csv',
      );
      final List<int> bytes = data.buffer.asUint8List();

      // Save the file (implementation depends on platform)
      if (kIsWeb) {
        // For web platform
        final blob = html.Blob([bytes]);
        final url = html.Url.createObjectUrlFromBlob(blob);
        html.AnchorElement(href: url)
          ..setAttribute('download', 'contacts_template.csv')
          ..click();
        html.Url.revokeObjectUrl(url);
      } else {
        // For mobile platforms
        final directory = await getApplicationDocumentsDirectory();
        final path = '${directory.path}/contacts_template.csv';
        final file = File(path);
        await file.writeAsBytes(bytes);
        await Share.shareXFiles([
          XFile(path),
        ], text: 'Contacts Import Template');
      }

      ToastUtils.showSuccessToast(
        "Template downloaded successfully",
        "Success",
      );
    } catch (e) {
      logger.e("Error downloading template: $e");
      ToastUtils.showErrorToast("Error downloading template: $e", "Error");
    }
  }

  /// Picks CSV/XLSX file using FilePicker, parses, and imports contacts.
  ///
  /// CSV Format Requirements:
  /// - First row should be a header row with column names
  /// - Phone numbers should be in the third column (index 2)
  /// - Phone numbers should include country code (e.g., +254712345678)
  ///
  /// XLSX Format Requirements:
  /// - First row should be a header row with column names
  /// - Phone numbers should be in the third column (index 2)
  /// - Phone numbers should include country code (e.g., +254712345678)
  ///
  /// You can download a sample template using the 'Download Template' button.
  Future<void> pickFileAndImportContacts(BuildContext context) async {
    try {
      // Show loading indicator
      _showLoadingDialog(context, 'Processing file...');

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv', 'xlsx'],
        withData: true, // Important for web & memory
      );

      // Close loading dialog if user canceled
      if (result == null) {
        Navigator.of(context, rootNavigator: true).pop();
        return; // user canceled
      }

      final bytes = result.files.first.bytes;
      final filename = result.files.first.name;
      final extension = result.files.first.extension?.toLowerCase();

      if (bytes == null) {
        Navigator.of(
          context,
          rootNavigator: true,
        ).pop(); // Close loading dialog
        ToastUtils.showErrorToast("Could not read file", "Error");
        return;
      }

      // Statistics for import results
      int importedCount = 0;
      int invalidCount = 0;
      int duplicateCount = 0;
      List<String> invalidNumbers = [];

      // Process based on file type
      if (extension == 'csv') {
        final csvString = String.fromCharCodes(bytes);
        final fields = const CsvToListConverter().convert(csvString);

        // Validate file structure
        if (fields.isEmpty) {
          Navigator.of(
            context,
            rootNavigator: true,
          ).pop(); // Close loading dialog
          ToastUtils.showErrorToast("CSV file is empty", "Error");
          return;
        }

        // Skip the header row
        for (var row in fields.skip(1)) {
          if (row.length >= 3 && row[2] != null) {
            String phoneNumber = _formatPhoneNumber(row[2].toString().trim());

            // Validate phone number
            if (phoneNumber.isEmpty) {
              invalidCount++;
              if (invalidNumbers.length < 5) {
                // Limit to 5 examples
                invalidNumbers.add(row[2].toString().trim());
              }
              continue;
            }

            // Check for duplicates
            if (recipients.contains(phoneNumber)) {
              duplicateCount++;
              continue;
            }

            // Add valid recipient
            addRecipient(phoneNumber);
            importedCount++;
          } else {
            invalidCount++;
          }
        }
        // If XLSX
      } else if (extension == 'xlsx') {
        var excel = ex.Excel.decodeBytes(bytes);
        if (excel.tables.isEmpty) {
          Navigator.of(
            context,
            rootNavigator: true,
          ).pop(); // Close loading dialog
          ToastUtils.showErrorToast("Excel file has no sheets", "Error");
          return;
        }

        var sheet = excel.tables[excel.tables.keys.first]!;

        // Validate sheet has data
        if (sheet.rows.length <= 1) {
          Navigator.of(
            context,
            rootNavigator: true,
          ).pop(); // Close loading dialog
          ToastUtils.showErrorToast(
            "Excel sheet is empty or has only headers",
            "Error",
          );
          return;
        }

        // Skip header row
        for (var row in sheet.rows.skip(1)) {
          if (row.length >= 3 && row[2]?.value != null) {
            String phoneNumber = _formatPhoneNumber(
              row[2]?.value.toString().trim() ?? '',
            );

            // Validate phone number
            if (phoneNumber.isEmpty) {
              invalidCount++;
              if (invalidNumbers.length < 5) {
                // Limit to 5 examples
                invalidNumbers.add(row[2]?.value.toString().trim() ?? '');
              }
              continue;
            }

            // Check for duplicates
            if (recipients.contains(phoneNumber)) {
              duplicateCount++;
              continue;
            }

            // Add valid recipient
            addRecipient(phoneNumber);
            importedCount++;
          } else {
            invalidCount++;
          }
        }
      }

      // Close loading dialog
      Navigator.of(context, rootNavigator: true).pop();

      // Show detailed results dialog
      _showImportResultsDialog(
        context,
        importedCount,
        invalidCount,
        duplicateCount,
        invalidNumbers,
        filename,
      );
    } catch (e) {
      // Close loading dialog if open
      try {
        Navigator.of(context, rootNavigator: true).pop();
      } catch (_) {}

      logger.e("Error processing file: $e");
      ToastUtils.showErrorToast("Error processing file: $e", "Error");
    }
  }

  /// Shows a loading dialog while processing the file
  AlertDialog _showLoadingDialog(BuildContext context, String message) {
    AlertDialog dialog = AlertDialog(
      content: Row(
        children: [
          CircleLoadingAnimation(),
          SizedBox(width: 20),
          Text(message),
        ],
      ),
    );

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => dialog,
    );

    return dialog;
  }

  /// Shows a dialog with detailed import results
  void _showImportResultsDialog(
    BuildContext context,
    int importedCount,
    int invalidCount,
    int duplicateCount,
    List<String> invalidExamples,
    String filename,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(
                  importedCount > 0 ? Icons.check_circle : Icons.warning,
                  color: importedCount > 0 ? Colors.green : Colors.orange,
                ),
                SizedBox(width: 8),
                Text('Import Results'),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'File: $filename',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 16),
                  _buildResultItem(
                    Icons.person_add,
                    '$importedCount contacts imported successfully',
                    Colors.green,
                  ),
                  _buildResultItem(
                    Icons.person_off,
                    '$invalidCount invalid phone numbers',
                    Colors.orange,
                  ),
                  _buildResultItem(
                    Icons.copy,
                    '$duplicateCount duplicate numbers skipped',
                    Colors.blue,
                  ),

                  if (invalidExamples.isNotEmpty) ...[
                    SizedBox(height: 16),
                    Text(
                      'Examples of invalid numbers:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Container(
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children:
                            invalidExamples
                                .map((number) => Text('• $number'))
                                .toList(),
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Tip: Phone numbers should include country code (e.g., +254712345678)',
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Close'),
              ),
              if (importedCount > 0)
                CustomButton(
                  icon: Icon(Icons.message),
                  label: Text('Continue with ${recipients.length} recipients'),
                  onPressed: () => Navigator.pop(context),
                ),
            ],
          ),
    );
  }

  /// Helper to build a result item row with icon
  Widget _buildResultItem(IconData icon, String text, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          SizedBox(width: 8),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }

  /// Formats and validates a phone number
  /// Returns empty string if invalid
  String _formatPhoneNumber(String input) {
    // Remove all non-digit characters except the plus sign
    String cleaned = input.replaceAll(RegExp(r'[^\d+]'), '');

    // If doesn't start with +, try to add country code
    if (!cleaned.startsWith('+')) {
      // If it starts with a leading zero, assume it's a local number and add +254
      if (cleaned.startsWith('0') && cleaned.length >= 10) {
        cleaned = '+254${cleaned.substring(1)}';
      }
      // If it's just digits and of reasonable length (at least 9 digits), assume it needs +
      else if (cleaned.length >= 9) {
        cleaned = '+$cleaned';
      } else {
        return ''; // Invalid number
      }
    }

    // Final validation - should be at least 11 chars (+ and 10 digits minimum)
    if (cleaned.length < 11) {
      return '';
    }

    return cleaned;
  }

  // Set phone number filter
  void setPhoneNumberFilter(String value) {
    phoneNumberFilter.value = value;
    currentPage.value = 0; // Reset to first page when filtering
    fetchMessages();
  }

  // Set message filter
  void setMessageFilter(String value) {
    messageFilter.value = value;
    currentPage.value = 0; // Reset to first page when filtering
    fetchMessages();
  }
}
