import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/features/members/controllers/member_controller.dart';
import 'package:pluto_grid/pluto_grid.dart';

class BulkActionsWidget extends StatelessWidget {
  final VoidCallback onClose;
  final PlutoGridStateManager? stateManager;

  const BulkActionsWidget({
    super.key,
    required this.onClose,
    required this.stateManager,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MemberController>();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      color: Theme.of(context).primaryColor.withOpacity(0.1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                "${controller.selectedMembers.length} members selected",
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: onClose,
                tooltip: 'Close',
              ),
            ],
          ),
          const Divider(),
          Wrap(
            spacing: 8.0,
            runSpacing: 8.0,
            children: [
              _buildActionChip(
                context: context,
                icon: Icons.campaign,
                label: 'Send Announcement',
                iconColor: Colors.blue,
                onPressed: () {
                  _handleSendAnnouncement(context);
                  onClose();
                },
              ),
              _buildActionChip(
                context: context,
                icon: Icons.message,
                label: 'Send Message',
                iconColor: Colors.green,
                onPressed: () {
                  _handleSendMessage(context);
                  onClose();
                },
              ),
              _buildActionChip(
                context: context,
                icon: Icons.group_add,
                label: 'Add to Group',
                iconColor: Colors.purple,
                onPressed: () {
                  _handleAddToGroup(context);
                  onClose();
                },
              ),
              _buildActionChip(
                context: context,
                icon: Icons.edit,
                label: 'Bulk Edit',
                iconColor: Colors.orange,
                onPressed: () {
                  _handleBulkEdit(context);
                  onClose();
                },
              ),
              _buildActionChip(
                context: context,
                icon: Icons.delete,
                label: 'Delete',
                iconColor: Colors.red,
                onPressed: () {
                  _handleDelete(context, onClose);
                },
              ),
              _buildActionChip(
                context: context,
                icon: Icons.clear_all,
                label: 'Clear Selection',
                iconColor: Colors.grey,
                onPressed: () {
                  final controller = Get.find<MemberController>();
                  controller.selectedMembers.clear();
                  stateManager?.notifyListeners();
                  onClose();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionChip({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color iconColor,
    required VoidCallback onPressed,
  }) {
    return ActionChip(
      avatar: Icon(icon, color: iconColor),
      label: Text(label),
      onPressed: onPressed,
    );
  }

  void _handleSendAnnouncement(BuildContext context) {
    final controller = Get.find<MemberController>();
    if (kDebugMode) {
      debugPrint(
        'Send announcement to ${controller.selectedMembers.length} members',
      );
    }
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Sending announcement to ${controller.selectedMembers.length} members',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleSendMessage(BuildContext context) {
    final controller = Get.find<MemberController>();
    if (kDebugMode) {
      debugPrint(
        'Send message to ${controller.selectedMembers.length} members',
      );
    }
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Sending message to ${controller.selectedMembers.length} members',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleAddToGroup(BuildContext context) {
    final controller = Get.find<MemberController>();
    if (kDebugMode) {
      debugPrint('Add ${controller.selectedMembers.length} members to group');
    }
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Adding ${controller.selectedMembers.length} members to group',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleBulkEdit(BuildContext context) {
    final controller = Get.find<MemberController>();
    if (kDebugMode) {
      debugPrint('Bulk edit ${controller.selectedMembers.length} members');
    }
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Editing ${controller.selectedMembers.length} members'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleDelete(BuildContext context, VoidCallback onClose) {
    final controller = Get.find<MemberController>();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirm Delete'),
          content: Text(
            'Are you sure you want to delete ${controller.selectedMembers.length} members?',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                if (kDebugMode) {
                  debugPrint(
                    'Delete ${controller.selectedMembers.length} members',
                  );
                }
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Deleted ${controller.selectedMembers.length} members',
                    ),
                    backgroundColor: Colors.red,
                    duration: const Duration(seconds: 2),
                  ),
                );
                controller.selectedMembers.clear();
                onClose();
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}
