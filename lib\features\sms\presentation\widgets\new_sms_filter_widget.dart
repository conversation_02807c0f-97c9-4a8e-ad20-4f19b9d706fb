import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import '../../controllers/sms_controller.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
class NewSmsFilterWidget extends StatelessWidget {
  const NewSmsFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SmsController>();
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(IconlyLight.filter, color: colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Filter SMS Messages',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: controller.clearFilters,
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.clear_all, size: 18),
                      const SizedBox(width: 4),
                      Text(
                        'Clear All',
                        style: Theme.of(context).textTheme.labelMedium,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Divider(height: 24),
            SizedBox(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    SizedBox(
                      width: 180.w,
                      child: CustomTextFormField(
                        controller: controller.messageFilterController,
                        labelText: 'Search Messages',
                        hintText: 'Message content...',
                        prefixIcon: const Icon(IconlyLight.search),
                        onChanged:
                            (value) => controller.messageFilter.value = value,
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 150.w,
                      child: CustomTextFormField(
                        controller: controller.phoneNumberController,
                        labelText: 'Phone Number',
                        hintText: 'Filter by phone...',
                        prefixIcon: const Icon(IconlyLight.call),
                        onChanged:
                            (value) =>
                                controller.phoneNumberFilter.value = value,
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 110.w,
                      child: _buildDateField(
                        context,
                        controller,
                        isStartDate: true,
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 110.w,
                      child: _buildDateField(
                        context,
                        controller,
                        isStartDate: false,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: CustomButton(
                onPressed: controller.fetchMessages,
                icon: const Icon(IconlyLight.filter),
                label: const Text('Apply Filters'),
               
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField(
    BuildContext context,
    SmsController controller, {
    required bool isStartDate,
  }) {
    return Obx(() {
      final date =
          isStartDate ? controller.startDate.value : controller.endDate.value;
      final label = isStartDate ? 'Start Date' : 'End Date';
      final icon = IconlyLight.calendar;

      return CustomTextFormField(
        readOnly: true,
        labelText: label,
        prefixIcon: Icon(icon),
        hintText: 'Select $label',
        controller: TextEditingController(
          text: date != null ? DateFormat('MMM dd, yyyy').format(date) : null,
        ),
        onTap:
            () =>
                isStartDate
                    ? _selectStartDate(context, controller)
                    : _selectEndDate(context, controller),
      );
    });
  }

  // Select start date
  Future<void> _selectStartDate(
    BuildContext context,
    SmsController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.startDate.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      // Set to start of day for inclusive filtering
      final startOfDay = DateTime(
        picked.year,
        picked.month,
        picked.day,
        0,
        0,
        0,
      );
      controller.startDate.value = startOfDay;
    }
  }

  // Select end date
  Future<void> _selectEndDate(
    BuildContext context,
    SmsController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.endDate.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      // Set to end of day for inclusive filtering
      final endOfDay = DateTime(
        picked.year,
        picked.month,
        picked.day,
        23,
        59,
        59,
      );
      controller.endDate.value = endOfDay;
    }
  }
}
