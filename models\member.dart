/// Model class representing a member/contact for SMS operations
class Member {
  final String id;
  final String name;
  final String phoneNumber;
  final String? email;
  final String entityType;
  final Map<String, dynamic>? additionalData;

  Member({
    required this.id,
    required this.name,
    required this.phoneNumber,
    this.email,
    required this.entityType,
    this.additionalData,
  });

  /// Create Member from JSON
  factory Member.fromJson(Map<String, dynamic> json) {
    return Member(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      phoneNumber: json['phone_number']?.toString() ?? json['phoneNumber']?.toString() ?? '',
      email: json['email']?.toString(),
      entityType: json['entity_type']?.toString() ?? json['entityType']?.toString() ?? 'PHONE_NUMBER',
      additionalData: json['additional_data'] as Map<String, dynamic>?,
    );
  }

  /// Convert Member to JSON
  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'id': id,
      'name': name,
      'phone_number': phoneNumber,
      'email': email,
      'entity_type': entityType,
      'additional_data': additionalData,
    };
  }

  /// Create a copy of Member with updated fields
  Member copyWith({
    String? id,
    String? name,
    String? phoneNumber,
    String? email,
    String? entityType,
    Map<String, dynamic>? additionalData,
  }) {
    return Member(
      id: id ?? this.id,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      entityType: entityType ?? this.entityType,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  @override
  String toString() {
    return 'Member(id: $id, name: $name, phoneNumber: $phoneNumber, entityType: $entityType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Member &&
        other.id == id &&
        other.phoneNumber == phoneNumber;
  }

  @override
  int get hashCode => id.hashCode ^ phoneNumber.hashCode;
}
