import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/utils/currency_format.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/core/app/utils/size_config.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/features/finances/controllers/transactions_controller.dart';
import 'package:onechurch/features/finances/models/transactions_model.dart';
import 'package:onechurch/features/finances/presentation/widgets/transaction_filter_widget.dart';
import 'package:pluto_grid/pluto_grid.dart';

class TransactionsView extends StatefulWidget {
  const TransactionsView({super.key});

  @override
  State<TransactionsView> createState() => _TransactionsViewState();
}

class _TransactionsViewState extends State<TransactionsView> {
  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  List<PlutoRow> setupRows(List<TransactionsModel> acct) {
    return acct.map((sermon) {
      return PlutoRow(
        cells: {
          'date': PlutoCell(
            value:
                sermon.createdAt != null
                    ? DateFormat(
                      'dd MMM yyyy HH:mm a',
                    ).format(sermon.createdAt!)
                    : 'N/A',
          ),
          'code': PlutoCell(
            value:
                "${sermon.transactionCode ?? ""}\n${sermon.transactionCodeOther ?? ""}",
          ),
          'reference': PlutoCell(value: sermon.internalId ?? ''),
          'amount': PlutoCell(value: sermon.amount),
          'status': PlutoCell(value: sermon.status),
          'phone': PlutoCell(
            value:
                sermon.phoneNumber != sermon.accountNumber
                    ? "${sermon.phoneNumber ?? ""}\n${sermon.accountNumber ?? ""} ${sermon.accountNumberRef ?? ''}}"
                    : sermon.phoneNumber ?? "",
          ),
          'name': PlutoCell(
            value: "${sermon.firstName ?? ""} ${sermon.secondName ?? ""}",
          ),
          'account': PlutoCell(value: sermon.subAccount?.account),

          'member': PlutoCell(
            value:
                "${sermon.member?.accountNumber ?? ''}\n${sermon.member?.firstName ?? ''} ${sermon.member?.secondName ?? ''}",
          ),
        },
      );
    }).toList();
  }

  setColumns() {
    // Initialize columns
    columns = [
      PlutoColumn(
        title: 'Date',
        field: 'date',
        type: PlutoColumnType.text(),
        width: 120,
        enableRowChecked: true,
      ),
      PlutoColumn(
        title: 'Transaction Code',
        field: 'code',
        type: PlutoColumnType.text(),
        width: 100,
      ),
      PlutoColumn(
        title: 'Reference',
        field: 'reference',
        type: PlutoColumnType.text(),
        width: 100,
      ),
      PlutoColumn(
        title: 'Amount',
        field: 'amount',
        type: PlutoColumnType.text(),
        width: 80,
      ),

      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final status = rendererContext.row.cells['status']?.value;
          return Chip(
            backgroundColor:
                status == "SUCCESS"
                    ? Colors.green.shade100.withValues(alpha: 0.2)
                    : Colors.red.shade100.withValues(alpha: 0.2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
              side: BorderSide(
                color: status == "SUCCESS" ? Colors.green : Colors.red,
                width: 0.6,
              ),
            ),
            label: Text(
              status.toString(),
              style: TextStyle(
                color: status == "SUCCESS" ? Colors.green : Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Phone Number',
        field: 'phone',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Name',
        field: 'name',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Member',
        field: 'member',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Org Account',
        field: 'account',
        type: PlutoColumnType.text(),
        width: 120,
      ),
    ];
  }

  TransactionsController controll = Get.find<TransactionsController>();

  @override
  void initState() {
    setColumns();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final showDashboard = false.obs;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Transactions'),
        actions: [
          if (MediaQuery.of(context).size.width > 600) ...[
            SizedBox(
              width: 180.w,
              child: CustomTextField(
                controller: controll.searchController,
                labelText: 'Search Transactions',
                hintText: 'Name, Code, Phone...',
                prefixIcon: const Icon(IconlyLight.search),

                onSubmitted: (value) {
                  controll.setSearchQuery(value);
                },
              ),
            ),
            const SizedBox(width: 8),
          ],
          Obx(
            () => IconButton(
              onPressed: () {
                showDashboard(!showDashboard.value);
              },
              icon: Icon(
                showDashboard.value
                    ? Icons.filter_alt
                    : Icons.filter_alt_outlined,
              ),
            ),
          ),
        ],
      ),
      body: SizedBox(
        height: SizeConfig.screenHeight,
        child: Column(
          children: [
            // Filter dashboard
            Obx(
              () =>
                  showDashboard.value
                      ? const TransactionFilterWidget()
                      : const SizedBox(),
            ),

            // Summary card with totals
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Card(
                color: Theme.of(context).secondaryHeaderColor,
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        IconlyLight.activity,
                        color: Theme.of(context).primaryColor,
                      ),
                      const SizedBox(width: 12),
                      Obx(
                        () => Row(
                          children: [
                            Text(
                              "Amount In: ${FormattedCurrency.getFormattedCurrency(controll.totalIn)}",
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 24),
                            Text(
                              "Amount Out: ${FormattedCurrency.getFormattedCurrency(controll.totalOut)}",
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Expanded(
              child: Card(
                color: Theme.of(context).secondaryHeaderColor,
                margin: const EdgeInsets.all(12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                elevation: 4,
                child: PlutoGrid(
                  mode: PlutoGridMode.selectWithOneTap,
                  columns: columns,
                  rows: rows,
                  onLoaded: (PlutoGridOnLoadedEvent event) {
                    event.stateManager.setShowColumnFilter(true);
                    if (kDebugMode) {
                      debugPrint("onLoaded: $event");
                    }
                  },
                  onSelected: (event) {
                    if (kDebugMode) {
                      debugPrint("onSelected: $event");
                    }
                  },
                  configuration: PlutoGridConfiguration(
                    style: PlutoGridStyleConfig(
                      // activatedBorderColor: Colors.transparent,
                      activatedColor: Color.fromARGB(255, 165, 205, 253),
                      // gridBorderColor: Colors.transparent,
                      cellTextStyle: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Color.fromARGB(255, 216, 108, 40),
                      ),
                      columnTextStyle: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blueGrey,
                      ),
                    ),
                    columnSize: PlutoGridColumnSizeConfig(
                      autoSizeMode: PlutoAutoSizeMode.scale,
                    ),
                  ),
                  createFooter: (stateManager) {
                    return PlutoLazyPagination(
                      // Determine the first page.
                      // Default is 1.
                      initialPage: 0,

                      // First call the fetch function to determine whether to load the page.
                      // Default is true.
                      initialFetch: true,

                      // Decide whether sorting will be handled by the server.
                      // If false, handle sorting on the client side.
                      // Default is true.
                      fetchWithSorting: true,

                      // Decide whether filtering is handled by the server.
                      // If false, handle filtering on the client side.
                      // Default is true.
                      fetchWithFiltering: true,

                      // Determines the page size to move to the previous and next page buttons.
                      // Default value is null. In this case,
                      // it moves as many as the number of page buttons visible on the screen.
                      pageSizeToMove: null,
                      fetch: (pagReq) async {
                        controll.currentPage.value = pagReq.page;
                        debugPrint("fetch page: ${pagReq.page}");

                        await controll.getTransactions();
                        if (controll.apiStatus.isFalse) {
                          ToastUtils.showErrorToast(
                            controll.message.value,
                            null,
                          );
                        }

                        return Future.value(
                          PlutoLazyPaginationResponse(
                            totalPage: controll.totalPages.value,
                            rows: setupRows(controll.transactions),
                          ),
                        );
                      },
                      stateManager: stateManager,
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
