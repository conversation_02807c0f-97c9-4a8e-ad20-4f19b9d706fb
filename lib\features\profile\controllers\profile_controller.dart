import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../services/profile_service.dart';
import '../../../core/app/utils/show_toast.dart';

class ProfileController extends GetxController {
  final ProfileService _profileService = ProfileService();
  final logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxBool isUpdating = false.obs;
  final RxString errorMessage = ''.obs;
  final RxMap<String, dynamic> profileData = <String, dynamic>{}.obs;

  // Form controllers
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController currentPasswordController =
      TextEditingController();
  final TextEditingController newPasswordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  @override
  void onInit() {
    super.onInit();
    fetchProfile();
  }

  @override
  void onClose() {
    firstNameController.dispose();
    lastNameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    addressController.dispose();
    currentPasswordController.dispose();
    newPasswordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }

  // Fetch user profile
  Future<void> fetchProfile() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final response = await _profileService.fetchProfile();

      if (response['status'] == true && response['data'] != null) {
        profileData.value = Map<String, dynamic>.from(response['data']);

        // Populate form controllers
        firstNameController.text = profileData['first_name']?.toString() ?? '';
        lastNameController.text = profileData['last_name']?.toString() ?? '';
        emailController.text = profileData['email']?.toString() ?? '';
        phoneController.text = profileData['phone_number']?.toString() ?? '';
        addressController.text = profileData['address']?.toString() ?? '';

        logger.d('Profile data loaded successfully');
      } else {
        errorMessage.value = response['message'] ?? 'Failed to load profile';
        logger.w('Failed to load profile: ${response['message']}');
      }
    } catch (e) {
      errorMessage.value = 'Failed to load profile: ${e.toString()}';
      logger.e('Error in fetchProfile: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Update profile
  Future<bool> updateProfile() async {
    try {
      isUpdating.value = true;

      final response = await _profileService.updateProfile(
        firstName: firstNameController.text.trim(),
        lastName: lastNameController.text.trim(),
        email: emailController.text.trim(),
        phoneNumber: phoneController.text.trim(),
        address: addressController.text.trim(),
      );

      if (response['status'] == true) {
        ToastUtils.showSuccessToast('Success', 'Profile updated successfully');
        await fetchProfile(); // Refresh profile data
        return true;
      } else {
        ToastUtils.showErrorToast(
          'Error',
          response['message'] ?? 'Failed to update profile',
        );
        return false;
      }
    } catch (e) {
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      logger.e('Error updating profile: $e');
      return false;
    } finally {
      isUpdating.value = false;
    }
  }

  // Change password
  Future<bool> changePassword() async {
    try {
      if (currentPasswordController.text.isEmpty ||
          newPasswordController.text.isEmpty ||
          confirmPasswordController.text.isEmpty) {
        ToastUtils.showErrorToast(
          'Error',
          'Please fill in all password fields',
        );
        return false;
      }

      if (newPasswordController.text != confirmPasswordController.text) {
        ToastUtils.showErrorToast('Error', 'New passwords do not match');
        return false;
      }

      if (newPasswordController.text.length < 6) {
        ToastUtils.showErrorToast(
          'Error',
          'Password must be at least 6 characters',
        );
        return false;
      }

      isUpdating.value = true;

      final response = await _profileService.changePassword(
        currentPassword: currentPasswordController.text,
        newPassword: newPasswordController.text,
      );

      if (response['status'] == true) {
        ToastUtils.showSuccessToast('Success', 'Password changed successfully');
        clearPasswordFields();
        return true;
      } else {
        ToastUtils.showErrorToast(
          'Error',
          response['message'] ?? 'Failed to change password',
        );
        return false;
      }
    } catch (e) {
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      logger.e('Error changing password: $e');
      return false;
    } finally {
      isUpdating.value = false;
    }
  }

  // Clear password fields
  void clearPasswordFields() {
    currentPasswordController.clear();
    newPasswordController.clear();
    confirmPasswordController.clear();
  }

  // Refresh profile data
  Future<void> refreshProfile() async {
    await fetchProfile();
  }
}
