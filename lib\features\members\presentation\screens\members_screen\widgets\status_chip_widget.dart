import 'package:flutter/material.dart';
import 'package:pluto_grid/pluto_grid.dart';

class StatusChipWidget extends StatelessWidget {
  final PlutoColumnRendererContext rendererContext;

  const StatusChipWidget({super.key, required this.rendererContext});

  @override
  Widget build(BuildContext context) {
    final status = rendererContext.cell.value as String?;

    return Chip(
      backgroundColor:
          status == "ACTIVE"
              ? Colors.green.shade100.withOpacity(0.2)
              : Colors.orange.shade100.withOpacity(0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: status == "ACTIVE" ? Colors.green : Colors.orange,
          width: 0.6,
        ),
      ),
      label: Text(
        status ?? 'INACTIVE',
        style: TextStyle(
          color: status == "ACTIVE" ? Colors.green : Colors.orange,
          fontWeight: FontWeight.w500,
          fontSize: 12,
        ),
      ),
    );
  }
}
