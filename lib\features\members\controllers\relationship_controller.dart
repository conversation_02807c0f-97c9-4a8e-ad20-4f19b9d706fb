import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/utils/show_toast.dart';
import '../models/relationship_models.dart';
import '../services/relationship_service.dart';
import '../../../data/models/member_model.dart';
import '../../members/services/member_service.dart';

/// Enum to define the view mode for relationships
enum RelationshipViewMode { table, tree }

class RelationshipController extends GetxController {
  final RelationshipService _relationshipService =
      Get.find<RelationshipService>();
  final MemberService _memberService = Get.find<MemberService>();
  final logger = Get.find<Logger>();

  // Relationship types
  final relationshipTypes = <RelationshipType>[].obs;
  final filteredRelationshipTypes = <RelationshipType>[].obs;
  final isLoadingTypes = false.obs;
  final errorMessageTypes = ''.obs;
  final currentPageTypes = 0.obs;
  final totalPagesTypes = 0.obs;
  final pageSizeTypes = 10.obs;
  final searchQueryTypes = ''.obs;
  final selectedCategory = ''.obs;
  final isSubmittingType = false.obs;

  // Member relationships
  final relationships = <MemberRelationship>[].obs;
  final isLoadingRelationships = false.obs;
  final errorMessageRelationships = ''.obs;
  final currentPageRelationships = 0.obs;
  final totalPagesRelationships = 0.obs;
  final pageSizeRelationships = 10.obs;
  final selectedMemberId = ''.obs;
  final isSubmittingRelationship = false.obs;

  // View mode for relationships (table or tree)
  final viewMode = RelationshipViewMode.table.obs;

  // Selected member details
  final selectedMember = Rxn<MemberModel>();

  // Form controllers
  final typeSearchController = TextEditingController();
  final typeTitleController = TextEditingController();
  final typeDescriptionController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onClose() {
    typeSearchController.dispose();
    typeTitleController.dispose();
    typeDescriptionController.dispose();
    super.onClose();
  }

  /// Toggle between table and tree view modes
  void toggleViewMode() {
    // Prevent unnecessary rebuilds by checking if we're already in the requested mode
    if (viewMode.value == RelationshipViewMode.table) {
      viewMode.value = RelationshipViewMode.tree;
    } else {
      viewMode.value = RelationshipViewMode.table;
    }
  }

  // Flag to indicate if tree has been initialized
  final isTreeInitialized = false.obs;

  // Set member ID and fetch relationships
  void setMemberId(String memberId) {
    selectedMemberId.value = memberId;
    fetchMemberDetails();
    fetchRelationships();
  }

  // Fetch member details
  Future<void> fetchMemberDetails() async {
    try {
      if (selectedMemberId.isEmpty) return;

      final result = await _memberService.getMemberById(selectedMemberId.value);

      if (result['status'] == true && result['data'] != null) {
        selectedMember.value = MemberModel.fromJson(result['data']);
      } else {
        errorMessageRelationships.value =
            result['message'] ?? 'Failed to fetch member details';
        ToastUtils.showErrorToast(errorMessageRelationships.value, null);
      }
    } catch (e) {
      errorMessageRelationships.value = 'Error: ${e.toString()}';
      ToastUtils.showErrorToast(errorMessageRelationships.value, null);
      logger.e('Error fetching member details: $e');
    }
  }

  // Fetch relationship types
  Future<void> fetchRelationshipTypes({
    int? page,
    String? search,
    String? category,
    bool resetList = true,
  }) async {
    try {
      isLoadingTypes.value = true;
      errorMessageTypes.value = '';

      final pageToFetch = page ?? currentPageTypes.value;
      final searchToUse = search ?? searchQueryTypes.value;
      final categoryToUse = category ?? selectedCategory.value;

      final result = await _relationshipService.fetchRelationshipTypes(
        page: pageToFetch,
        size: pageSizeTypes.value,
        search: searchToUse,
        category: categoryToUse,
      );

      if (result.status) {
        if (resetList) {
          relationshipTypes.clear();
        }

        if (result.data != null) {
          relationshipTypes.addAll(result.data!.items);
          totalPagesTypes.value = result.data!.totalPages;
          currentPageTypes.value = result.data!.currentPage;
        }

        // Update filtered list
        filteredRelationshipTypes.value = relationshipTypes;
      } else {
        errorMessageTypes.value = result.message;
        ToastUtils.showErrorToast(errorMessageTypes.value, null);
      }
    } catch (e) {
      errorMessageTypes.value = 'Error: ${e.toString()}';
      ToastUtils.showErrorToast(errorMessageTypes.value, null);
      logger.e('Error fetching relationship types: $e');
    } finally {
      isLoadingTypes.value = false;
    }
  }

  // Fetch member relationships
  Future<void> fetchRelationships({int? page, bool resetList = true}) async {
    try {
      if (selectedMemberId.isEmpty) return;

      isLoadingRelationships.value = true;
      errorMessageRelationships.value = '';

      final pageToFetch = page ?? currentPageRelationships.value;

      final result = await _relationshipService.fetchMemberRelationships(
        page: pageToFetch,
        size: pageSizeRelationships.value,
        fromMemberId: selectedMemberId.value,
      );

      if (result.status) {
        if (resetList) {
          relationships.clear();
        }

        if (result.data != null) {
          relationships.addAll(result.data!.items);
          totalPagesRelationships.value = result.data!.totalPages;
          currentPageRelationships.value = result.data!.currentPage;
        }
      } else {
        errorMessageRelationships.value = result.message;
        ToastUtils.showErrorToast(errorMessageRelationships.value, null);
      }
    } catch (e) {
      errorMessageRelationships.value = 'Error: ${e.toString()}';
      ToastUtils.showErrorToast(errorMessageRelationships.value, null);
      logger.e('Error fetching member relationships: $e');
    } finally {
      isLoadingRelationships.value = false;
    }
  }

  // Create a new relationship type
  Future<bool> createRelationshipType({
    required String title,
    required String description,
    required String category,
    required bool isGeneral,
  }) async {
    isSubmittingType.value = true;

    try {
      final result = await _relationshipService.createRelationshipType(
        title: title,
        description: description,
        category: category,
        isGeneral: isGeneral,
      );

      if (result['status'] == true) {
        ToastUtils.showSuccessToast(
          'Success',
          'Relationship type created successfully',
        );

        // Refresh relationship types list
        await fetchRelationshipTypes();
        return true;
      } else {
        logger.e('Error creating relationship type: ${result['message']}');
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to create relationship type',
        );
        return false;
      }
    } catch (e) {
      logger.e('Exception creating relationship type: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      return false;
    } finally {
      isSubmittingType.value = false;
    }
  }

  // Create a new member relationship
  Future<bool> createMemberRelationship({
    required String toMemberId,
    required String relationshipTypeId,
  }) async {
    if (selectedMemberId.isEmpty) {
      ToastUtils.showErrorToast('Error', 'No member selected');
      return false;
    }

    isSubmittingRelationship.value = true;

    try {
      final result = await _relationshipService.createMemberRelationship(
        fromMemberId: selectedMemberId.value,
        toMemberId: toMemberId,
        relationshipTypeId: relationshipTypeId,
      );

      if (result['status'] == true) {
        ToastUtils.showSuccessToast(
          'Success',
          'Relationship created successfully',
        );

        // Refresh relationships list
        await fetchRelationships();
        return true;
      } else {
        logger.e('Error creating relationship: ${result['message']}');
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to create relationship',
        );
        return false;
      }
    } catch (e) {
      logger.e('Exception creating relationship: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      return false;
    } finally {
      isSubmittingRelationship.value = false;
    }
  }

  // Create multiple member relationships at once
  Future<bool> createMemberRelationships({
    required List<Map<String, dynamic>> relationships,
  }) async {
    if (selectedMemberId.isEmpty) {
      ToastUtils.showErrorToast('Error', 'No member selected');
      return false;
    }

    isSubmittingRelationship.value = true;

    try {
      final result = await _relationshipService.createMemberRelationships(
        relationships: relationships,
      );

      if (result['status'] == true) {
        ToastUtils.showSuccessToast(
          'Success',
          'Relationships created successfully',
        );

        // Refresh relationships list
        await fetchRelationships();
        return true;
      } else {
        logger.e('Error creating relationships: ${result['message']}');
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to create relationships',
        );
        return false;
      }
    } catch (e) {
      logger.e('Exception creating relationships: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      return false;
    } finally {
      isSubmittingRelationship.value = false;
    }
  }

  // Delete a member relationship
  Future<bool> deleteRelationship(String id) async {
    try {
      final result = await _relationshipService.deleteMemberRelationship(id);

      if (result['status'] == true) {
        ToastUtils.showSuccessToast(
          'Success',
          'Relationship deleted successfully',
        );

        // Refresh relationships list
        await fetchRelationships();
        return true;
      } else {
        logger.e('Error deleting relationship: ${result['message']}');
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to delete relationship',
        );
        return false;
      }
    } catch (e) {
      logger.e('Exception deleting relationship: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      return false;
    }
  }

  // Delete a relationship type
  Future<bool> deleteRelationshipType(String id) async {
    try {
      final result = await _relationshipService.deleteRelationshipType(id);

      if (result['status'] == true) {
        ToastUtils.showSuccessToast(
          'Success',
          'Relationship type deleted successfully',
        );

        // Refresh relationship types list
        await fetchRelationshipTypes();
        return true;
      } else {
        logger.e('Error deleting relationship type: ${result['message']}');
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to delete relationship type',
        );
        return false;
      }
    } catch (e) {
      logger.e('Exception deleting relationship type: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      return false;
    }
  }
}
