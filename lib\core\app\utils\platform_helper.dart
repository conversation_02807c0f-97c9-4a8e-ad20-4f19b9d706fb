import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

/// Helper class to handle platform-specific operations
class PlatformHelper {
  /// Downloads a file from assets and shares it with the user
  /// 
  /// Works on both web and mobile platforms
  static Future<void> downloadFileFromAssets({
    required String assetPath,
    required String fileName,
  }) async {
    try {
      // Get the file data from assets
      final ByteData data = await rootBundle.load(assetPath);
      final List<int> bytes = data.buffer.asUint8List();
      
      if (kIsWeb) {
        // Web implementation
        _downloadFileWeb(bytes, fileName);
      } else {
        // Mobile implementation
        await _downloadFileMobile(bytes, fileName);
      }
    } catch (e) {
      rethrow; // Let the caller handle the error
    }
  }

  // Web implementation using JS interop
  static void _downloadFileWeb(List<int> bytes, String fileName) {
    // This is implemented in a separate file to avoid dart:html import issues
    // The actual implementation is in web_download_helper.dart
    throw UnsupportedError('Web downloads should be handled by WebDownloadHelper');
  }

  // Mobile implementation using share_plus
  static Future<void> _downloadFileMobile(List<int> bytes, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final path = '${directory.path}/$fileName';
    final file = File(path);
    await file.writeAsBytes(bytes);
    await Share.shareXFiles([XFile(path)], text: 'Download $fileName');
  }
}