import 'package:flutter_test/flutter_test.dart';
import 'package:onechurch/core/app/utils/location_utils.dart';
import 'package:onechurch/core/app/constants/enums.dart' as enums;

void main() {
  group('LocationUtils Tests', () {
    group('Coordinate Validation', () {
      test('should validate correct coordinates', () {
        expect(LocationUtils.isValidCoordinates(37.7749, -122.4194), isTrue);
        expect(LocationUtils.isValidCoordinates(0.0, 0.0), isTrue);
        expect(LocationUtils.isValidCoordinates(90.0, 180.0), isTrue);
        expect(LocationUtils.isValidCoordinates(-90.0, -180.0), isTrue);
      });

      test('should reject invalid coordinates', () {
        expect(LocationUtils.isValidCoordinates(null, null), isFalse);
        expect(LocationUtils.isValidCoordinates(91.0, 0.0), isFalse);
        expect(LocationUtils.isValidCoordinates(-91.0, 0.0), isFalse);
        expect(LocationUtils.isValidCoordinates(0.0, 181.0), isFalse);
        expect(LocationUtils.isValidCoordinates(0.0, -181.0), isFalse);
      });
    });

    group('Coordinate Formatting', () {
      test('should format coordinates correctly', () {
        expect(
          LocationUtils.formatCoordinates(37.7749, -122.4194),
          equals('37.774900, -122.419400'),
        );
        expect(
          LocationUtils.formatCoordinates(0.0, 0.0),
          equals('0.000000, 0.000000'),
        );
        expect(
          LocationUtils.formatCoordinates(-90.0, 180.0),
          equals('-90.000000, 180.000000'),
        );
      });
    });

    group('Distance Calculations', () {
      test('should calculate distance between same points as 0', () {
        final distance = LocationUtils.calculateDistance(
          37.7749, -122.4194,
          37.7749, -122.4194,
        );
        expect(distance, equals(0.0));
      });

      test('should calculate distance between different points', () {
        // Distance between San Francisco and Los Angeles (approximately 559 km)
        final distance = LocationUtils.calculateDistance(
          37.7749, -122.4194, // San Francisco
          34.0522, -118.2437, // Los Angeles
        );
        
        // Should be approximately 559,000 meters (559 km)
        expect(distance, greaterThan(550000));
        expect(distance, lessThan(570000));
      });
    });

    group('Bearing Calculations', () {
      test('should calculate bearing between same points as 0', () {
        final bearing = LocationUtils.calculateBearing(
          37.7749, -122.4194,
          37.7749, -122.4194,
        );
        expect(bearing, equals(0.0));
      });

      test('should calculate bearing between different points', () {
        // Bearing from San Francisco to Los Angeles (approximately 135 degrees)
        final bearing = LocationUtils.calculateBearing(
          37.7749, -122.4194, // San Francisco
          34.0522, -118.2437, // Los Angeles
        );
        
        // Should be approximately 135 degrees (southeast)
        expect(bearing, greaterThan(130));
        expect(bearing, lessThan(140));
      });
    });

    group('Enum Conversions', () {
      test('should convert LocationAccuracy enum correctly', () {
        expect(
          LocationUtils.convertToGeolocatorAccuracy(enums.LocationAccuracy.high),
          isNotNull,
        );
        expect(
          LocationUtils.convertToGeolocatorAccuracy(enums.LocationAccuracy.best),
          isNotNull,
        );
        expect(
          LocationUtils.convertToGeolocatorAccuracy(enums.LocationAccuracy.lowest),
          isNotNull,
        );
      });
    });

    group('Accuracy Descriptions', () {
      test('should provide correct accuracy descriptions', () {
        expect(
          LocationUtils.getAccuracyDescription(enums.LocationAccuracy.lowest),
          contains('3000m'),
        );
        expect(
          LocationUtils.getAccuracyDescription(enums.LocationAccuracy.high),
          contains('10m'),
        );
        expect(
          LocationUtils.getAccuracyDescription(enums.LocationAccuracy.best),
          contains('3m'),
        );
        expect(
          LocationUtils.getAccuracyDescription(enums.LocationAccuracy.bestForNavigation),
          contains('1m'),
        );
      });
    });

    group('Permission Status Helpers', () {
      test('should correctly identify sufficient permissions', () {
        expect(
          LocationUtils.isPermissionSufficient(enums.LocationPermissionStatus.granted),
          isTrue,
        );
        expect(
          LocationUtils.isPermissionSufficient(enums.LocationPermissionStatus.always),
          isTrue,
        );
        expect(
          LocationUtils.isPermissionSufficient(enums.LocationPermissionStatus.whileInUse),
          isTrue,
        );
      });

      test('should correctly identify insufficient permissions', () {
        expect(
          LocationUtils.isPermissionSufficient(enums.LocationPermissionStatus.denied),
          isFalse,
        );
        expect(
          LocationUtils.isPermissionSufficient(enums.LocationPermissionStatus.deniedForever),
          isFalse,
        );
        expect(
          LocationUtils.isPermissionSufficient(enums.LocationPermissionStatus.unableToDetermine),
          isFalse,
        );
      });
    });

    group('Permission Descriptions', () {
      test('should provide correct permission descriptions', () {
        expect(
          LocationUtils.getPermissionDescription(enums.LocationPermissionStatus.granted),
          contains('granted'),
        );
        expect(
          LocationUtils.getPermissionDescription(enums.LocationPermissionStatus.denied),
          contains('denied'),
        );
        expect(
          LocationUtils.getPermissionDescription(enums.LocationPermissionStatus.deniedForever),
          contains('permanently denied'),
        );
      });
    });

    group('Platform Location Settings', () {
      test('should create platform location settings without errors', () {
        expect(
          () => LocationUtils.getPlatformLocationSettings(),
          returnsNormally,
        );
        
        expect(
          () => LocationUtils.getPlatformLocationSettings(
            accuracy: enums.LocationAccuracy.best,
            distanceFilter: 5,
            timeLimit: const Duration(seconds: 30),
          ),
          returnsNormally,
        );
      });
    });

    group('Edge Cases', () {
      test('should handle extreme coordinate values', () {
        // Test with maximum valid coordinates
        expect(LocationUtils.isValidCoordinates(90.0, 180.0), isTrue);
        expect(LocationUtils.isValidCoordinates(-90.0, -180.0), isTrue);
        
        // Test formatting with extreme values
        expect(
          LocationUtils.formatCoordinates(90.0, 180.0),
          equals('90.000000, 180.000000'),
        );
        expect(
          LocationUtils.formatCoordinates(-90.0, -180.0),
          equals('-90.000000, -180.000000'),
        );
      });

      test('should handle distance calculation with extreme coordinates', () {
        // Distance from North Pole to South Pole
        final distance = LocationUtils.calculateDistance(
          90.0, 0.0,   // North Pole
          -90.0, 0.0,  // South Pole
        );
        
        // Should be approximately half the Earth's circumference (20,003 km)
        expect(distance, greaterThan(19000000)); // 19,000 km
        expect(distance, lessThan(21000000));    // 21,000 km
      });
    });

    group('Null Safety', () {
      test('should handle null coordinates safely', () {
        expect(LocationUtils.isValidCoordinates(null, 0.0), isFalse);
        expect(LocationUtils.isValidCoordinates(0.0, null), isFalse);
        expect(LocationUtils.isValidCoordinates(null, null), isFalse);
      });
    });
  });
}
