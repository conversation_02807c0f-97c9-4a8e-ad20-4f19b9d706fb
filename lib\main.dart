import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onechurch/core/app/translations/app_translations.dart';
import 'package:onechurch/firebase_options.dart';
import 'package:toastification/toastification.dart';
import 'core/app/themes/app_theme.dart';
import 'core/app/constants/routes.dart';
import 'core/app/services/init_services.dart';
import 'package:dynamic_path_url_strategy/dynamic_path_url_strategy.dart';
import 'package:device_preview_plus/device_preview_plus.dart';
import 'package:flutter_quill/flutter_quill.dart';
// ignore: depend_on_referenced_packages
import 'package:flutter_localizations/flutter_localizations.dart';
import 'dart:js' as js;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Configure URL strategy for web - must be called early, before any other initialization
  if (kIsWeb) {
    try {
      setPathUrlStrategy();
    } catch (e) {
      debugPrint('Error setting URL strategy: $e');
      // Continue execution even if setting URL strategy fails
    }
  }

  // Initialize Firebase with platform-specific error handling
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  } catch (e) {
    debugPrint('Firebase initialization error: $e');
    // Continue execution even if Firebase fails
  }

  await GetStorage.init();
  // Initialize all services and controllers
  await InitServices.init();

  runApp(
    DevicePreview(
      // isToolbarVisible: false,
      // enabled: kDebugMode || kIsWeb,
      enabled: false,
      builder: (context) => MyApp(), // Wrap your app
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Check for stored route from sessionStorage (for web redirects)
    String initialRoute = Routes.HOME;
    if (kIsWeb) {
      try {
        // Use JS interop to access sessionStorage
        final js.JsObject? window = js.context['window'] as js.JsObject?;
        if (window != null) {
          final js.JsObject? sessionStorage =
              window['sessionStorage'] as js.JsObject?;
          if (sessionStorage != null) {
            final storedRoute = sessionStorage['flutter_route'];

            if (storedRoute != null && storedRoute.toString().isNotEmpty) {
              initialRoute = storedRoute.toString();
              // Clear the stored route to prevent infinite redirects
              sessionStorage.deleteProperty('flutter_route');
              debugPrint('Redirecting to stored route: $initialRoute');
            }
          }
        }
      } catch (e) {
        debugPrint('Error getting stored route: $e');
      }
    }
    // todo move profile column next to name
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: false,
      splitScreenMode: true,
      child: ToastificationWrapper(
        child: ToastificationConfigProvider(
          config: ToastificationConfig(
            alignment: Alignment.center,
            itemWidth: 440,
            animationDuration: Duration(milliseconds: 500),
          ),
          child: MaterialApp.router(
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              FlutterQuillLocalizations.delegate,
            ],
            supportedLocales: AppLocalizations.supportedLocales,
            debugShowCheckedModeBanner: false,
            title: 'onechurch',

            routerConfig: AppRouter.createRouter(),
            theme: AppTheme.lightTheme(),
            darkTheme: AppTheme.darkTheme(),
            themeMode: ThemeMode.light,
            locale: Locale('sw'),
            useInheritedMediaQuery: true,
          ),
        ),
      ),
    );
  }
}
