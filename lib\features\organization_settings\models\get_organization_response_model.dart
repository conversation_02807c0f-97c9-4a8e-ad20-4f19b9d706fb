// To parse this JSON data, do
//
//     final organisationResponse = organisationResponseFromJson(jsonString);

import 'dart:convert';
import '../../../data/models/organisation_model.dart';

OrganisationResponse organisationResponseFromJson(String str) => 
    OrganisationResponse.fromJson(json.decode(str));

String organisationResponseToJson(OrganisationResponse data) => 
    json.encode(data.toJson());

class OrganisationResponse {
    final bool? status;
    final String? message;
    final Organisation? data;
    final dynamic errors;

    OrganisationResponse({
        this.status,
        this.message,
        this.data,
        this.errors,
    });

    OrganisationResponse copyWith({
        bool? status,
        String? message,
        Organisation? data,
        dynamic errors,
    }) => 
        OrganisationResponse(
            status: status ?? this.status,
            message: message ?? this.message,
            data: data ?? this.data,
            errors: errors ?? this.errors,
        );

    factory OrganisationResponse.fromJson(Map<String, dynamic> json) => 
        OrganisationResponse(
            status: json["status"],
            message: json["message"],
            data: json["data"] == null ? null : Organisation.fromJson(json["data"]),
            errors: json["errors"],
        );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
        "errors": errors,
    };
}
