import 'package:flutter/material.dart';

class RecipientChip extends StatelessWidget {
  final String phone;
  final VoidCallback onDeleted;

  const RecipientChip({
    super.key,
    required this.phone,
    required this.onDeleted,
  });

  @override
  Widget build(BuildContext context) {
    return Chip(
      label: Text(phone),
      deleteIcon: const Icon(Icons.close, size: 18),
      onDeleted: onDeleted,
      backgroundColor: Colors.blue.shade100,
      deleteIconColor: Colors.red,
    );
  }
}
