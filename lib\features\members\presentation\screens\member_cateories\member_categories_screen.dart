import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/core/app/widgets/loading_animations.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../../../../core/app/constants/routes.dart';
// Import only what we need
import '../../../controllers/member_category_controller.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';

class MemberCategoriesScreen extends StatefulWidget {
  const MemberCategoriesScreen({super.key});

  @override
  State<MemberCategoriesScreen> createState() => _MemberCategoriesScreenState();
}

class _MemberCategoriesScreenState extends State<MemberCategoriesScreen> {
  final controller = Get.put(MemberCategoryController());
  final searchController = TextEditingController();

  // Pluto Grid state
  late final PlutoGridStateManager stateManager;
  final List<PlutoColumn> columns = [];
  final List<PlutoRow> rows = [];

  @override
  void initState() {
    super.initState();
    _initPlutoColumns();
    _fetchCategories();
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  void _fetchCategories() {
    controller.fetchCategories().then((_) {
      _updatePlutoRows();
    });
  }

  void _initPlutoColumns() {
    columns.addAll([
      PlutoColumn(
        title: 'Title',
        field: 'title',
        type: PlutoColumnType.text(),
        width: 200,
      ),
      PlutoColumn(
        title: 'Code',
        field: 'code',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        width: 250,
      ),
      PlutoColumn(
        title: 'General',
        field: 'isGeneral',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final isGeneral = rendererContext.cell.value == 'Yes';
          return Center(
            child: Icon(
              isGeneral ? Icons.check_circle : Icons.cancel,
              color: isGeneral ? Colors.green : Colors.red,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Active',
        field: 'isActive',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final isActive = rendererContext.cell.value == 'Yes';
          return Center(
            child: Icon(
              isActive ? Icons.check_circle : Icons.cancel,
              color: isActive ? Colors.green : Colors.red,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 150,
        renderer: (rendererContext) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.edit, color: Colors.blue),
                onPressed: () {
                  final categoryId = rendererContext.row.cells['id']!.value;
                  _editCategory(categoryId);
                },
                tooltip: 'Edit',
              ),
              IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: () {
                  final categoryId = rendererContext.row.cells['id']!.value;
                  _confirmDeleteCategory(categoryId);
                },
                tooltip: 'Delete',
              ),
            ],
          );
        },
      ),
    ]);
  }

  void _updatePlutoRows() {
    // Clear existing rows
    rows.clear();

    // Add a row for each category
    for (int i = 0; i < controller.categories.length; i++) {
      final category = controller.categories[i];
      rows.add(
        PlutoRow(
          cells: {
            'title': PlutoCell(value: category.title),
            'code': PlutoCell(value: category.code),
            'description': PlutoCell(value: category.description ?? ''),
            'isGeneral': PlutoCell(
              value: category.isGeneral ?? false ? 'Yes' : 'No',
            ),
            'isActive': PlutoCell(
              value: category.isActive ?? false ? 'Yes' : 'No',
            ),
            'actions': PlutoCell(value: ''),
            'id': PlutoCell(value: category.id),
          },
        ),
      );
    }

    // Refresh grid if initialized
    if (mounted) {
      stateManager.notifyListeners();
    }
  }

  void _editCategory(String categoryId) {
    // Navigate to edit category screen
    context.go(Routes.EDIT_MEMBER_CATEGORY.replaceAll(':id', categoryId));
  }

  void _confirmDeleteCategory(String categoryId) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirm Delete'),
            content: const Text(
              'Are you sure you want to delete this category? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              CustomButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _deleteCategory(categoryId);
                },
                color: Colors.red,
                label: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  Future<void> _deleteCategory(String categoryId) async {
    final result = await controller.deleteCategory(categoryId);
    if (result) {
      _updatePlutoRows();
    }
  }

  void _searchCategories() {
    final query = searchController.text.trim();
    controller.searchCategories(query).then((_) {
      _updatePlutoRows();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Member Categories'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              context.go(Routes.ADD_MEMBER_CATEGORY);
            },
            tooltip: 'Add Category',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter bar
          Container(
            padding: EdgeInsets.all(16.r),
            color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            child: Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: searchController,
                    hintText: 'Search categories...',
                    prefixIcon: const Icon(IconlyLight.search),

                    onSubmitted: (_) => _searchCategories(),
                  ),
                ),
                SizedBox(width: 16.w),
                CustomButton(
                  onPressed: _searchCategories,
                  icon: const Icon(IconlyLight.search),
                  label: const Text('Search'),
                ),
                SizedBox(width: 8.w),
                IconButton(
                  onPressed: () {
                    searchController.clear();
                    controller.searchQuery.value = '';
                    _fetchCategories();
                  },
                  icon: const Icon(Icons.clear),
                  tooltip: 'Clear search',
                ),
              ],
            ),
          ),

          // Categories list with Pluto Grid
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value && controller.categories.isEmpty) {
                return const Center(child: LoadingAnimations());
              }

              if (controller.categories.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        IconlyLight.category,
                        size: 64.r,
                        color: theme.colorScheme.primary.withOpacity(0.5),
                      ),
                      Gap(16.h),
                      Text(
                        'No categories found',
                        style: theme.textTheme.titleMedium,
                      ),
                      Gap(8.h),
                      Text(
                        'Click the + button to add a category',
                        style: theme.textTheme.bodyMedium,
                      ),
                      Gap(24.h),
                      CustomButton(
                        onPressed: () {
                          context.go(Routes.ADD_MEMBER_CATEGORY);
                        },
                        icon: const Icon(Icons.add),
                        label: const Text('Add Category'),
                      ),
                    ],
                  ),
                );
              }

              return Padding(
                padding: EdgeInsets.all(16.r),
                child: PlutoGrid(
                  columns: columns,
                  rows: rows,
                  onLoaded: (PlutoGridOnLoadedEvent event) {
                    stateManager = event.stateManager;
                  },
                  configuration: PlutoGridConfiguration(
                    columnSize: const PlutoGridColumnSizeConfig(
                      autoSizeMode: PlutoAutoSizeMode.scale,
                    ),
                    style: PlutoGridStyleConfig(
                      borderColor: theme.dividerColor,
                      gridBackgroundColor: theme.scaffoldBackgroundColor,
                      rowColor: theme.cardColor,
                      cellTextStyle: theme.textTheme.bodyMedium!,
                      columnTextStyle: theme.textTheme.titleSmall!.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),

          // Pagination controls
          Obx(() {
            if (controller.categories.isEmpty) {
              return const SizedBox.shrink();
            }

            return Container(
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: theme.cardColor,
                border: Border(top: BorderSide(color: theme.dividerColor)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Showing ${controller.categories.length} of ${controller.totalCategories} categories',
                    style: theme.textTheme.bodySmall,
                  ),
                  Row(
                    children: [
                      IconButton(
                        onPressed:
                            controller.currentPage.value > 0
                                ? () => controller
                                    .fetchCategories(
                                      page: controller.currentPage.value - 1,
                                    )
                                    .then((_) => _updatePlutoRows())
                                : null,
                        icon: const Icon(Icons.chevron_left),
                        tooltip: 'Previous page',
                      ),
                      Text(
                        'Page ${controller.currentPage.value + 1} of ${controller.totalPages.value}',
                        style: theme.textTheme.bodySmall,
                      ),
                      IconButton(
                        onPressed:
                            controller.currentPage.value <
                                    controller.totalPages.value - 1
                                ? () => controller
                                    .fetchCategories(
                                      page: controller.currentPage.value + 1,
                                    )
                                    .then((_) => _updatePlutoRows())
                                : null,
                        icon: const Icon(Icons.chevron_right),
                        tooltip: 'Next page',
                      ),
                    ],
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
}
