import 'dart:convert';

class StaffRoleAssignmentModel {
  final String? id;
  final String? staffId;
  final String? roleId;
  final DateTime? assignedAt;
  final String? description;
  final bool? isActive;
  final DateTime? expiresAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;

  StaffRoleAssignmentModel({
    this.id,
    this.staffId,
    this.roleId,
    this.assignedAt,
    this.description,
    this.isActive,
    this.expiresAt,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
  });

  StaffRoleAssignmentModel copyWith({
    String? id,
    String? staffId,
    String? roleId,
    DateTime? assignedAt,
    String? description,
    bool? isActive,
    DateTime? expiresAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
  }) =>
      StaffRoleAssignmentModel(
        id: id ?? this.id,
        staffId: staffId ?? this.staffId,
        roleId: roleId ?? this.roleId,
        assignedAt: assignedAt ?? this.assignedAt,
        description: description ?? this.description,
        isActive: isActive ?? this.isActive,
        expiresAt: expiresAt ?? this.expiresAt,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
      );

  factory StaffRoleAssignmentModel.fromRawJson(String str) =>
      StaffRoleAssignmentModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StaffRoleAssignmentModel.fromJson(Map<String, dynamic> json) =>
      StaffRoleAssignmentModel(
        id: json["id"],
        staffId: json["staff_id"],
        roleId: json["role_id"],
        assignedAt: json["assigned_at"] == null
            ? null
            : DateTime.parse(json["assigned_at"]),
        description: json["description"],
        isActive: json["is_active"],
        expiresAt: json["expires_at"] == null
            ? null
            : DateTime.parse(json["expires_at"]),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        deletedAt: json["deleted_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "staff_id": staffId,
        "role_id": roleId,
        "assigned_at": assignedAt?.toIso8601String(),
        "description": description,
        "is_active": isActive,
        "expires_at": expiresAt?.toIso8601String(),
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt,
      };
}
