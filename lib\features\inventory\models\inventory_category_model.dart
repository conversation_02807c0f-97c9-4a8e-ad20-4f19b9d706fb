import 'dart:convert';

class InventoryCategory {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final String? description;
  final String? code;
  final String? organisationId;
  final bool? isGeneral;
  final bool? isActive;

  InventoryCategory({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.description,
    this.code,
    this.organisationId,
    this.isGeneral,
    this.isActive,
  });

  InventoryCategory copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? title,
    String? description,
    String? code,
    String? organisationId,
    bool? isGeneral,
    bool? isActive,
  }) => InventoryCategory(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    title: title ?? this.title,
    description: description ?? this.description,
    code: code ?? this.code,
    organisationId: organisationId ?? this.organisationId,
    isGeneral: isGeneral ?? this.isGeneral,
    isActive: isActive ?? this.isActive,
  );

  factory InventoryCategory.fromRawJson(String str) =>
      InventoryCategory.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory InventoryCategory.fromJson(
    Map<String, dynamic> json,
  ) => InventoryCategory(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    title: json["title"],
    description: json["description"],
    code: json["code"],
    organisationId: json["organisation_id"],
    isGeneral: json["is_general"],
    isActive: json["is_active"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "title": title,
    "description": description,
    "code": code,
    "organisation_id": organisationId,
    "is_general": isGeneral,
    "is_active": isActive,
  };

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InventoryCategory &&
          other.id == id &&
          other.title == title &&
          other.code == code;

  @override
  int get hashCode => Object.hash(id, title, code);
}

class InventoryCategoryResponse {
  final bool status;
  final String message;
  final List<InventoryCategory> data;
  final dynamic errors;

  InventoryCategoryResponse({
    required this.status,
    required this.message,
    required this.data,
    this.errors,
  });

  factory InventoryCategoryResponse.fromJson(Map<String, dynamic> json) =>
      InventoryCategoryResponse(
        status: json["status"] ?? false,
        message: json["message"] ?? "",
        data:
            json["data"] == null
                ? []
                : List<InventoryCategory>.from(
                  json["data"].map((x) => InventoryCategory.fromJson(x)),
                ),
        errors: json["errors"],
      );
}
