import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum Snacktype { errror, success }

class Snack {
  static show(bool isSuccess, String message, {String? message2}) {
    if (!isSuccess) {
      return showError(message1: message, message2: message2);
    } else {
      return showInfo(message1: message, message2: message2);
    }
  }

  static showInfo({
    required String message1,
    String? message2,
    SnackPosition? snackPosition,
    int? duration,
  }) {
    return Get.snackbar(
      message1,
      message2 ?? 'success',
      isDismissible: true,
      duration: Duration(seconds: duration ?? 3),
      snackPosition: snackPosition ?? SnackPosition.top,
      borderColor: const Color(0xFF02BA83).withOpacity(0.3),
      borderWidth: 1.7,
      barBlur: 39,
      backgroundColor: const Color(0xFF02BA83).withOpacity(0.12),
    );
  }

  static showError({
    required String message1,
    String? message2,
    SnackPosition? snackPosition,
    int? duration,
  }) {
    return Get.snackbar(
      message1,
      message2 ?? 'Error',
      isDismissible: true,
      colorText: const Color.fromARGB(238, 255, 255, 255),
      overlayBlur: 1.2,
      borderColor: const Color(0xFFFC6868).withOpacity(0.3),
      backgroundColor: Colors.red,
      //backgroundColor: const Color(0xFFFC6868).withOpacity(0.12),
      borderWidth: 1.7,
      barBlur: 15,
      duration: Duration(seconds: duration ?? 3),
      snackPosition: snackPosition ?? SnackPosition.top,
    );
  }
}
