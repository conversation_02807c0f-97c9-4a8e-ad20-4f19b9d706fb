import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/services/api_urls.dart';
import 'package:onechurch/core/app/services/http_service.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import 'package:onechurch/features/finances/models/transactions_model.dart';

class TransactionsController extends GetxController {
  final logger = Get.find<Logger>();

  // State variables
  RxBool isLoading = false.obs;
  RxString message = ''.obs;
  RxBool apiStatus = false.obs;
  RxInt currentPage = 0.obs;
  RxInt totalPages = 0.obs;
  RxInt totalItems = 0.obs;
  RxInt pageSize = 10.obs;
  RxBool isLastPage = false.obs;
  RxBool isFirstPage = true.obs;
  final HttpService _httpService = Get.find();
  RxList<TransactionsModel> transactions = <TransactionsModel>[].obs;
  RxDouble totalIn = 0.0.obs;
  RxDouble totalOut = 0.0.obs;
  RxString startDate = ''.obs;
  RxString endDate = ''.obs;
  RxString accountNumber = ''.obs;
  RxString subAccount = ''.obs;
  RxString member = ''.obs;
  RxString transactionCode = ''.obs;
  RxString searchQuery = ''.obs;
  final TextEditingController searchController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    getTransactions();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    currentPage.value = 0;
    getTransactions();
  }

  // Set date filters
  void setDateFilters(String start, String end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0;
    getTransactions();
  }

  // Set account filter
  void setAccountFilter(String account) {
    subAccount.value = account;
    currentPage.value = 0;
    getTransactions();
  }

  // Set transaction code filter
  void setTransactionCodeFilter(String code) {
    transactionCode.value = code;
    currentPage.value = 0;
    getTransactions();
  }

  // Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    searchController.clear();
    startDate.value = '';
    endDate.value = '';
    accountNumber.value = '';
    subAccount.value = '';
    member.value = '';
    transactionCode.value = '';
    currentPage.value = 0;
    getTransactions();
  }

  Future<void> getTransactions() async {
    isLoading.value = true;
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.transactions}?member_id=${member.value}",
        method: Method.GET,
        params: {
          'page': currentPage.value,
          'page_size': pageSize.value,
          "organisation_id": Get.find<AuthController>().currentOrg.value?.id,
          "start_date": startDate.string,
          "end_date": endDate.string,
          "account_number": accountNumber.string,
          "subaccount": subAccount.string,
          "member_id": member.string,
          "transaction_code": transactionCode.string,
          "search": searchQuery.string,
        },
      );
      if (response.data['status']) {
        var data = response.data['data'];
        apiStatus.value = true;
        totalIn.value = double.parse(data["totals"]['total_in'].toString());
        totalOut.value = double.parse(data["totals"]['total_out'].toString());

        final List<dynamic> items = data['data']['items'];
        transactions.value =
            items.map((json) => TransactionsModel.fromJson(json)).toList();
        currentPage.value = data['data']['page'];
        totalPages.value = data['data']['total_pages'];
        totalItems.value = data['data']['total'];
        isLastPage.value = data['data']['last'];
        isFirstPage.value = data['data']['first'];
      } else {
        message.value =
            response.data['message'] ?? 'Failed to fetch transactions';
      }
    } catch (e) {
      message.value = 'Error fetching transactions:';
      logger.e('Error fetching transactions: $e');
      rethrow;
    } finally {
      isLoading.value = false;
      update();
    }
  }
}
