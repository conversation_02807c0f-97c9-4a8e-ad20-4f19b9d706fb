import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:logger/web.dart';
import 'package:onechurch/core/app/constants/routes.dart';
import 'package:onechurch/core/app/widgets/loading_animations.dart';
import 'package:onechurch/features/finances/controllers/finance_controller.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../widgets/category_filter_widget.dart';

class AccountCategoriesScreen extends StatefulWidget {
  const AccountCategoriesScreen({super.key});

  @override
  State<AccountCategoriesScreen> createState() =>
      _AccountCategoriesScreenState();
}

class _AccountCategoriesScreenState extends State<AccountCategoriesScreen> {
  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  final TextEditingController searchController = TextEditingController();
  String searchQuery = '';

  setColumns() {
    // Initialize columns
    columns = [
      PlutoColumn(
        title: 'Title',
        field: 'title',
        type: PlutoColumnType.text(),
        width: 200,
        enableRowChecked: true,
        renderer: (rendererContext) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircleAvatar(
                backgroundColor: Color(0xFFE0E2E7),
                radius: 15,
                child: Icon(
                  IconlyLight.category,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  rendererContext.cell.value?.toString() ?? '',
                  style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          );
        },
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        width: 300,
        renderer: (rendererContext) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text(
              rendererContext.cell.value?.toString() ?? '',
              style: TextStyle(fontSize: 13, color: Colors.grey.shade700),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Type',
        field: 'type',
        type: PlutoColumnType.text(),
        width: 120,
        renderer: (rendererContext) {
          final isGeneral = rendererContext.cell.value == 'General';
          return Container(
            margin: const EdgeInsets.symmetric(vertical: 6),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isGeneral ? Colors.blue.shade50 : Colors.green.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isGeneral ? Colors.blue.shade200 : Colors.green.shade200,
              ),
            ),
            child: Text(
              isGeneral ? 'General' : 'Organization',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isGeneral ? Colors.blue.shade700 : Colors.green.shade700,
              ),
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Created Date',
        field: 'createdAt',
        type: PlutoColumnType.text(),
        width: 140,
        renderer: (rendererContext) {
          return Text(
            rendererContext.cell.value?.toString() ?? '',
            style: TextStyle(fontSize: 13, color: Colors.grey.shade600),
          );
        },
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 120,
        renderer: (rendererContext) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(IconlyLight.edit, size: 18),
                onPressed: () {
                  final categoryId = rendererContext.cell.value;
                  if (categoryId != null) {
                    // Find the category object
                    final category = Get.find<FinanceController>().categories
                        .firstWhereOrNull((cat) => cat.id == categoryId);
                    context.go(
                      Routes.EDIT_ACCOUNT_CATEGORY.replaceFirst(
                        ':id',
                        categoryId,
                      ),
                      extra: category,
                    );
                  }
                },
                tooltip: 'Edit',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
              IconButton(
                icon: const Icon(IconlyLight.delete, size: 18),
                onPressed: () {
                  final categoryId = rendererContext.cell.value;
                  if (categoryId != null) {
                    // TODO: Implement delete functionality
                    _showDeleteDialog(categoryId);
                  }
                },
                tooltip: 'Delete',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
            ],
          );
        },
      ),
    ];
  }

  void _showDeleteDialog(String categoryId) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Category'),
            content: const Text(
              'Are you sure you want to delete this category?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // TODO: Implement delete functionality
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Delete category: $categoryId')),
                  );
                },
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  Widget buildSearchField(FinanceController controller, BuildContext context) {
    return TextField(
      controller: searchController,
      decoration: InputDecoration(
        hintText: 'Search categories...',
        prefixIcon: const Icon(IconlyLight.search),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        filled: true,
        fillColor: Colors.grey.shade100,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
      onChanged: (value) {
        setState(() {
          searchQuery = value;
        });
        // TODO: Implement search functionality
      },
    );
  }

  @override
  void initState() {
    super.initState();
    setColumns();
  }

  @override
  Widget build(BuildContext context) {
    final showFilter = false.obs;
    return Scaffold(
      appBar: AppBar(
        title: const Text('Account Categories'),
        actions: [
          if (MediaQuery.of(context).size.width > 600) ...[
            SizedBox(
              width: 180.w,
              child: buildSearchField(Get.find<FinanceController>(), context),
            ),
            const SizedBox(width: 12),
          ],
          Obx(
            () => IconButton(
              onPressed: () {
                showFilter.value = !showFilter.value;
              },
              icon:
                  showFilter.value
                      ? Icon(Icons.filter_alt_outlined)
                      : Icon(Icons.filter_alt),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push(Routes.CREATE_ACCOUNT_CATEGORY);
        },
        child: const Icon(Icons.add),
      ),
      body: Center(
        child: Column(
          children: [
            Obx(
              () =>
                  showFilter.value ? const CategoryFilterWidget() : SizedBox(),
            ),
            Expanded(
              child: SizedBox(
                width: double.infinity,
                child: Card(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  elevation: 4,
                  child: GetX<FinanceController>(
                    init: Get.put(FinanceController()),
                    initState: (state) async {
                      await state.controller!.fetchCategories();
                    },
                    builder: (controller) {
                      if (kDebugMode) {
                        debugPrint(
                          "Controller state - Loading: ${controller.isLoading.value}, Categories: ${controller.categories.length}",
                        );
                      }

                      if (controller.isLoading.value) {
                        return const Center(child: LoadingAnimations());
                      } else if (controller.categories.isNotEmpty) {
                        // Convert categories to PlutoRows
                        if (kDebugMode) {
                          debugPrint(
                            "Categories Fetched: ${controller.categories.length}",
                          );
                        }
                        rows =
                            controller.categories.map((category) {
                              return PlutoRow(
                                cells: {
                                  'title': PlutoCell(
                                    value: category.title ?? 'Untitled',
                                  ),
                                  'description': PlutoCell(
                                    value:
                                        category.description ??
                                        'No description',
                                  ),
                                  'type': PlutoCell(
                                    value:
                                        category.isGeneral == true
                                            ? 'General'
                                            : 'Organization',
                                  ),
                                  'createdAt': PlutoCell(
                                    value:
                                        category.createdAt != null
                                            ? DateFormat(
                                              'dd MMM yyyy',
                                            ).format(category.createdAt!)
                                            : 'N/A',
                                  ),
                                  'actions': PlutoCell(value: category.id),
                                },
                              );
                            }).toList();

                        return PlutoGrid(
                          mode: PlutoGridMode.selectWithOneTap,
                          columns: columns,
                          rows: rows,
                          onLoaded: (PlutoGridOnLoadedEvent event) {},
                          onSelected: (event) {
                            try {
                              final category =
                                  controller.categories[event.rowIdx ?? 0];
                              // Navigate to category detail
                              if (category.id != null) {
                                context.go(
                                  Routes.VIEW_ACCOUNT_CATEGORY.replaceFirst(
                                    ':id',
                                    category.id!,
                                  ),
                                  extra: category,
                                );
                              }
                            } catch (e) {
                              Logger().e(e);
                            }
                          },
                          configuration: const PlutoGridConfiguration(
                            style: PlutoGridStyleConfig(
                              gridBorderRadius: BorderRadius.all(
                                Radius.circular(10),
                              ),
                            ),
                          ),
                        );
                      } else {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                IconlyLight.category,
                                size: 64,
                                color: Colors.grey.shade400,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No categories found',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Create your first category to get started',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade500,
                                ),
                              ),
                            ],
                          ),
                        );
                      }
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
