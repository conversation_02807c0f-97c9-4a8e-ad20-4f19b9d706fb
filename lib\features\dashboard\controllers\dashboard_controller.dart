import 'package:get/get.dart';
import 'package:onechurch/data/models/announcement_model.dart';
import 'package:onechurch/data/models/event_model.dart';

class DashboardController extends GetxController {
  // Dashboard data - using Rx variables for reactivity
  final userName = ''.obs;
  final totalMembers = 0.obs;
  final totalGroups = 0.obs;
  final totalEvents = 0.obs;
  final totalDonations = 0.0.obs;
  final upcomingEvents = <EventModel>[].obs;
  final announcements = <AnnouncementModel>[].obs;
  final isLoading = false.obs;

  // Load mock data for demonstration purposes
  Future<void> loadDashboardData() async {
    isLoading.value = true;

    try {
      // Mock data for demonstration
      userName.value = 'Admin';
      totalMembers.value = 250;
      totalGroups.value = 15;
      totalEvents.value = 8;
      totalDonations.value = 12500.0;

      // Mock upcoming events
      final now = DateTime.now();
      upcomingEvents.value = [
        EventModel(
          id: '1',
          title: 'Sunday Service',
          description: 'Weekly Sunday worship service',
          startDate: now.add(const Duration(days: 2)).toIso8601String(),
          endDate: now.add(const Duration(days: 2, hours: 2)).toIso8601String(),
          location: 'Main Sanctuary',
        ),
        EventModel(
          id: '2',
          title: 'Youth Group Meeting',
          description: 'Weekly youth group gathering',
          startDate: now.add(const Duration(days: 3)).toIso8601String(),
          endDate:
              now
                  .add(const Duration(days: 3, hours: 1, minutes: 30))
                  .toIso8601String(),
          location: 'Youth Center',
        ),
        EventModel(
          id: '3',
          title: 'Bible Study',
          description: 'Mid-week Bible study',
          startDate: now.add(const Duration(days: 4)).toIso8601String(),
          endDate: now.add(const Duration(days: 4, hours: 1)).toIso8601String(),
          location: 'Fellowship Hall',
        ),
      ];

      // Mock announcements
      announcements.value = [
        AnnouncementModel(
          id: '1',
          title: 'New Worship Schedule',
          description:
              'Starting next month, we will have two Sunday services at 9:00 AM and 11:00 AM.',
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
        ),
        AnnouncementModel(
          id: '2',
          title: 'Community Outreach',
          description:
              'Join us this Saturday for our community outreach program. We will be serving meals at the local shelter.',
          createdAt: DateTime.now().subtract(const Duration(days: 3)),
        ),
        AnnouncementModel(
          id: '3',
          title: 'Prayer Meeting',
          description:
              'Special prayer meeting this Friday at 7:00 PM. All are welcome to attend.',
          createdAt: DateTime.now().subtract(const Duration(days: 5)),
        ),
      ];
    } catch (e) {
      printError(info: 'Error loading dashboard data: $e');
    } finally {
      isLoading.value = false;
    }
  }
}
