import 'package:flutter/material.dart';
import 'app_theme.dart';

class AppIconStyles {
  // Gets the appropriate icon size based on screen width
  static double _getResponsiveSize(double mobileSize, {double? tabletSize, double? desktopSize}) {
    // Use MediaQuery to check screen width
    final width = MediaQueryData.fromView(WidgetsBinding.instance.window).size.width;
    final isTablet = width >= 768 && width < 1024;
    final isDesktop = width >= 1024;
    
    if (isDesktop && desktopSize != null) {
      return desktopSize;
    } else if (isTablet && tabletSize != null) {
      return tabletSize;
    } else {
      return mobileSize;
    }
  }

  // Light Theme Icon Styles
  static IconThemeData get extraSmallLight => IconThemeData(
    size: _getResponsiveSize(12, tabletSize: 14, desktopSize: 16),
    color: AppTheme.secondaryColor,
  );

  static IconThemeData get smallLight => IconThemeData(
    size: _getResponsiveSize(16, tabletSize: 18, desktopSize: 20),
    color: AppTheme.secondaryColor,
  );

  static IconThemeData get mediumLight => IconThemeData(
    size: _getResponsiveSize(20, tabletSize: 22, desktopSize: 24),
    color: AppTheme.secondaryColor,
  );

  static IconThemeData get largeLight => IconThemeData(
    size: _getResponsiveSize(24, tabletSize: 28, desktopSize: 32),
    color: AppTheme.secondaryColor,
  );

  static IconThemeData get extraLargeLight => IconThemeData(
    size: _getResponsiveSize(32, tabletSize: 36, desktopSize: 40),
    color: AppTheme.secondaryColor,
  );

  // Accent Light Theme Icon Styles
  static IconThemeData get extraSmallAccentLight => IconThemeData(
    size: _getResponsiveSize(12, tabletSize: 14, desktopSize: 16),
    color: AppTheme.accentColor,
  );

  static IconThemeData get smallAccentLight => IconThemeData(
    size: _getResponsiveSize(16, tabletSize: 18, desktopSize: 20),
    color: AppTheme.accentColor,
  );

  static IconThemeData get mediumAccentLight => IconThemeData(
    size: _getResponsiveSize(20, tabletSize: 22, desktopSize: 24),
    color: AppTheme.accentColor,
  );

  static IconThemeData get largeAccentLight => IconThemeData(
    size: _getResponsiveSize(24, tabletSize: 28, desktopSize: 32),
    color: AppTheme.accentColor,
  );

  static IconThemeData get extraLargeAccentLight => IconThemeData(
    size: _getResponsiveSize(32, tabletSize: 36, desktopSize: 40),
    color: AppTheme.accentColor,
  );

  // Dark Theme Icon Styles
  static IconThemeData get extraSmallDark => IconThemeData(
    size: _getResponsiveSize(12, tabletSize: 14, desktopSize: 16),
    color: Colors.white,
  );

  static IconThemeData get smallDark => IconThemeData(
    size: _getResponsiveSize(16, tabletSize: 18, desktopSize: 20),
    color: Colors.white,
  );

  static IconThemeData get mediumDark => IconThemeData(
    size: _getResponsiveSize(20, tabletSize: 22, desktopSize: 24),
    color: Colors.white,
  );

  static IconThemeData get largeDark => IconThemeData(
    size: _getResponsiveSize(24, tabletSize: 28, desktopSize: 32),
    color: Colors.white,
  );

  static IconThemeData get extraLargeDark => IconThemeData(
    size: _getResponsiveSize(32, tabletSize: 36, desktopSize: 40),
    color: Colors.white,
  );

  // Accent Dark Theme Icon Styles
  static IconThemeData get extraSmallAccentDark => IconThemeData(
    size: _getResponsiveSize(12, tabletSize: 14, desktopSize: 16),
    color: AppTheme.accentColorDark,
  );

  static IconThemeData get smallAccentDark => IconThemeData(
    size: _getResponsiveSize(16, tabletSize: 18, desktopSize: 20),
    color: AppTheme.accentColorDark,
  );

  static IconThemeData get mediumAccentDark => IconThemeData(
    size: _getResponsiveSize(20, tabletSize: 22, desktopSize: 24),
    color: AppTheme.accentColorDark,
  );

  static IconThemeData get largeAccentDark => IconThemeData(
    size: _getResponsiveSize(24, tabletSize: 28, desktopSize: 32),
    color: AppTheme.accentColorDark,
  );

  static IconThemeData get extraLargeAccentDark => IconThemeData(
    size: _getResponsiveSize(32, tabletSize: 36, desktopSize: 40),
    color: AppTheme.accentColorDark,
  );

  // Helper method to get icon theme for light theme
  static IconThemeData getLightIconTheme() {
    return mediumLight;
  }

  // Helper method to get icon theme for dark theme
  static IconThemeData getDarkIconTheme() {
    return mediumDark;
  }
}
