import 'dart:convert';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import '../../../data/models/member_category_model.dart';
import '../services/member_category_service.dart';

class MemberCategoryController extends GetxController {
  final MemberCategoryService _categoryService = MemberCategoryService();
  final logger = Get.find<Logger>();

  // Observable state variables
  final RxBool isLoading = false.obs;
  final RxBool isSubmitting = false.obs;
  final RxList<MemberCategory> categories = <MemberCategory>[].obs;
  final RxInt totalCategories = 0.obs;
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxString searchQuery = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchCategories();
  }

  // Fetch categories with pagination and search
  Future<void> fetchCategories({
    int page = 0,
    String? search,
    bool resetList = true,
  }) async {
    isLoading.value = true;

    try {
      final result = await _categoryService.fetchMemberCategories(
        page: page,
        search: search ?? searchQuery.value,
      );

      if (result['status'] == true) {
        final data = result['data'];
        List<dynamic> items;

        // Handle different response formats
        if (data is List<dynamic>) {
          // If data itself is a list, use it directly
          items = data;
        } else if (data is Map<String, dynamic>) {
          // If data is a map, try to extract items
          var itemsData = data['items'];
          if (itemsData is List<dynamic>) {
            items = itemsData;
          } else if (itemsData is String) {
            // If items is a string (possibly JSON), try to parse it
            try {
              items = json.decode(itemsData) as List<dynamic>;
            } catch (e) {
              logger.e('Failed to parse items string: $e');
              items = [];
            }
          } else {
            // Fallback to empty list if items is null or unexpected type
            items = [];
          }
        } else {
          // Fallback to empty list for any other case
          items = [];
        }

        final parsedCategories = _categoryService.parseCategories(items);

        if (resetList) {
          categories.clear();
        }

        categories.addAll(parsedCategories);

        // Safely convert pagination data with proper type handling
        if (data is Map<String, dynamic>) {
          // Handle totalItems
          var totalItemsData = data['totalItems'];
          if (totalItemsData is int) {
            totalCategories.value = totalItemsData;
          } else if (totalItemsData is String) {
            totalCategories.value = int.tryParse(totalItemsData) ?? 0;
          } else {
            totalCategories.value = 0;
          }

          // Handle currentPage
          var currentPageData = data['currentPage'];
          if (currentPageData is int) {
            currentPage.value = currentPageData;
          } else if (currentPageData is String) {
            currentPage.value = int.tryParse(currentPageData) ?? 0;
          } else {
            currentPage.value = 0;
          }

          // Handle totalPages
          var totalPagesData = data['totalPages'];
          if (totalPagesData is int) {
            totalPages.value = totalPagesData;
          } else if (totalPagesData is String) {
            totalPages.value = int.tryParse(totalPagesData) ?? 0;
          } else {
            totalPages.value = 0;
          }
        } else {
          // Default values if data is not a map
          totalCategories.value = 0;
          currentPage.value = 0;
          totalPages.value = 0;
        }
      } else {
        logger.e('Error fetching categories: ${result['message']}');
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to load categories',
        );
      }
    } catch (e) {
      logger.e('Exception fetching categories: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
    } finally {
      isLoading.value = false;
    }
  }

  // Load more categories (pagination)
  Future<void> loadMoreCategories() async {
    if (currentPage.value < totalPages.value - 1 && !isLoading.value) {
      await fetchCategories(page: currentPage.value + 1, resetList: false);
    }
  }

  // Search categories
  Future<void> searchCategories(String query) async {
    searchQuery.value = query;
    await fetchCategories(search: query);
  }

  // Create a new category
  Future<bool> createCategory({
    required String title,
    required String description,
    required String code,
    required bool isGeneral,
  }) async {
    isSubmitting.value = true;

    try {
      final result = await _categoryService.createMemberCategory(
        title: title,
        description: description,
        code: code,
        isGeneral: isGeneral,
      );

      if (result['status'] == true) {
        ToastUtils.showSuccessToast('Success', 'Category created successfully');

        // Refresh categories list
        await fetchCategories();
        return true;
      } else {
        logger.e('Error creating category: ${result['message']}');
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to create category',
        );
        return false;
      }
    } catch (e) {
      logger.e('Exception creating category: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Update an existing category
  Future<bool> updateCategory({
    required String id,
    required String title,
    required String description,
    required String code,
  }) async {
    isSubmitting.value = true;

    try {
      final result = await _categoryService.updateMemberCategory(
        id: id,
        title: title,
        description: description,
        code: code,
      );

      if (result['status'] == true) {
        ToastUtils.showSuccessToast('Success', 'Category updated successfully');

        // Refresh categories list
        await fetchCategories();
        return true;
      } else {
        logger.e('Error updating category: ${result['message']}');
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to update category',
        );
        return false;
      }
    } catch (e) {
      logger.e('Exception updating category: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Delete a category
  Future<bool> deleteCategory(String id) async {
    isSubmitting.value = true;

    try {
      final result = await _categoryService.deleteMemberCategory(id);

      if (result['status'] == true) {
        ToastUtils.showSuccessToast('Success', 'Category deleted successfully');

        // Refresh categories list
        await fetchCategories();
        return true;
      } else {
        logger.e('Error deleting category: ${result['message']}');
        ToastUtils.showErrorToast(
          'Error',
          result['message'] ?? 'Failed to delete category',
        );
        return false;
      }
    } catch (e) {
      logger.e('Exception deleting category: $e');
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }
}
