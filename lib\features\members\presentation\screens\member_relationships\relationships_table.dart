import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../../controllers/relationship_controller.dart';
import '../../../models/relationship_models.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
// Import the split widget files
import 'show_add_relationship.dart';
import 'confirm_delete_dialogs.dart';

/// Build relationships table
Widget buildRelationshipsTable(
  BuildContext context,
  RelationshipController relationshipController,
  String memberId,
) {
  return Obx(() {
    if (relationshipController.isLoadingRelationships.value) {
      return const Center(child: CircleLoadingAnimation());
    }

    if (relationshipController.relationships.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              IconlyBold.user3,
              size: 48,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            ),
            Gap(16.h),
            Text(
              'No relationships found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            Gap(8.h),
            CustomButton(
              onPressed:
                  () => showAddRelationship(
                    context,
                    relationshipController,
                    memberId,
                  ),
              icon: const Icon(IconlyLight.addUser),
              label: const Text('Add Relationship'),
            
            ),
          ],
        ),
      );
    }

    final columns = <PlutoColumn>[
      PlutoColumn(
        title: 'Relationship',
        field: 'relationship',
        type: PlutoColumnType.text(),
        width: 150,
        titleTextAlign: PlutoColumnTextAlign.start,
        textAlign: PlutoColumnTextAlign.start,
        renderer: (rendererContext) {
          final relationship =
              rendererContext.row.cells['relationshipObj']!.value
                  as MemberRelationship;
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  relationship.relationshipType?.title ?? 'Unknown',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                  overflow: TextOverflow.ellipsis,
                ),
                if (relationship.description != null &&
                    relationship.description!.isNotEmpty)
                  Text(
                    relationship.description!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withOpacity(0.6),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Related Member',
        field: 'relatedMember',
        type: PlutoColumnType.text(),
        width: 200,
        titleTextAlign: PlutoColumnTextAlign.start,
        textAlign: PlutoColumnTextAlign.start,
        renderer: (rendererContext) {
          final relationship =
              rendererContext.row.cells['relationshipObj']!.value
                  as MemberRelationship;
          final isFromMember = relationship.fromMemberId == memberId;

          String memberName =
              isFromMember
                  ? relationship.getToMemberName()
                  : relationship.getFromMemberName();

          String? profileUrl =
              isFromMember
                  ? relationship.getToMemberProfileUrl()
                  : relationship.getFromMemberProfileUrl();

          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 14.r,
                  backgroundColor: Theme.of(
                    context,
                  ).colorScheme.primary.withOpacity(0.2),
                  backgroundImage:
                      profileUrl != null ? NetworkImage(profileUrl) : null,
                  child:
                      profileUrl == null
                          ? Icon(
                            IconlyBold.profile,
                            color: Theme.of(context).colorScheme.primary,
                            size: 14.r,
                          )
                          : null,
                ),
                Gap(8.w),
                Expanded(
                  child: Text(
                    memberName,
                    style: Theme.of(context).textTheme.bodyMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Direction',
        field: 'direction',
        type: PlutoColumnType.text(),
        width: 200,
        titleTextAlign: PlutoColumnTextAlign.start,
        textAlign: PlutoColumnTextAlign.start,
        renderer: (rendererContext) {
          final relationship =
              rendererContext.row.cells['relationshipObj']!.value
                  as MemberRelationship;
          final isFromMember = relationship.fromMemberId == memberId;

          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
              decoration: BoxDecoration(
                color:
                    isFromMember
                        ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                        : Theme.of(
                          context,
                        ).colorScheme.secondary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isFromMember
                        ? IconlyLight.arrowRight
                        : IconlyLight.arrowLeft,
                    size: 14,
                    color:
                        isFromMember
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.secondary,
                  ),
                  Gap(4.w),
                  Text(
                    isFromMember ? 'Outgoing' : 'Incoming',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color:
                          isFromMember
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.secondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 80,
        titleTextAlign: PlutoColumnTextAlign.center,
        textAlign: PlutoColumnTextAlign.center,
        renderer: (rendererContext) {
          return Center(
            child: IconButton(
              icon: Icon(
                IconlyLight.delete,
                size: 20,
                color: Theme.of(context).colorScheme.error,
              ),
              onPressed: () {
                final relationship =
                    rendererContext.row.cells['relationshipObj']!.value
                        as MemberRelationship;
                confirmDeleteRelationship(
                  context,
                  relationshipController,
                  relationship,
                  memberId,
                );
              },
              tooltip: 'Delete',
            ),
          );
        },
      ),
    ];

    final rows =
        relationshipController.relationships.map((relationship) {
          return PlutoRow(
            cells: {
              'relationship': PlutoCell(
                value: relationship.relationshipType?.title ?? 'Unknown',
              ),
              'relatedMember': PlutoCell(value: 'Member'),
              'direction': PlutoCell(
                value:
                    relationship.fromMemberId == memberId
                        ? 'Outgoing'
                        : 'Incoming',
              ),
              'actions': PlutoCell(value: ''),
              'relationshipObj': PlutoCell(value: relationship),
            },
          );
        }).toList();

    return PlutoGrid(
      columns: columns,
      rows: rows,
      onLoaded: (PlutoGridOnLoadedEvent event) {
        // relationshipsStateManager = event.stateManager;
      },
      configuration: PlutoGridConfiguration(
        style: PlutoGridStyleConfig(
          borderColor: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          gridBackgroundColor: Theme.of(context).colorScheme.surface,
          rowHeight: 48,
          columnTextStyle: Theme.of(context).textTheme.bodyMedium!,
          cellTextStyle: Theme.of(context).textTheme.bodyMedium!,
          iconColor: Theme.of(context).colorScheme.primary,
          activatedColor: Theme.of(
            context,
          ).colorScheme.primary.withOpacity(0.1),
          gridBorderRadius: BorderRadius.circular(8.r),
        ),
      ),
    );
  });
}
