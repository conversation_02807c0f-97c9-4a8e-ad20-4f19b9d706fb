import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../data/models/member_model.dart';
import '../models/member_payload_model.dart';

class MemberService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();

  // Initialize the service
  MemberService() {
    _httpService.initializeDio();
  }

  // Fetch members with pagination and filters
  Future<Map<String, dynamic>> fetchMembers({
    required int page,
    required int size,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? id,
  }) async {
    try {
      // Build URL with query parameters
      String url = "${ApiUrls.getMembers}?";

      url += "page=$page";
      url += "&size=$size";

      // Add optional filters to URL
      if (searchQuery != null && searchQuery.isNotEmpty) {
        url += "&identifier=${Uri.encodeComponent(searchQuery)}";
      }

      if (startDate != null) {
        // Format date with time
        final formattedStart = startDate.toUtc().toIso8601String();
        url += "&start_date=${Uri.encodeComponent(formattedStart)}";
      }

      if (endDate != null) {
        // Format date with time
        final formattedEnd = endDate.toUtc().toIso8601String();
        url += "&end_date=${Uri.encodeComponent(formattedEnd)}";
      }

      if (id != null && id.isNotEmpty) {
        url += "&id=${Uri.encodeComponent(id)}";
      }

      final response = await _httpService.request(url: url, method: Method.GET);

      return response.data;
    } catch (e) {
      logger.e('Error fetching members: $e');
      rethrow;
    }
  }

  // Get a single member by ID
  Future<Map<String, dynamic>> getMemberById(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.getSingleMember}?member_id=$id",
        method: Method.GET,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching member details: $e');
      rethrow;
    }
  }

  // Register multiple members using MemberPayloadModel
  Future<Map<String, dynamic>> registerMembers(
    List<MemberPayloadModel> members,
  ) async {
    try {
      final List<Map<String, dynamic>> formattedMembers =
          members.map((member) => member.toJson()).toList();

      final response = await _httpService.request(
        url: ApiUrls.onboardCustomer,
        method: Method.POST,
        params: formattedMembers,
      );

      return response.data;
    } catch (e) {
      logger.e('Error registering member: $e');
      rethrow;
    }
  }

  // Parse member items from API response
  List<MemberModel> parseMembers(List<dynamic> items) {
    return items.map((item) => MemberModel.fromJson(item)).toList();
  }

  // Update a member
  Future<Map<String, dynamic>> updateMember(
    String id,
    Map<String, dynamic> memberData,
  ) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.getSingleMember}$id/",
        method: Method.PUT,
        params: memberData,
      );

      return response.data;
    } catch (e) {
      logger.e('Error updating member: $e');
      rethrow;
    }
  }

  // Fetch member categories
  Future<Map<String, dynamic>> fetchMemberCategories({
    int page = 0,
    String? search,
  }) async {
    try {
      // Build URL with query parameters
      String url = "${ApiUrls.memberCategories}?";
      url += "page=$page";
      url += "&size=10";

      // Add search parameter if provided
      if (search != null && search.isNotEmpty) {
        url += "&search=${Uri.encodeComponent(search)}";
      }

      final response = await _httpService.request(url: url, method: Method.GET);
      return response.data;
    } catch (e) {
      logger.e('Error fetching member categories: $e');
      return {
        'status': false,
        'message': 'Failed to load categories: $e',
        'data': null,
      };
    }
  }
}
