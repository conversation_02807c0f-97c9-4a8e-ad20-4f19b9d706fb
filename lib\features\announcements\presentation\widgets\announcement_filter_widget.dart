import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import '../../controllers/announcement_controller.dart';

class AnnouncementFilterWidget extends StatelessWidget {
  const AnnouncementFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AnnouncementController>();

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(controller, context),
            const Divider(height: 24),
            _buildFilterControls(controller, context),
            const SizedBox(height: 16),
            _buildActionButtons(controller),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(AnnouncementController controller, BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Row(
      children: [
        Icon(IconlyLight.filter, color: colorScheme.primary),
        const SizedBox(width: 8),
        Text(
          'Filter Announcements',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const Spacer(),
        _buildClearButton(controller, context),
      ],
    );
  }

  Widget _buildClearButton(
    AnnouncementController controller,
    BuildContext context,
  ) {
    return TextButton(
      onPressed: controller.clearFilters,
      style: TextButton.styleFrom(
        foregroundColor: Colors.red,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: Row(
        children: [
          const Icon(Icons.clear_all, size: 18),
          const SizedBox(width: 4),
          Text('Clear All', style: Theme.of(context).textTheme.labelMedium),
        ],
      ),
    );
  }

  Widget _buildFilterControls(
    AnnouncementController controller,
    BuildContext context,
  ) {
    final isMobile = MediaQuery.of(context).size.width < 600;

    return Wrap(
      runSpacing: 12,
      spacing: 12,
      children: [
        // Search field
        if (isMobile) ...[
          SizedBox(width: 180.w, child: buildSearchField(controller, context)),
        ],

        // Title filter
        // SizedBox(width: 180.w, child: _buildTitleField(controller, context)),

        // Status filter
        SizedBox(width: 150.w, child: _buildStatusFilter(controller, context)),

        // Date filters
        SizedBox(
          width: 130.w,
          child: _buildDateField(context, controller, isStartDate: true),
        ),

        // SizedBox(
        //   width: 130.w,
        //   child: _buildDateField(context, controller, isStartDate: false),
        // ),
      ],
    );
  }

  Widget buildSearchField(
    AnnouncementController controller,
    BuildContext context,
  ) {
    final colorScheme = Theme.of(context).colorScheme;

    return CustomTextFormField(
      controller: controller.searchController,
      labelText: 'Search',
      hintText: 'Search announcements...',
      prefixIcon: Icon(IconlyLight.search, color: colorScheme.primary),
      suffixIcon: _buildClearIcon(() {
        controller.searchController.clear();
        controller.setSearchQuery('');
      }, showClear: controller.searchController.text.isNotEmpty),

      onFieldSubmitted: (value) {
        controller.setSearchQuery(value);
        controller.fetchAnnouncements();
      },
      onChanged: (value) => controller.setSearchQuery(value),
    );
  }

  Widget _buildStatusFilter(
    AnnouncementController controller,
    BuildContext context,
  ) {
    final colorScheme = Theme.of(context).colorScheme;

    return Obx(
      () => DropdownButtonFormField<String>(
        decoration: InputDecoration(
          labelText: 'Status',
          prefixIcon: Icon(IconlyLight.tickSquare, color: colorScheme.primary),
          border: _inputBorder(),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 14,
            horizontal: 16,
          ),
        ),
        value:
            controller.statusFilter.value.isEmpty
                ? null
                : controller.statusFilter.value,
        items: const [
          DropdownMenuItem<String>(value: '', child: Text('All Statuses')),
          DropdownMenuItem<String>(
            value: 'PUBLISHED',
            child: Text('Published'),
          ),
          DropdownMenuItem<String>(value: 'DRAFT', child: Text('Draft')),
          DropdownMenuItem<String>(value: 'ARCHIVED', child: Text('Archived')),
        ],
        onChanged: (value) {
          controller.setStatusFilter(value ?? '');
        },
        icon: Icon(Icons.arrow_drop_down, color: colorScheme.primary),
      ),
    );
  }

  Widget _buildDateField(
    BuildContext context,
    AnnouncementController controller, {
    required bool isStartDate,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Obx(() {
      final date =
          isStartDate ? controller.startDate.value : controller.endDate.value;
      final label = 'Date';

      return CustomTextFormField(
        readOnly: true,
        labelText: label,
        prefixIcon: Icon(IconlyLight.calendar, color: colorScheme.primary),
        suffixIcon: _buildClearIcon(
          () =>
              isStartDate
                  ? controller.setDateFilters(null, controller.endDate.value)
                  : controller.setDateFilters(controller.startDate.value, null),
          showClear: date != null,
        ),
        hintText: 'Select $label',
        controller: TextEditingController(
          text: date != null ? DateFormat('MMM dd, yyyy').format(date) : null,
        ),
        onTap: () => _selectDate(context, controller, isStartDate),
      );
    });
  }

  Future<void> _selectDate(
    BuildContext context,
    AnnouncementController controller,
    bool isStartDate,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          isStartDate
              ? controller.startDate.value ?? DateTime.now()
              : controller.endDate.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      if (isStartDate) {
        controller.setDateFilters(picked, controller.endDate.value);
      } else {
        // Set to end of day for inclusive filtering
        final endOfDay = DateTime(
          picked.year,
          picked.month,
          picked.day,
          23,
          59,
          59,
        );
        controller.setDateFilters(controller.startDate.value, endOfDay);
      }
    }
  }

  Widget _buildActionButtons(AnnouncementController controller) {
    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        onPressed: controller.fetchAnnouncements,
        icon: const Icon(IconlyLight.filter),
        label: const Text('Apply Filters'),
        text: '',
      ),
    );
  }

  Widget _buildClearIcon(VoidCallback onPressed, {bool showClear = true}) {
    if (!showClear) return const SizedBox.shrink();

    return IconButton(
      icon: const Icon(Icons.clear, size: 18),
      onPressed: onPressed,
      splashRadius: 20,
    );
  }

  OutlineInputBorder _inputBorder() {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
      borderSide: const BorderSide(width: 1),
    );
  }
}
