import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../data/models/notification_model.dart';
import '../services/notification_service.dart';
import '../../../core/app/services/auth_service.dart';

class NotificationController extends GetxController {
  final NotificationService _notificationService = NotificationService();
  final AuthService _authService = Get.find<AuthService>();
  final logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<NotificationModel> notifications = <NotificationModel>[].obs;
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalItems = 0.obs;
  final RxInt pageSize = 10.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;
  final RxString searchQuery = ''.obs;
  final TextEditingController searchController = TextEditingController();
  final RxInt unreadCount = 0.obs;
  final RxBool showUnreadOnly = false.obs;

  @override
  void onInit() {
    super.onInit();
    fetchNotifications();
    fetchUnreadCount();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    currentPage.value = 0; // Reset to first page when searching
    fetchNotifications();
  }

  // Toggle unread only filter
  void toggleUnreadOnly() {
    showUnreadOnly.value = !showUnreadOnly.value;
    currentPage.value = 0; // Reset to first page when filtering
    fetchNotifications();
  }

  // Fetch notifications
  Future<void> fetchNotifications() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final result = await _notificationService.fetchNotifications(
        page: currentPage.value,
        pageSize: pageSize.value,
        searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
        unreadOnly: showUnreadOnly.value,
      );

      notifications.value = result['notifications'] as List<NotificationModel>;
      totalPages.value = result['total_pages'] as int;
      totalItems.value = result['total_items'] as int;
      currentPage.value = result['current_page'] as int;

      isLastPage.value = currentPage.value >= totalPages.value - 1;
      isFirstPage.value = currentPage.value == 0;
    } catch (e) {
      errorMessage.value = e.toString();
      logger.e('Error fetching notifications: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Fetch unread count
  Future<void> fetchUnreadCount() async {
    try {
      final count = await _notificationService.getUnreadCount();
      unreadCount.value = count;
    } catch (e) {
      logger.e('Error fetching unread count: $e');
    }
  }

  // Mark notification as read
  Future<void> markAsRead(NotificationModel notification) async {
    try {
      final success = await _notificationService.markAsRead(notification.id);
      if (success) {
        // Update the notification in the list
        final index = notifications.indexWhere((n) => n.id == notification.id);
        if (index != -1) {
          final updatedNotification = notification.copyWith(
            readAt: DateTime.now(),
          );
          notifications[index] = updatedNotification;
          notifications.refresh();
        }

        // Update unread count
        if (!notification.isRead.value) {
          unreadCount.value--;
        }
      }
    } catch (e) {
      logger.e('Error marking notification as read: $e');
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      final success = await _notificationService.markAllAsRead();
      if (success) {
        // Update all notifications in the list
        for (var i = 0; i < notifications.length; i++) {
          if (!notifications[i].isRead.value) {
            final updatedNotification = notifications[i].copyWith(
              readAt: DateTime.now(),
            );
            notifications[i] = updatedNotification;
          }
        }
        notifications.refresh();

        // Reset unread count
        unreadCount.value = 0;
      }
    } catch (e) {
      logger.e('Error marking all notifications as read: $e');
    }
  }

  // Delete notification
  Future<void> deleteNotification(NotificationModel notification) async {
    try {
      final success = await _notificationService.deleteNotification(notification.id);
      if (success) {
        // Remove the notification from the list
        notifications.removeWhere((n) => n.id == notification.id);

        // Update unread count if needed
        if (!notification.isRead.value) {
          unreadCount.value--;
        }
      }
    } catch (e) {
      logger.e('Error deleting notification: $e');
    }
  }

  // Load next page
  void nextPage() {
    if (!isLastPage.value) {
      currentPage.value++;
      fetchNotifications();
    }
  }

  // Load previous page
  void previousPage() {
    if (!isFirstPage.value) {
      currentPage.value--;
      fetchNotifications();
    }
  }

  // Refresh notifications
  Future<void> refreshNotifications() async {
    currentPage.value = 0;
    await fetchNotifications();
    await fetchUnreadCount();
  }

  // Check if user is admin
  bool get isAdmin => _authService.isAdmin;
}