import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../../core/app/constants/routes.dart';
import '../../../core/app/widgets/loading_animations.dart';
import '../controllers/group_controller.dart';
import 'widgets/group_filter_widget.dart';

class ViewGroupsGrid extends StatefulWidget {
  const ViewGroupsGrid({super.key});

  @override
  State<ViewGroupsGrid> createState() => _ViewGroupsGridState();
}

class _ViewGroupsGridState extends State<ViewGroupsGrid> {
  late List<PlutoColumn> columns;
  late List<PlutoRow> rows;
  final controller = Get.put(GroupController());

  @override
  void initState() {
    setColumns();
    super.initState();
  }

  void _confirmDeleteGroup(BuildContext context, String groupId) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Group'),
            content: const Text('Are you sure you want to delete this group?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                onPressed: () async {
                  Navigator.pop(context);
                  final success = await controller.deleteGroup(groupId);
                  if (success && context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Group deleted successfully'),
                        backgroundColor: Colors.green,
                      ),
                    );
                    // Refresh the grid
                    controller.refreshGroups();
                  }
                },
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  void setColumns() {
    columns = [
      PlutoColumn(
        title: 'Title',
        field: 'title',
        type: PlutoColumnType.text(),
        width: 200,
        enableSorting: true,
        enableContextMenu: false,
        enableDropToResize: true,
        titleTextAlign: PlutoColumnTextAlign.left,
        textAlign: PlutoColumnTextAlign.left,
        renderer: (rendererContext) {
          return Text(
            rendererContext.cell.value ?? '',
            style: const TextStyle(fontWeight: FontWeight.bold),
            overflow: TextOverflow.ellipsis,
          );
        },
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        width: 250,
        enableSorting: true,
        enableContextMenu: false,
        enableDropToResize: true,
        titleTextAlign: PlutoColumnTextAlign.left,
        textAlign: PlutoColumnTextAlign.left,
        renderer: (rendererContext) {
          return Text(
            rendererContext.cell.value ?? '',
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          );
        },
      ),
      PlutoColumn(
        title: 'Members',
        field: 'members',
        type: PlutoColumnType.number(),
        width: 100,
        enableSorting: true,
        enableContextMenu: false,
        enableDropToResize: true,
        titleTextAlign: PlutoColumnTextAlign.left,
        textAlign: PlutoColumnTextAlign.left,
        renderer: (rendererContext) {
          return Text(
            rendererContext.cell.value?.toString() ?? '0',
            style: const TextStyle(fontWeight: FontWeight.bold),
          );
        },
      ),
      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
        enableSorting: true,
        enableContextMenu: false,
        enableDropToResize: true,
        titleTextAlign: PlutoColumnTextAlign.left,
        textAlign: PlutoColumnTextAlign.left,
        renderer: (rendererContext) {
          final status =
              rendererContext.cell.value?.toString().toLowerCase() ?? '';
          Color statusColor;

          switch (status) {
            case 'active':
              statusColor = Colors.green;
              break;
            case 'inactive':
              statusColor = Colors.orange;
              break;
            case 'draft':
              statusColor = Colors.grey;
              break;
            default:
              statusColor = Colors.blue;
          }

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status.capitalize,
              style: TextStyle(color: statusColor, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Created Date',
        field: 'createdAt',
        type: PlutoColumnType.date(),
        width: 150,
        enableSorting: true,
        enableContextMenu: false,
        enableDropToResize: true,
        titleTextAlign: PlutoColumnTextAlign.left,
        textAlign: PlutoColumnTextAlign.left,
        renderer: (rendererContext) {
          return Text(rendererContext.cell.value ?? 'N/A');
        },
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: false,
        enableContextMenu: false,
        enableDropToResize: true,
        titleTextAlign: PlutoColumnTextAlign.center,
        textAlign: PlutoColumnTextAlign.center,
        renderer: (rendererContext) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(
                  IconlyLight.edit,
                  size: 18,
                  color: Colors.blue,
                ),
                onPressed: () {
                  final groupId = rendererContext.cell.value;
                  if (groupId != null) {
                    final group = controller.groups.firstWhereOrNull(
                      (g) => g.id == groupId,
                    );
                    if (group != null) {
                      context.push(
                        Routes.GROUP_EDIT.replaceFirst(':id', groupId),
                        extra: group,
                      );
                    }
                  }
                },
                tooltip: 'Edit',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
              IconButton(
                icon: const Icon(
                  IconlyLight.delete,
                  size: 18,
                  color: Colors.red,
                ),
                onPressed: () {
                  final groupId = rendererContext.cell.value;
                  if (groupId != null) {
                    _confirmDeleteGroup(context, groupId);
                  }
                },
                tooltip: 'Delete',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
            ],
          );
        },
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final showFilter = false.obs;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Groups'),
        actions: [
          if (MediaQuery.of(context).size.width > 600) ...[
            SizedBox(
              width: 180.w,
              child: GroupFilterWidget().buildSearchField(
                Get.find<GroupController>(),
                context,
              ),
            ),
            const SizedBox(width: 12),
          ],
          Obx(
            () => IconButton(
              onPressed: () {
                showFilter.value = !showFilter.value;
              },
              icon:
                  showFilter.value
                      ? Icon(Icons.filter_alt)
                      : Icon(Icons.filter_alt_outlined),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.go(Routes.GROUP_CREATE);
        },
        child: const Icon(Icons.add),
      ),
      body: Center(
        child: Column(
          children: [
            Obx(
              () =>
                  showFilter.value
                      ? const GroupFilterWidget()
                      : const SizedBox(),
            ),
            Expanded(
              child: Card(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                elevation: 4,
                child: Obx(() {
                  if (controller.isLoading.value) {
                    return const Center(child: LoadingAnimations());
                  } else if (controller.groups.isNotEmpty) {
                    // group data to PlutoRows-----> one by one
                    if (kDebugMode) {
                      debugPrint("Groups Fetched: ${controller.groups.length}");
                    }
                    rows =
                        controller.groups.map((group) {
                          return PlutoRow(
                            cells: {
                              'title': PlutoCell(value: group.title),
                              'description': PlutoCell(
                                value: group.description,
                              ),
                              'members': PlutoCell(
                                value: group.members?.length ?? 0,
                              ),
                              'status': PlutoCell(value: group.status),
                              'createdAt': PlutoCell(
                                value:
                                    group.createdAt != null
                                        ? DateFormat(
                                          'dd MMM yyyy',
                                        ).format(group.createdAt!)
                                        : 'N/A',
                              ),
                              'actions': PlutoCell(value: group.id),
                            },
                          );
                        }).toList();

                    return PlutoGrid(
                      mode: PlutoGridMode.selectWithOneTap,
                      columns: columns,
                      rows: rows,
                      onLoaded: (PlutoGridOnLoadedEvent event) {},
                      onSelected: (event) {
                        try {
                          final rowIndex = event.rowIdx ?? 0;
                          final selectedGroup = controller.groups[rowIndex];
                          context.go(
                            Routes.GROUP_DETAIL.replaceFirst(
                              ':id',
                              selectedGroup.id ?? '',
                            ),
                            extra: selectedGroup,
                          );
                        } catch (e) {
                          Logger().e(e);
                        }
                      },
                      onRowDoubleTap: (PlutoGridOnRowDoubleTapEvent event) {
                        try {
                          final rowIndex = event.rowIdx;
                          final selectedGroup = controller.groups[rowIndex];
                          context.go(
                            Routes.GROUP_DETAIL.replaceFirst(
                              ':id',
                              selectedGroup.id ?? '',
                            ),
                            extra: selectedGroup,
                          );
                        } catch (e) {
                          Logger().e(e);
                        }
                      },
                      configuration: PlutoGridConfiguration(
                        style: PlutoGridStyleConfig(
                          activatedColor: const Color.fromARGB(
                            255,
                            165,
                            205,
                            253,
                          ),
                          cellTextStyle: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Color.fromARGB(255, 216, 108, 40),
                          ),
                          columnTextStyle: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blueGrey,
                          ),
                        ),
                        columnSize: const PlutoGridColumnSizeConfig(
                          autoSizeMode: PlutoAutoSizeMode.scale,
                        ),
                      ),
                    );
                  } else if (controller.errorMessage.string.isNotEmpty) {
                    return Center(
                      child: Text(
                        controller.errorMessage.string,
                        style: const TextStyle(color: Colors.red),
                      ),
                    );
                  } else {
                    // Show empty grid
                    return PlutoGrid(
                      mode: PlutoGridMode.selectWithOneTap,
                      columns: columns,
                      rows: [],
                      onLoaded: (PlutoGridOnLoadedEvent event) {},
                      configuration: PlutoGridConfiguration(
                        style: PlutoGridStyleConfig(
                          activatedColor: const Color.fromARGB(
                            255,
                            165,
                            205,
                            253,
                          ),
                          cellTextStyle: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Color.fromARGB(255, 216, 108, 40),
                          ),
                          columnTextStyle: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blueGrey,
                          ),
                        ),
                        columnSize: const PlutoGridColumnSizeConfig(
                          autoSizeMode: PlutoAutoSizeMode.scale,
                        ),
                      ),
                      noRowsWidget: const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.people, size: 48, color: Colors.grey),
                            SizedBox(height: 16),
                            Text('No groups available'),
                          ],
                        ),
                      ),
                    );
                  }
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
