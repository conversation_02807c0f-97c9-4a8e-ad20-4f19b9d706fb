💡 RESPONSE[200] => DATA: {data: {first: true, items:
[{CreatedAt: 2025-05-30T17:24:19.64Z, DeletedAt: null, ID: 6938,
UpdatedAt: 2025-05-30T17:24:20.209Z, characters: 75, cost_org:  
0.3, created_by_id: , external_user_id: 1074, message: Dear     
customer, payment received. Thank you for choosing Elsafrica    
Networks!, message_hash:
70da44b7b86c801d9253e34e6504406f37dfc94228277ebb6379d0d88e8830cc
, message_id: 9f095e52-d399-4760-b5e0-f5ce6bdb322a, num_sms: 1, 
organisation: , organisation_name: , price_per_unit: 0.3,       
sender_email: , sender_id: ONEKITTY, sender_phone: ************,
status: , third_party_charge: 0.3, total_charges: 0.6,
total_recipients: 1, transaction_id: 6359}, {CreatedAt:
2025-05-30T15:47:48.847Z, DeletedAt: null, ID: 6937, UpdatedAt: 
2025-05-30T15:47:49.412Z, characters: 31, cost_org: 0.3,        
created_by_id: , external_user_id: 0, message: Your verification
code is 44504, message_hash:
ec2c461b90759fb7b379e1ac17dfc154ba39d8aca7352b9c28dd835243b0f77b
, message_id: 9f093bce-c5b5-488b-aab7-0d2747830a32, num_sms: 1, 
organisation: , organisation_name: , price_per_unit: 0.3,       
sender_email: , sender_id: ONEKITTY, sender_phone: , status: ,  
third_party_charge: 0.3, total_charges: 0.6, total_recipients:  
1}, {CreatedAt: 2025-05-30T10:21:27.431Z, DeletedAt: null, ID:  
6936, UpdatedAt: 2025-05-30T10:21:27.987Z, characters: 75,      
cost_org: 0.3, created_by_id: , external_user_id: 1074, message:
Dear customer, payment received. Thank you for choosing
Elsafrica Networks!, message_hash:
70da44b7b86c801d9253e34e6504406f37dfc94228277ebb6379d0d88e8830cc
, message_id: 9f08c717-e505-4590-a929-5987831e0732, num_sms: 1, 
organisation: , organisation_name: , price_per_unit: 0.3,       
sender_email: , sender_id: ONEKITTY, sender_phone: ************,
status: , third_party_charge: 0.3, total_charges: 0.6,
total_recipients: 1, transaction_id: 6358}, {CreatedAt:
2025-05-30T05:00:03.635Z, DeletedAt: null, ID: 6935, UpdatedAt: 
2025-05-30T05:00:04.206Z, characters: 141, cost_org: 0.3,       
created_by_id: , external_user_id: 1074, message: Dear customer,
your internet bill is due on Jun 4th 2025. Please pay to avoid  
service disruption.
│ 💡 Pay Bill: 247247
│ 💡 Account: 051015
│ 💡 Elsafrica!, message_hash:
c748b96090a98fa00536d31f907dfc6c762c9642e894bbd070d38a0d61aebb31
, message_id: 9f085427-2c47-48b1-950c-d397b31103a4, num_sms: 1, 
organisation: , organisation_name: , price_per_unit: 0.3,       
sender_email: , sender_id: ONEKITTY, sender_phone: ************,
status: , third_party_charge: 0.3, total_charges: 0.6,
total_recipients: 1, transaction_id: 6356}, {CreatedAt:
2025-05-30T05:00:03.623Z, DeletedAt: null, ID: 6934, UpdatedAt: 
2025-05-30T05:00:04.207Z, characters: 142, cost_org: 0.3,       
created_by_id: , external_user_id: 1074, message: Dear customer,
your internet bill is due on May 30th 2025. Please pay to avoid 
service disruption.
│ 💡 Pay Bill: 247247
│ 💡 Account: 051015
│ 💡 Elsafrica!, message_hash:
1efdbcf4f31cda5ea3680d742e2f085b80b2999a3050bbbd12ce35b4141ab9f2
, message_id: 9f085427-2c47-42d3-8c2f-a43562a68b9e, num_sms: 1, 
organisation: , organisation_name: , price_per_unit: 0.3,       
sender_email: , sender_id: ONEKITTY, sender_phone: ************,
status: , third_party_charge: 0.3, total_charges: 0.6,
total_recipients: 1, transaction_id: 6357}, {CreatedAt:
2025-05-30T05:00:03.506Z, DeletedAt: null, ID: 6933, UpdatedAt: 
2025-05-30T05:00:04.054Z, characters: 141, cost_org: 0.3,       
created_by_id: , external_user_id: 1074, message: Dear customer,
your internet bill is due on Jun 4th 2025. Please pay to avoid  
service disruption.
│ 💡 Pay Bill: 247247
│ 💡 Account: 051015
│ 💡 Elsafrica!, message_hash:
c748b96090a98fa00536d31f907dfc6c762c9642e894bbd070d38a0d61aebb31
, message_id: 9f085426-f189-4107-b045-5704352b6097, num_sms: 1, 
organisation: , organisation_name: , price_per_unit: 0.3,       
sender_email: , sender_id: ONEKITTY, sender_phone: ************,
status: , third_party_charge: 0.3, total_charges: 0.6,
total_recipients: 1, transaction_id: 6355}, {CreatedAt:
2025-05-30T05:00:03.172Z, DeletedAt: null, ID: 6932, UpdatedAt: 
2025-05-30T05:00:03.937Z, characters: 142, cost_org: 0.3,       
created_by_id: , external_user_id: 1074, message: Dear customer,
your internet bill is due on May 30th 2025. Please pay to avoid 
service disruption.
│ 💡 Pay Bill: 247247
│ 💡 Account: 051015
│ 💡 Elsafrica!, message_hash:
1efdbcf4f31cda5ea3680d742e2f085b80b2999a3050bbbd12ce35b4141ab9f2
, message_id: 9f085426-c6b4-40f3-8de9-fb86a1ea76f8, num_sms: 1, 
organisation: , organisation_name: , price_per_unit: 0.3,       
sender_email: , sender_id: ONEKITTY, sender_phone: ************,
status: , third_party_charge: 0.3, total_charges: 0.6,
total_recipients: 1, transaction_id: 6354}, {CreatedAt:
2025-05-29T17:25:03.639Z, DeletedAt: null, ID: 6931, UpdatedAt: 
2025-05-29T17:25:04.213Z, characters: 31, cost_org: 0.3,        
created_by_id: , external_user_id: 0, message: Your verification
code is 30235, message_hash:
d1572aba989a97bef9984601c283cb039a8ac546bc581e240e40124679fdb4dd
, message_id: 9f075b9a-07ea-4402-a9af-ecc0c9ebcf14, num_sms: 1, 
organisation: , organisation_name: , price_per_unit: 0.3,       
sender_email: , sender_id: ONEKITTY, sender_phone: , status: ,  
third_party_charge: 0.3, total_charges: 0.6, total_recipients:  
1}, {CreatedAt: 2025-05-29T16:20:29.009Z, DeletedAt: null, ID:  
6930, UpdatedAt: 2025-05-29T16:20:29.593Z, characters: 31,      
cost_org: 0.3, created_by_id: , external_user_id: 0, message:   
Your verification code is 23066, message_hash:
5f87b1b2f9d5fcbe62a81b64f0a59c37b8614463514c657bff21369bc854731e
, message_id: 9f074481-d511-4323-8be0-9961fdf3de0d, num_sms: 1, 
organisation: , organisation_name: , price_per_unit: 0.3,       
sender_email: <EMAIL>, sender_id: MOBILESASA,   
sender_phone: 254708046200, status: , third_party_charge: 0.3,  
total_charges: 0.6, total_recipients: 1}, {CreatedAt:
2025-05-29T11:28:09.151Z, DeletedAt: null, ID: 6929, UpdatedAt: 
2025-05-29T11:28:09.724Z, characters: 75, cost_org: 0.3,        
created_by_id: , external_user_id: 1074, message: Dear customer,
payment received. Thank you for choosing Elsafrica Networks!,   
message_hash:
70da44b7b86c801d9253e34e6504406f37dfc94228277ebb6379d0d88e8830cc
, message_id: 9f06dbf6-1dd3-46e6-baff-decf548d508c, num_sms: 1, 
organisation: , organisation_name: , price_per_unit: 0.3,       
sender_email: , sender_id: ONEKITTY, sender_phone: ************,
status: , third_party_charge: 0.3, total_charges: 0.6,
total_recipients: 1, transaction_id: 6353}], last: false,       
max_page: 693, page: 0, size: 10, total: 6938, total_pages: 694,
visible: 10}, message: success, status: true}
