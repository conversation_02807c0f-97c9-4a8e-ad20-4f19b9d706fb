import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';
import 'package:intl/intl.dart';
import 'package:pluto_grid/pluto_grid.dart';

import '../../../../core/app/constants/routes.dart';
import '../../../../core/app/utils/show_toast.dart';
import '../../../../core/app/widgets/loading_animations.dart';
import '../../controllers/event_controller.dart';
import '../widgets/event_filter_widget.dart';
import '../../../../data/models/event_model.dart';

class EventsScreen extends StatefulWidget {
  const EventsScreen({super.key});

  @override
  State<EventsScreen> createState() => _EventsScreenState();
}

class _EventsScreenState extends State<EventsScreen> {
  final EventController _eventController = Get.find<EventController>();
  final logger = Get.find<Logger>();

  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  late PlutoGridStateManager stateManager;

  @override
  void initState() {
    super.initState();
    setColumns();
  }

  // Setup rows for the PlutoGrid
  List<PlutoRow> setupRows(List<EventModel> events) {
    logger.d('Setting up ${events.length} rows for PlutoGrid');
    if (events.isNotEmpty) {
      logger.d('First event: ${events.first.toJson()}');
    }

    return events.map((event) {
      return PlutoRow(
        cells: {
          'id': PlutoCell(value: event.id ?? ''),
          'title': PlutoCell(value: event.title ?? 'No Title'),
          'description': PlutoCell(value: event.description ?? ''),
          'start_date': PlutoCell(
            value:
                event.startDate != null ? formatDate(event.startDate) : 'N/A',
          ),
          'end_date': PlutoCell(
            value: event.endDate != null ? formatDate(event.endDate) : 'N/A',
          ),
          'location': PlutoCell(value: event.location ?? ''),
          'status': PlutoCell(value: event.status ?? 'unknown'),
          'frequency': PlutoCell(value: event.frequency ?? ''),
          'actions': PlutoCell(value: event.id),
        },
      );
    }).toList();
  }

  // Format date string for display
  String formatDate(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) {
      return 'N/A';
    }
    try {
      // Handle different date formats
      DateTime date;
      if (dateStr.contains('T')) {
        // ISO format with time
        date = DateTime.parse(dateStr);
      } else {
        // Date only format
        date = DateTime.parse(dateStr);
      }
      return DateFormat('dd MMM yyyy').format(date);
    } catch (e) {
      logger.w('Failed to parse date: $dateStr, error: $e');
      return dateStr; // Return original string if parsing fails
    }
  }

  // Set up columns for the PlutoGrid
  void setColumns() {
    columns = [
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.text(),
        width: 80,
        enableRowChecked: true,
        hide: true, // Hidden but available for filtering
      ),
      PlutoColumn(
        title: 'Title',
        field: 'title',
        type: PlutoColumnType.text(),
        width: 200,
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        width: 200,
        renderer: (rendererContext) {
          final description = rendererContext.cell.value as String? ?? '';
          return Tooltip(
            message: description,
            child: Text(
              description.length > 100
                  ? '${description.substring(0, 100)}...'
                  : description,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Start Date',
        field: 'start_date',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'End Date',
        field: 'end_date',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Location',
        field: 'location',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final status = rendererContext.cell.value as String? ?? 'unknown';
          Color chipColor;
          switch (status.toLowerCase()) {
            case 'active':
              chipColor = Colors.green;
              break;
            case 'inactive':
              chipColor = Colors.grey;
              break;
            case 'draft':
              chipColor = Colors.orange;
              break;
            default:
              chipColor = Colors.blue;
          }
          return Container(
            decoration: BoxDecoration(
              color: chipColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Text(
              status,
              style: TextStyle(color: chipColor, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Frequency',
        field: 'frequency',
        type: PlutoColumnType.text(),
        width: 100,
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 150,
        renderer: (rendererContext) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(
                  Icons.visibility,
                  color: Colors.green,
                  size: 20,
                ),
                onPressed: () {
                  final eventId = rendererContext.cell.value as String? ?? '';
                  if (eventId.isNotEmpty) {
                    // Find the event object from the controller's events list
                    final event = _eventController.events.firstWhereOrNull(
                      (e) => e.id == eventId,
                    );

                    // Navigate to view screen with event object
                    context.go('${Routes.EVENTS}/$eventId', extra: event);
                  }
                },
                tooltip: 'View',
                constraints: const BoxConstraints(maxWidth: 30),
                padding: EdgeInsets.zero,
              ),
              IconButton(
                icon: const Icon(Icons.edit, color: Colors.blue, size: 20),
                onPressed: () {
                  final eventId = rendererContext.cell.value as String? ?? '';
                  if (eventId.isNotEmpty) {
                    // Find the event object from the controller's events list
                    final event = _eventController.events.firstWhereOrNull(
                      (e) => e.id == eventId,
                    );

                    // Navigate to edit screen with event object
                    context.go('${Routes.EVENTS}/$eventId/edit', extra: event);
                  }
                },
                tooltip: 'Edit',
                constraints: const BoxConstraints(maxWidth: 30),
                padding: EdgeInsets.zero,
              ),
              IconButton(
                icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                onPressed: () {
                  final eventId = rendererContext.cell.value as String? ?? '';
                  if (eventId.isNotEmpty) {
                    _showDeleteConfirmation(eventId);
                  }
                },
                tooltip: 'Delete',
                constraints: const BoxConstraints(maxWidth: 30),
                padding: EdgeInsets.zero,
              ),
            ],
          );
        },
      ),
    ];
  }

  // Show delete confirmation dialog
  Future<void> _showDeleteConfirmation(String eventId) async {
    final confirmDelete = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirm Delete'),
            content: const Text('Are you sure you want to delete this event?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (confirmDelete == true) {
      final success = await _eventController.deleteEvent(eventId);
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Event deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to delete event: ${_eventController.errorMessage.value}',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final showFilter = false.obs;
    return Scaffold(
      appBar: AppBar(
        title: const Text('Events'),
        actions: [
          if (MediaQuery.of(context).size.width > 600) ...[
            SizedBox(
              width: 180.w,
              child: EventFilterWidget().buildSearchField(
                Get.find<EventController>(),
                context,
              ),
            ),
            const SizedBox(width: 12),
          ],
          Obx(
            () => IconButton(
              onPressed: () {
                showFilter.value = !showFilter.value;
              },
              icon:
                  showFilter.value
                      ? Icon(Icons.filter_alt)
                      : Icon(Icons.filter_alt_outlined),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go('${Routes.EVENTS}/create'),
        tooltip: 'Create new event',
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          // Filters section
          Obx(() => showFilter.value ? const EventFilterWidget() : SizedBox()),
          Gap(8.h),

          // Stats and info
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Obx(
                  () => Text(
                    "Total Events: ${_eventController.totalItems}",
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                const Spacer(),
                Obx(
                  () =>
                      _eventController.isLoading.value
                          ? const LoadingAnimations()
                          : const SizedBox.shrink(),
                ),
              ],
            ),
          ),
          Gap(8.h),

          // Error message if any
          Obx(
            () =>
                _eventController.errorMessage.value.isNotEmpty
                    ? Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        'Error: ${_eventController.errorMessage.value}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    )
                    : const SizedBox.shrink(),
          ),

          // Events grid
          Expanded(
            child: Card(
              color: Theme.of(context).secondaryHeaderColor,
              margin: const EdgeInsets.all(12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              elevation: 4,
              child: PlutoGrid(
                mode: PlutoGridMode.selectWithOneTap,
                columns: columns,
                rows: rows,
                onLoaded: (PlutoGridOnLoadedEvent event) {
                  stateManager = event.stateManager;
                  stateManager.setShowColumnFilter(true);
                  if (kDebugMode) {
                    debugPrint("Grid loaded");
                  }
                },
                onSelected: (event) {
                  final eventId =
                      event.row?.cells['id']?.value as String? ?? '';
                  if (eventId.isNotEmpty) {
                    // Find the event object from the controller's events list
                    final eventObj = _eventController.events.firstWhereOrNull(
                      (e) => e.id == eventId,
                    );

                    // Navigate to view screen with event object
                    context.go('${Routes.EVENTS}/$eventId', extra: eventObj);
                  }
                  if (kDebugMode) {
                    debugPrint("Selected event: $eventId");
                  }
                },
                configuration: PlutoGridConfiguration(
                  style: PlutoGridStyleConfig(
                    activatedColor: const Color.fromARGB(255, 165, 205, 253),
                    cellTextStyle: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Color.fromARGB(255, 64, 64, 64),
                    ),
                    columnTextStyle: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blueGrey,
                    ),
                  ),
                  columnSize: const PlutoGridColumnSizeConfig(
                    autoSizeMode: PlutoAutoSizeMode.scale,
                  ),
                ),
                createFooter: (stateManager) {
                  return PlutoLazyPagination(
                    initialPage: 0,
                    initialFetch: true,
                    fetchWithSorting: true,
                    fetchWithFiltering: true,
                    pageSizeToMove: null,
                    fetch: (pagReq) async {
                      _eventController.currentPage.value = pagReq.page;
                      debugPrint("Fetching page: ${pagReq.page}");

                      await _eventController.fetchEvents();
                      if (_eventController.errorMessage.value.isNotEmpty) {
                        ToastUtils.showErrorToast(
                          _eventController.errorMessage.value,
                          null,
                        );
                      }

                      return Future.value(
                        PlutoLazyPaginationResponse(
                          totalPage: _eventController.totalPages.value,
                          rows: setupRows(_eventController.events),
                        ),
                      );
                    },
                    stateManager: stateManager,
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
