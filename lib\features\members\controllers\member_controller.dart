import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:intl/intl.dart';
import '../../../core/app/utils/show_toast.dart';
import '../../../data/models/member_model.dart';
import '../services/member_service.dart';
import '../models/member_payload_model.dart';
import '../../../features/auth/controllers/auth_controller.dart';

class MemberController extends GetxController {
  final MemberService _memberService = MemberService();
  final logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<MemberModel> members = <MemberModel>[].obs;
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalItems = 0.obs;
  final RxInt pageSize = 10.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;
  final RxString searchQuery = ''.obs;
  final TextEditingController searchController = TextEditingController();
  RxList<MemberModel> selectedMembers = <MemberModel>[].obs;

  // Filter variables
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);
  final RxString idFilter = ''.obs;
  final TextEditingController idController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    fetchMembers();

    // Check if there are URL parameters for filters
    final uri = Uri.base;
    if (uri.hasQuery) {
      final params = uri.queryParameters;
      if (params.containsKey('search')) {
        searchQuery.value = params['search']!;
        searchController.text = params['search']!;
      }
      if (params.containsKey('id')) {
        idFilter.value = params['id']!;
        idController.text = params['id']!;
      }
      if (params.containsKey('start_date')) {
        try {
          startDate.value = DateTime.parse(params['start_date']!);
        } catch (e) {
          logger.e('Invalid start_date parameter: ${params['start_date']}');
        }
      }
      if (params.containsKey('end_date')) {
        try {
          endDate.value = DateTime.parse(params['end_date']!);
        } catch (e) {
          logger.e('Invalid end_date parameter: ${params['end_date']}');
        }
      }
      fetchMembers(); // Refetch with filters
    }
  }

  @override
  void onClose() {
    searchController.dispose();
    idController.dispose();
    super.onClose();
  }

  // Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    currentPage.value = 0;
    fetchMembers();
  }

  // Set date filters
  void setDateFilters(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0;
    fetchMembers();
  }

  // Set ID filter
  void setIdFilter(String id) {
    idFilter.value = id;
    currentPage.value = 0;
    fetchMembers();
  }

  // Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    searchController.clear();
    startDate.value = null;
    endDate.value = null;
    idFilter.value = '';
    idController.clear();
    currentPage.value = 0;
    fetchMembers();
  }

  // Navigate to next page
  void nextPage() {
    if (!isLastPage.value) {
      currentPage.value++;
      fetchMembers();
    }
  }

  // Navigate to previous page
  void previousPage() {
    if (currentPage.value > 0) {
      currentPage.value--;
      fetchMembers();
    }
  }

  // Fetch members with pagination and filters
  Future<void> fetchMembers() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final result = await _memberService.fetchMembers(
        page: currentPage.value,
        size: pageSize.value,
        searchQuery: searchQuery.value,
        startDate: startDate.value,
        endDate: endDate.value,
        id: idFilter.value,
      );

      if (result['status'] == true) {
        final data = result['data'];
        final items = data['items'] as List<dynamic>;

        members.value = _memberService.parseMembers(items);

        totalPages.value = data['total_pages'] as int;
        totalItems.value = data['total'] as int;
        isLastPage.value = data['last'] as bool;
        isFirstPage.value = data['first'] as bool;

        // Update URL with current filters
        _updateUrlWithFilters();
      } else {
        errorMessage.value = result['message'] ?? 'Failed to fetch members';
        ToastUtils.showErrorToast(errorMessage.value, null);
      }
    } catch (e) {
      errorMessage.value = 'Error: ${e.toString()}';
      ToastUtils.showErrorToast(errorMessage.value, null);
      logger.e('Error fetching members: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Refresh members list
  void refreshMembers() {
    fetchMembers();
  }

  // Update URL with current filters
  void _updateUrlWithFilters() {
    final queryParams = <String, String>{};

    if (searchQuery.isNotEmpty) {
      queryParams['search'] = searchQuery.value;
    }

    if (idFilter.isNotEmpty) {
      queryParams['id'] = idFilter.value;
    }

    if (startDate.value != null) {
      queryParams['start_date'] = startDate.value!.toIso8601String();
    }

    if (endDate.value != null) {
      queryParams['end_date'] = endDate.value!.toIso8601String();
    }
  }

  // Register multiple members using MemberPayloadModel
  Future<Map<String, dynamic>> registerMembers(
    List<MemberPayloadModel> members,
  ) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final result = await _memberService.registerMembers(members);

      if (result['status'] == true) {
        // Refresh the members list after adding new members
        await fetchMembers();
      } else {
        errorMessage.value = result['message'] ?? 'Failed to register members';
        // Don't show toast here, let the calling screen handle it
      }

      return result;
    } catch (e) {
      errorMessage.value = 'Error: ${e.toString()}';
      ToastUtils.showErrorToast(errorMessage.value, null);
      logger.e('Error registering members: $e');
      return {'status': false, 'message': e.toString(), 'data': null};
    } finally {
      isLoading.value = false;
    }
  }

  // Get a single member by ID
  Future<MemberModel?> getMemberById(String id) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final result = await _memberService.getMemberById(id);

      if (result['status'] == true) {
        final data = result['data'];
        return MemberModel.fromJson(data);
      } else {
        errorMessage.value =
            result['message'] ?? 'Failed to get member details';
        ToastUtils.showErrorToast(errorMessage.value, null);
        return null;
      }
    } catch (e) {
      errorMessage.value = 'Error: ${e.toString()}';
      ToastUtils.showErrorToast(errorMessage.value, null);
      logger.e('Error getting member details: $e');
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // Format date for display
  String formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    return DateFormat('dd MMM yyyy').format(date);
  }

  // Convert MemberModel to MemberPayloadModel
  MemberPayloadModel memberToPayload(MemberModel member) {
    final authController = Get.find<AuthController>();
    return MemberPayloadModel.fromMemberModel(
      member,
      authController.currentOrg.value?.id ?? '',
    );
  }

  // Convert multiple MemberModels to MemberPayloadModels
  List<MemberPayloadModel> membersToPayloads(List<MemberModel> members) {
    return members.map((member) => memberToPayload(member)).toList();
  }

  // Register members from MemberModel list
  Future<Map<String, dynamic>> registerMemberModels(
    List<MemberModel> members,
  ) async {
    final payloads = membersToPayloads(members);
    return await registerMembers(payloads);
  }

  // Update a member using MemberPayloadModel
  Future<bool> updateMemberWithPayload(
    String id,
    MemberPayloadModel memberPayload,
  ) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final result = await _memberService.updateMember(
        id,
        memberPayload.toJson(),
      );

      if (result['status'] == true) {
        // Refresh the members list after updating
        await fetchMembers();
        return true;
      } else {
        errorMessage.value = result['message'] ?? 'Failed to update member';
        ToastUtils.showErrorToast(errorMessage.value, null);
        return false;
      }
    } catch (e) {
      errorMessage.value = 'Error: ${e.toString()}';
      ToastUtils.showErrorToast(errorMessage.value, null);
      logger.e('Error updating member: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Update a member using Map (for backward compatibility)
  Future<bool> updateMember(String id, Map<String, dynamic> memberData) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final result = await _memberService.updateMember(id, memberData);

      if (result['status'] == true) {
        // Refresh the members list after updating
        await fetchMembers();
        return true;
      } else {
        errorMessage.value = result['message'] ?? 'Failed to update member';
        ToastUtils.showErrorToast(errorMessage.value, null);
        return false;
      }
    } catch (e) {
      errorMessage.value = 'Error: ${e.toString()}';
      ToastUtils.showErrorToast(errorMessage.value, null);
      logger.e('Error updating member: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Update member from MemberModel
  Future<bool> updateMemberModel(String id, MemberModel member) async {
    final payload = memberToPayload(member);
    return await updateMemberWithPayload(id, payload);
  }
}
