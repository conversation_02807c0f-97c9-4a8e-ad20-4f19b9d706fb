import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

class MembershipSelectionDialog extends StatelessWidget {
  final List<dynamic> memberships;
  final Function(dynamic) onMembershipSelected;

  const MembershipSelectionDialog({
    super.key,
    required this.memberships,
    required this.onMembershipSelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Select Organization', style: theme.textTheme.titleLarge),
            Text(
              'Choose which organization you want to use',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            SizedBox(height: 24.h),
            ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.5,
              ),
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: memberships.length,
                separatorBuilder: (context, index) => Divider(height: 1.h),
                itemBuilder: (context, index) {
                  // Get the membership data
                  final membershipData = memberships[index];

                  // Debug: Print the membership data structure
                  debugPrint('Membership data: $membershipData');

                  // Try to extract organization data
                  String? orgName;
                  String? orgDescription;
                  String? orgLogo;

                  try {
                    // First check if the organization field exists
                    if (membershipData['organisation'] != null) {
                      var orgData = membershipData['organisation'];
                      debugPrint('Organization data: $orgData');

                      // Try different field names that might contain the organization name
                      orgName =
                          orgData['name'] ??
                          orgData['Name'] ??
                          orgData['NAME'] ??
                          membershipData['organization_name'] ??
                          membershipData['name'];

                      orgDescription =
                          orgData['description'] ??
                          orgData['Description'] ??
                          orgData['desc'] ??
                          membershipData['organization_description'] ??
                          '';

                      orgLogo =
                          orgData['logo'] ??
                          orgData['Logo'] ??
                          orgData['icon'] ??
                          membershipData['organization_logo'] ??
                          '';
                    } else if (membershipData['organization'] != null) {
                      // Try alternate spelling
                      var orgData = membershipData['organization'];
                      debugPrint('Organization data (alt spelling): $orgData');

                      orgName =
                          orgData['name'] ?? orgData['Name'] ?? orgData['NAME'];

                      orgDescription =
                          orgData['description'] ??
                          orgData['Description'] ??
                          orgData['desc'] ??
                          '';

                      orgLogo =
                          orgData['logo'] ??
                          orgData['Logo'] ??
                          orgData['icon'] ??
                          '';
                    } else {
                      // Try to find fields directly in the membership data
                      orgName =
                          membershipData['organization_name'] ??
                          membershipData['org_name'] ??
                          membershipData['church_name'];

                      orgDescription =
                          membershipData['organization_description'] ??
                          membershipData['org_description'] ??
                          '';
                    }
                  } catch (e) {
                    debugPrint('Error parsing organization data: $e');
                    // Fallback if there's an error parsing the organization data
                    orgName = 'Organization ${index + 1}';
                    orgDescription = '';
                  }

                  // If still no name found, check if there's a name field we can use
                  if (orgName == null || orgName.isEmpty) {
                    orgName =
                        membershipData['organisation_name'] ??
                        membershipData['organization_name'] ??
                        membershipData['church_name'] ??
                        'Organization ${index + 1}';
                  }

                  return ListTile(
                    contentPadding: EdgeInsets.symmetric(
                      vertical: 8.h,
                      horizontal: 16.w,
                    ),
                    leading: CircleAvatar(
                      backgroundColor: colorScheme.primary,
                      backgroundImage:
                          orgLogo != null && orgLogo.isNotEmpty
                              ? NetworkImage(orgLogo)
                              : null,
                      child:
                          orgLogo == null || orgLogo.isEmpty
                              ? Text(
                                orgName != null && orgName.isNotEmpty
                                    ? orgName.substring(0, 1).toUpperCase()
                                    : 'O',
                                style: TextStyle(color: colorScheme.onPrimary),
                              )
                              : null,
                    ),
                    title: Text(
                      orgName ?? 'Unknown Organization',
                      style: theme.textTheme.titleMedium,
                    ),
                    subtitle: Text(
                      orgDescription ?? '',
                      style: theme.textTheme.bodySmall,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    onTap: () {
                      debugPrint('Membership selected: $membershipData');
                      onMembershipSelected(membershipData);
                      // Close dialog immediately to improve user experience
                      context.go('/home');
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
