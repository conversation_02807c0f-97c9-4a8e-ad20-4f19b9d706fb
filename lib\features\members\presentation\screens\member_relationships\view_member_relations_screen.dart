import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:onechurch/features/members/presentation/widgets/info_cards.dart';
import '../../../../../core/app/utils/screen_breakpoints.dart';
import '../../../controllers/member_controller.dart';
import '../../../controllers/relationship_controller.dart';
import '../../../bindings/relationship_binding.dart';

// Import the split widget files
import 'relationship_types_dialog.dart';
import 'relationships_table.dart';
import 'relationship_tree_stateful.dart';
import 'show_add_relationship.dart';

class ViewMemberRelationsScreen extends StatefulWidget {
  final String memberId;
  const ViewMemberRelationsScreen({super.key, required this.memberId});

  @override
  State<ViewMemberRelationsScreen> createState() =>
      _ViewMemberRelationsScreenState();
}

class _ViewMemberRelationsScreenState extends State<ViewMemberRelationsScreen> {
  late final RelationshipController relationshipController;
  final memberController = Get.find<MemberController>();

  @override
  void initState() {
    super.initState();
    RelationshipBinding().dependencies();
    relationshipController = Get.find<RelationshipController>();
    relationshipController.setMemberId(widget.memberId);
    relationshipController.fetchRelationshipTypes();
  }

  void _showManageRelationshipTypes() {
    final mediaQuery = MediaQuery.of(context);
    final isLargeScreen = mediaQuery.size.width >= ScreenBreakpoints.tablet;

    if (isLargeScreen) {
      showDialog(
        context: context,
        builder:
            (context) =>
                buildRelationshipTypesDialog(context, relationshipController),
      );
    } else {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder:
            (context) =>
                buildRelationshipTypesSheet(context, relationshipController),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final mediaQuery = MediaQuery.of(context);
    final width = mediaQuery.size.width;

    final isDesktopLarge = width >= ScreenBreakpoints.desktopLarge;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Member Relationships'),
        elevation: isDesktopLarge ? 0 : null,
        scrolledUnderElevation: isDesktopLarge ? 0 : 2,
        actions: [
          IconButton(
            icon: const Icon(IconlyLight.category),
            onPressed: _showManageRelationshipTypes,
            tooltip: 'Manage Relationship Types',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              relationshipController.fetchRelationships();
              relationshipController.fetchRelationshipTypes();
            },
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Obx(() {
        final selectedMember = relationshipController.selectedMember.value;
        final currentViewMode = relationshipController.viewMode.value;

        return Container(
          color: isDesktopLarge ? theme.scaffoldBackgroundColor : null,
          child: Padding(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (selectedMember != null)
                  MemberInfoCard(member: selectedMember),
                Gap(16.h),
                Expanded(
                  child:
                      currentViewMode == RelationshipViewMode.table
                          ? buildRelationshipsTable(
                            context,
                            relationshipController,
                            widget.memberId,
                          )
                          : buildRelationshipTree(
                            context,
                            relationshipController,
                            widget.memberId,
                          ),
                ),
              ],
            ),
          ),
        );
      }),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      floatingActionButton: Padding(
        padding: EdgeInsets.only(bottom: 16.h, right: 16.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Toggle view mode button
            FloatingActionButton(
              heroTag: 'toggle_view',
              mini: true,
              onPressed: relationshipController.toggleViewMode,
              tooltip:
                  relationshipController.viewMode.value ==
                          RelationshipViewMode.table
                      ? 'Switch to Tree View'
                      : 'Switch to Table View',
              backgroundColor: colorScheme.primary,
              foregroundColor: colorScheme.onPrimary,

              elevation: 4,
              child: Obx(
                () => Icon(
                  relationshipController.viewMode.value ==
                          RelationshipViewMode.table
                      ? IconlyBold.chart
                      : IconlyBold.category,
                ),
              ),
            ),
            SizedBox(height: 16.h),
            // Add relationship button
            FloatingActionButton(
              heroTag: 'add_relationship',
              onPressed:
                  () => showAddRelationship(
                    context,
                    relationshipController,
                    widget.memberId,
                  ),
              tooltip: 'Add Relationship',
              backgroundColor: colorScheme.primary,
              foregroundColor: colorScheme.onPrimary,
              elevation: 4,
              child: const Icon(IconlyBold.addUser),
            ),
          ],
        ),
      ),
    );
  }
}
