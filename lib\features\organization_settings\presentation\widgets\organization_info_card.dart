import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import '../../../../core/app/utils/currency_format.dart';
import '../../controllers/organization_settings_controller.dart';

class OrganizationInfoCard extends StatelessWidget {
  const OrganizationInfoCard({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrganizationSettingsController>();
    final theme = Theme.of(context);

    return Obx(() {
      final organization = controller.organization.value;
      if (organization == null) return const SizedBox.shrink();

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Organization Logo
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    image:
                        organization.logo != null &&
                                organization.logo!.isNotEmpty
                            ? DecorationImage(
                              image: NetworkImage(organization.logo!),
                              fit: BoxFit.cover,
                            )
                            : null,
                  ),
                  child:
                      organization.logo == null || organization.logo!.isEmpty
                          ? Icon(
                            IconlyLight.home,
                            color: theme.primaryColor,
                            size: 30,
                          )
                          : null,
                ),

                const SizedBox(width: 12),

                // Organization Name and Status
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        organization.name ?? 'Organization Name',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 4),

                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getStatusColor(
                                organization.status,
                                theme,
                              ).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              organization.status ?? 'Unknown',
                              style: theme.textTheme.labelSmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: _getStatusColor(
                                  organization.status,
                                  theme,
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(width: 8),

                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getVerificationStatusColor(
                                organization.verificationStatus,
                                theme,
                              ).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              organization.verificationStatus ?? 'Unknown',
                              style: theme.textTheme.labelSmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: _getVerificationStatusColor(
                                  organization.verificationStatus,
                                  theme,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Organization Details
            if (organization.description != null &&
                organization.description!.isNotEmpty)
              Text(
                organization.description!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

            if (organization.slogan != null &&
                organization.slogan!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                '"${organization.slogan}"',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.primaryColor,
                  fontStyle: FontStyle.italic,
                  height: 1.4,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],

            const SizedBox(height: 16),

            // Quick Stats
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context: context,
                    icon: IconlyLight.message,
                    label: 'SMS Balance',
                    value: FormattedCurrency.getFormattedCurrency(
                     controller.smsBalance
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                Expanded(
                  child: _buildStatItem(
                    context: context,
                    icon: IconlyLight.wallet,
                    label: 'SMS Rate',
                    value: FormattedCurrency.getFormattedCurrency(
                      controller.smsRate,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildStatItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String value,
  }) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: theme.primaryColor, size: 20),

          const SizedBox(height: 4),

          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          Text(
            label,
            style: theme.textTheme.labelSmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String? status, ThemeData theme) {
    switch (status?.toUpperCase()) {
      case 'ACTIVE':
        return Colors.green;
      case 'INACTIVE':
        return Colors.red;
      case 'SUSPENDED':
        return Colors.orange;
      default:
        return theme.colorScheme.onSurface.withOpacity(0.6);
    }
  }

  Color _getVerificationStatusColor(String? status, ThemeData theme) {
    switch (status?.toUpperCase()) {
      case 'VERIFIED':
        return Colors.green;
      case 'PENDING':
        return Colors.orange;
      case 'REJECTED':
        return Colors.red;
      default:
        return theme.colorScheme.onSurface.withOpacity(0.6);
    }
  }
}
