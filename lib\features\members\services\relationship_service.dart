import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../models/relationship_models.dart';
import '../../auth/controllers/auth_controller.dart';

class RelationshipService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();
  final authController = Get.find<AuthController>();

  // Initialize the service
  RelationshipService() {
    _httpService.initializeDio();
  }

  // Fetch relationship types with pagination
  Future<RelationshipTypeResponse> fetchRelationshipTypes({
    int page = 0,
    int size = 10,
    String? search,
    String? category,
  }) async {
    try {
      final Map<String, dynamic> params = {
        'page': page,
        'size': size,
        'organisation_id': authController.organizationId.value,
      };

      // Add search parameter if provided
      if (search != null && search.isNotEmpty) {
        params['search'] = search;
      }

      // Add category parameter if provided
      if (category != null && category.isNotEmpty) {
        params['category'] = category;
      }

      final response = await _httpService.request(
        url: ApiUrls.getMemberRelationshipType,
        method: Method.GET,
        params: params,
      );
      return RelationshipTypeResponse.fromJson(response.data);
    } catch (e) {
      logger.e('Error fetching relationship types: $e');
      return RelationshipTypeResponse(
        status: false,
        message: 'Failed to fetch relationship types: $e',
        data: null,
      );
    }
  }

  // Create a new relationship type
  Future<Map<String, dynamic>> createRelationshipType({
    required String title,
    required String description,
    required String category,
    required bool isGeneral,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'title': title,
        'description': description,
        'category': category,
        'organisation_id': authController.organizationId.value,
        'is_general': isGeneral,
      };

      final response = await _httpService.request(
        url: ApiUrls.createMemberRelationshipType,
        method: Method.POST,
        params: data,
      );

      return response.data;
    } catch (e) {
      logger.e('Error creating relationship type: $e');
      rethrow;
    }
  }

  // Update a relationship type
  Future<Map<String, dynamic>> updateRelationshipType(
    String id,
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.updateMemberRelationshipType}$id/",
        method: Method.PUT,
        params: data,
      );

      return response.data;
    } catch (e) {
      logger.e('Error updating relationship type: $e');
      rethrow;
    }
  }

  // Delete a relationship type
  Future<Map<String, dynamic>> deleteRelationshipType(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.deleteMemberRelationshipType}$id/",
        method: Method.DELETE,
      );

      return response.data;
    } catch (e) {
      logger.e('Error deleting relationship type: $e');
      rethrow;
    }
  }

  // Fetch member relationships with pagination
  Future<MemberRelationshipResponse> fetchMemberRelationships({
    int page = 0,
    int size = 10,
    String? fromMemberId,
    String? toMemberId,
    String? relationshipTypeId,
  }) async {
    try {
      // Build URL with query parameters
      String url = "${ApiUrls.getMemberRelationship}?";
      url += "page=$page";
      url += "&size=$size";

      // Add filter parameters if provided
      if (fromMemberId != null && fromMemberId.isNotEmpty) {
        url += "&from_member=${Uri.encodeComponent(fromMemberId)}";
      }

      if (toMemberId != null && toMemberId.isNotEmpty) {
        url += "&to_member=${Uri.encodeComponent(toMemberId)}";
      }

      if (relationshipTypeId != null && relationshipTypeId.isNotEmpty) {
        url += "&relationship_type=${Uri.encodeComponent(relationshipTypeId)}";
      }

      final response = await _httpService.request(url: url, method: Method.GET);
      return MemberRelationshipResponse.fromJson(response.data);
    } catch (e) {
      logger.e('Error fetching member relationships: $e');
      return MemberRelationshipResponse(
        status: false,
        message: 'Failed to fetch member relationships: $e',
        data: null,
      );
    }
  }

  // Create a new member relationship
  Future<Map<String, dynamic>> createMemberRelationship({
    required String fromMemberId,
    required String toMemberId,
    required String relationshipTypeId,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'from_member_id': fromMemberId,
        'to_member_id': toMemberId,
        'relationship_type_id': relationshipTypeId,
      };

      final response = await _httpService.request(
        url: ApiUrls.createMemberRelationship,
        method: Method.POST,
        params: data,
      );

      return response.data;
    } catch (e) {
      logger.e('Error creating member relationship: $e');
      rethrow;
    }
  }

  // Create multiple member relationships at once
  Future<Map<String, dynamic>> createMemberRelationships({
    required List<Map<String, dynamic>> relationships,
  }) async {
    try {
      final response = await _httpService.request(
        url: ApiUrls.createMemberRelationship,
        method: Method.POST,
        params: relationships,
      );

      return response.data;
    } catch (e) {
      logger.e('Error creating member relationships: $e');
      rethrow;
    }
  }

  // Update a member relationship
  Future<Map<String, dynamic>> updateMemberRelationship(
    String id,
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.updateMemberRelationship}$id/",
        method: Method.PUT,
        params: data,
      );

      return response.data;
    } catch (e) {
      logger.e('Error updating member relationship: $e');
      rethrow;
    }
  }

  // Delete a member relationship
  Future<Map<String, dynamic>> deleteMemberRelationship(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.deleteMemberRelationship}$id/",
        method: Method.DELETE,
      );

      return response.data;
    } catch (e) {
      logger.e('Error deleting member relationship: $e');
      rethrow;
    }
  }
}
