import 'package:flutter/material.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';

class CustomPhoneInput extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final String? Function(String?)? validator;
  final Function(PhoneNumber)? onPhoneNumberChanged;
  final String? errorText;
  final PhoneNumber? initialPhoneNumber;
  const CustomPhoneInput({
    super.key,
    required this.label,
    required this.controller,
    this.validator,
    this.onPhoneNumberChanged,
    this.errorText,
    this.initialPhoneNumber,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: isSmallScreen ? 6 : 8),
        Container(
          alignment: Alignment.topCenter,
          padding: EdgeInsets.symmetric(
            horizontal: isSmallScreen ? 6 : 8,
            vertical: isSmallScreen ? 8 : 12,
          ),
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  errorText != null
                      ? theme.colorScheme.error
                      : theme.colorScheme.outline.withValues(alpha: 0.5),
              width: errorText != null ? 1.5 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: theme.colorScheme.surface,
          ),
          child: InternationalPhoneNumberInput(
            onInputChanged: onPhoneNumberChanged,
            selectorConfig: const SelectorConfig(
              selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
            ),
            ignoreBlank: false,
            validator: validator,
            autoValidateMode: AutovalidateMode.disabled,
            selectorTextStyle: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface,
            ),
            initialValue: initialPhoneNumber ?? PhoneNumber(isoCode: 'KE'),
            formatInput: true,
            keyboardType: const TextInputType.numberWithOptions(
              signed: true,
              decimal: true,
            ),
            inputDecoration: InputDecoration(
              border: InputBorder.none,
              hintText: 'Enter phone number',
              contentPadding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 12 : 16,
                vertical: isSmallScreen ? 4 : 8,
              ),
              hintStyle: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
            textStyle: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface,
            ),
            searchBoxDecoration: InputDecoration(
              labelText: 'Search by country name or code',
              hintText: 'Search...',
              prefixIcon: Icon(
                Icons.search,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: theme.colorScheme.outline),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: theme.colorScheme.outline),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: theme.colorScheme.primary,
                  width: 1.5,
                ),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 12 : 16,
                vertical: isSmallScreen ? 12 : 16,
              ),
            ),
          ),
        ),
        if (errorText != null || validator != null)
          Padding(
            padding: EdgeInsets.only(
              top: isSmallScreen ? 4 : 6,
              left: isSmallScreen ? 4 : 6,
            ),
            child: Text(
              errorText ?? '',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.error,
                fontSize: isSmallScreen ? 11 : 12,
              ),
            ),
          ),
      ],
    );
  }
}
