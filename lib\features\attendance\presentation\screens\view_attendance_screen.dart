import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import '../../../../core/app/constants/routes.dart';
import '../../controllers/attendance_controller.dart';
import '../../models/attendance_model.dart';

class ViewAttendanceScreen extends StatefulWidget {
  final String attendanceId;

  const ViewAttendanceScreen({super.key, required this.attendanceId});

  @override
  State<ViewAttendanceScreen> createState() => _ViewAttendanceScreenState();
}

class _ViewAttendanceScreenState extends State<ViewAttendanceScreen> {
  final AttendanceController _attendanceController =
      Get.find<AttendanceController>();
  final logger = Get.find<Logger>();

  // In a real implementation, this would be fetched from the API
  AttendanceModel? attendance;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    // Simulate fetching attendance data
    // In a real implementation, you would fetch the specific attendance record
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          // For demo purposes, use the first item in the list or create a dummy one
          attendance =
              _attendanceController.attendanceList.isNotEmpty
                  ? _attendanceController.attendanceList.first
                  : AttendanceModel(
                    organisationId: widget.attendanceId,
                    title: 'Sample Event',
                    description: 'Sample Event Description',
                    timeIn: DateTime.now().toIso8601String(),
                    timeOut: DateTime.now().toIso8601String(),
                    locationName: 'Sample Location',
                  );
          isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Attendance Details'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(Routes.ATTENDANCE),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              context.go(
                Routes.EDIT_ATTENDANCE.replaceFirst(':id', widget.attendanceId),
              );
            },
            tooltip: 'Edit Attendance',
          ),
        ],
      ),
      body:
          isLoading
              ? const Center(child: CircleLoadingAnimation())
              : SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Card(
                        elevation: 4,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(IconlyBold.document, size: 24),
                                  Gap(8.w),
                                  Text(
                                    attendance?.title ?? 'No Title',
                                    style: const TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              const Divider(height: 24),

                              // Description
                              if (attendance?.description != null &&
                                  attendance!.description!.isNotEmpty)
                                _buildInfoRow(
                                  IconlyLight.paper,
                                  'Description',
                                  attendance!.description!,
                                ),

                              // Time In
                              if (attendance?.timeIn != null)
                                _buildInfoRow(
                                  IconlyLight.timeCircle,
                                  'Time In',
                                  _attendanceController.formatDate(
                                    attendance!.timeIn!,
                                  ),
                                ),

                              // Time Out
                              if (attendance?.timeOut != null)
                                _buildInfoRow(
                                  IconlyLight.timeCircle,
                                  'Time Out',
                                  _attendanceController.formatDate(
                                    attendance!.timeOut!,
                                  ),
                                ),

                              // Location
                              if (attendance?.locationName != null)
                                _buildInfoRow(
                                  IconlyLight.location,
                                  'Location',
                                  attendance!.locationName!,
                                ),

                              // Coordinates
                              if (attendance?.latitude != null &&
                                  attendance?.longitude != null)
                                _buildInfoRow(
                                  IconlyLight.location,
                                  'Coordinates',
                                  'Lat: ${attendance!.latitude}, Lng: ${attendance!.longitude}',
                                ),
                            ],
                          ),
                        ),
                      ),

                      Gap(24.h),

                      // Actions
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomButton(
                            style: CustomButtonStyle.outlined,
                            onPressed: () => context.go(Routes.ATTENDANCE),
                            icon: const Icon(IconlyLight.arrowLeft),
                            label: const Text('Back to List'),
                          ),
                          Gap(16.w),
                          CustomButton(
                            onPressed: () {
                              context.go(
                                Routes.EDIT_ATTENDANCE.replaceFirst(
                                  ':id',
                                  widget.attendanceId,
                                ),
                              );
                            },
                            icon: const Icon(IconlyLight.edit),
                            label: const Text('Edit Attendance'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[700]),
          Gap(12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Gap(4.h),
                Text(value, style: const TextStyle(fontSize: 16)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
