// To parse this JSON data, do
//
//     final announcementModel = announcementModelFromJson(jsonString);

import 'dart:convert';

import 'package:onechurch/data/models/user_model.dart';
import 'package:onechurch/features/media_upload/models/media_model.dart';

List<AnnouncementModel> announcementModelFromJson(String str) =>
    List<AnnouncementModel>.from(
      json.decode(str).map((x) => AnnouncementModel.fromJson(x)),
    );

String announcementModelToJson(List<AnnouncementModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AnnouncementModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? organisationId;
  final dynamic organisation;
  final String? title;
  final String? description;
  final String? username;
  final String? status;
  final String? category;
  final String? type;
  final List<MediaModel>? media;
  final List<Item>? items;
  final int? createdByUserId;
  final UserModel? createdByUser;
  final dynamic updatedByUserId;

  AnnouncementModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.organisationId,
    this.organisation,
    this.title,
    this.description,
    this.username,
    this.status,
    this.category,
    this.type,
    this.media,
    this.items,
    this.createdByUserId,
    this.createdByUser,
    this.updatedByUserId,
  });

  AnnouncementModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? organisationId,
    dynamic organisation,
    String? title,
    String? description,
    String? username,
    String? status,
    String? category,
    String? type,
    List<MediaModel>? media,
    List<Item>? items,
    int? createdByUserId,
    UserModel? createdByUser,
    dynamic updatedByUserId,
  }) => AnnouncementModel(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    organisationId: organisationId ?? this.organisationId,
    organisation: organisation ?? this.organisation,
    title: title ?? this.title,
    description: description ?? this.description,
    username: username ?? this.username,
    status: status ?? this.status,
    category: category ?? this.category,
    type: type ?? this.type,
    media: media ?? this.media,
    items: items ?? this.items,
    createdByUserId: createdByUserId ?? this.createdByUserId,
    createdByUser: createdByUser ?? this.createdByUser,
    updatedByUserId: updatedByUserId ?? this.updatedByUserId,
  );

  factory AnnouncementModel.fromJson(
    Map<String, dynamic> json,
  ) => AnnouncementModel(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    organisationId: json["organisation_id"],
    organisation: json["organisation"],
    title: json["title"],
    description: json["description"],
    username: json["username"],
    status: json['status'],
    category: json["category"],
    type: json["type"],
    media:
        json["media"] == null
            ? []
            : List<MediaModel>.from(
              json["media"]!.map((x) => MediaModel.fromJson(x)),
            ),
    items:
        json["items"] == null
            ? []
            : List<Item>.from(json["items"]!.map((x) => Item.fromJson(x))),
    createdByUserId: json["created_by_user_id"],
    createdByUser:
        json["created_by_user"] == null
            ? null
            : UserModel.fromJson(json["created_by_user"]),
    updatedByUserId: json["updated_by_user_id"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "organisation_id": organisationId,
    "organisation": organisation,
    "title": title,
    "description": description,
    "username": username,
    "status": status,
    "category": category,
    "type": type,
    "media":
        media == null ? [] : List<dynamic>.from(media!.map((x) => x.toJson())),
    "items":
        items == null ? [] : List<dynamic>.from(items!.map((x) => x.toJson())),
    "created_by_user_id": createdByUserId,
    "created_by_user": createdByUser?.toJson(),
    "updated_by_user_id": updatedByUserId,
  };
}

class Item {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? announcementId;
  final dynamic announcement;
  final dynamic organisation;
  final String? organisationId;
  final String? title;
  final String? description;
  final String? category;
  final String? locationName;
  final List<dynamic>? media;
  final dynamic createdByUserId;
  final dynamic updatedByUserId;

  Item({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.announcementId,
    this.announcement,
    this.organisation,
    this.organisationId,
    this.title,
    this.description,
    this.category,
    this.locationName,
    this.media,
    this.createdByUserId,
    this.updatedByUserId,
  });

  Item copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? announcementId,
    dynamic announcement,
    dynamic organisation,
    String? organisationId,
    String? title,
    String? description,
    String? category,
    String? locationName,
    List<dynamic>? media,
    dynamic createdByUserId,
    dynamic updatedByUserId,
  }) => Item(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    announcementId: announcementId ?? this.announcementId,
    announcement: announcement ?? this.announcement,
    organisation: organisation ?? this.organisation,
    organisationId: organisationId ?? this.organisationId,
    title: title ?? this.title,
    description: description ?? this.description,
    category: category ?? this.category,
    locationName: locationName ?? this.locationName,
    media: media ?? this.media,
    createdByUserId: createdByUserId ?? this.createdByUserId,
    updatedByUserId: updatedByUserId ?? this.updatedByUserId,
  );

  factory Item.fromJson(Map<String, dynamic> json) => Item(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    announcementId: json["announcement_id"],
    announcement: json["announcement"],
    organisation: json["organisation"],
    organisationId: json["organisation_id"],
    title: json["title"],
    description: json["description"],
    category: json["category"],
    locationName: json["location_name"],
    media:
        json["media"] == null
            ? []
            : List<dynamic>.from(json["media"]!.map((x) => x)),
    createdByUserId: json["created_by_user_id"],
    updatedByUserId: json["updated_by_user_id"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "announcement_id": announcementId,
    "announcement": announcement,
    "organisation": organisation,
    "organisation_id": organisationId,
    "title": title,
    "description": description,
    "category": category,
    "location_name": locationName,
    "media": media == null ? [] : List<dynamic>.from(media!.map((x) => x)),
    "created_by_user_id": createdByUserId,
    "updated_by_user_id": updatedByUserId,
  };
}
