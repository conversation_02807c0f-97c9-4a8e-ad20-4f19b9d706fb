import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController? controller;

  final String? labelText;

  final String? label;

  final String? hintText;

  final Widget? prefixIcon;

  final dynamic suffixIcon;

  final VoidCallback? suffixIconAction;

  final bool readOnly;

  final bool enabled;

  final bool obscureText;

  final TextInputType? keyboardType;

  final int? maxLines;

  final List<TextInputFormatter>? inputFormatters;

  final Function(String)? onChanged;

  final VoidCallback? onTap;

  final Function(String)? onSubmitted;

  final String? errorText;

  final String? Function(String?)? validator;

  final bool autofocus;

  final InputDecoration? decoration;

  final TextCapitalization textCapitalization;

  final bool isPassword;
  final Widget? prefix;

  final String? helperText;

  const CustomTextField({
    super.key,
    this.controller,
    this.labelText,
    this.label,
    this.hintText,
    this.prefixIcon,
    this.prefix,
    this.suffixIcon,
    this.suffixIconAction,
    this.readOnly = false,
    this.enabled = true,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.maxLines = 1,
    this.inputFormatters,
    this.onChanged,
    this.onTap,
    this.onSubmitted,
    this.errorText,
    this.validator,
    this.autofocus = false,
    this.decoration,
    this.textCapitalization = TextCapitalization.none,
    this.isPassword = false,
    this.helperText,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final effectiveLabel = labelText ?? label;

    final effectiveDecoration =
        decoration ??
        InputDecoration(
          labelText: effectiveLabel,
          labelStyle: textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface.withOpacity(0.7),
            fontWeight: FontWeight.w500,
          ),
          hintText: hintText,
          hintStyle: textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface.withOpacity(0.5),
            fontWeight: FontWeight.normal,
          ),
          helperText: helperText,
          helperStyle: textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface.withOpacity(0.6),
          ),
          errorText: errorText,
          errorStyle: textTheme.bodyMedium?.copyWith(color: colorScheme.error),
          prefixIcon:
              prefixIcon != null
                  ? IconTheme(
                    data: IconThemeData(
                      color: colorScheme.primary.withOpacity(0.8),
                      size: 20,
                    ),
                    child: prefixIcon!,
                  )
                  : null,
          prefix: prefix,
          suffixIcon:
              suffixIcon is IconData
                  ? IconButton(
                    icon: Icon(
                      suffixIcon as IconData,
                      color: colorScheme.primary.withOpacity(0.8),
                      size: 20,
                    ),
                    onPressed: suffixIconAction,
                  )
                  : suffixIcon,
          filled: true,
          fillColor: colorScheme.surfaceVariant.withOpacity(0.25),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 14,
            horizontal: 16,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: colorScheme.outline),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: colorScheme.outline.withOpacity(0.5)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: colorScheme.primary, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: colorScheme.error),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: colorScheme.error, width: 2),
          ),
        );

    // Otherwise use regular TextField
    return TextField(
      controller: controller,
      readOnly: readOnly,
      enabled: enabled,
      obscureText: isPassword || obscureText,
      keyboardType: keyboardType,
      maxLines: maxLines,
      inputFormatters: inputFormatters,
      onChanged: onChanged,
      onTap: onTap,
      onSubmitted: onSubmitted,
      autofocus: autofocus,
      decoration: effectiveDecoration,
      textCapitalization: textCapitalization,
      style: textTheme.bodyMedium?.copyWith(color: colorScheme.onSurface),
    );
  }
}
