import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_admin_scaffold/admin_scaffold.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/constants/routes.dart';
import 'package:onechurch/core/app/utils/size_config.dart';
import 'package:onechurch/features/admin/controllers/admin_controller.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
class AdminDrawerScreen extends StatelessWidget {
  final StatefulNavigationShell? navKey;

  const AdminDrawerScreen({super.key, this.navKey});

  List<AdminMenuItem> _buildMenuItems() {
    return [
      AdminMenuItem(
        title: 'Home',
        icon: Icons.dashboard,
        route: Routes.DASHBOARD,
      ),
      AdminMenuItem(
        title: 'Members',
        icon: Icons.manage_accounts,
        route: Routes.MEMBERS,
        children: [
          AdminMenuItem(
            title: 'View Members',
            icon: Icons.list,
            route: Routes.MEMBERS,
          ),
          AdminMenuItem(
            title: 'Add Member',
            icon: Icons.add,
            route: Routes.ADD_MEMBER,
          ),
          AdminMenuItem(
            title: 'Member Categories',
            icon: Icons.category,
            route: Routes.MEMBER_CATEGORIES,
          ),
          AdminMenuItem(
            title: 'Attendance',
            icon: Icons.how_to_reg,
            route: Routes.ATTENDANCE,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Announcements',
        icon: Icons.announcement_outlined,
        route: Routes.ANNOUNCEMENTS,
        children: [
          AdminMenuItem(
            title: 'View Announcements',
            icon: Icons.list,
            route: Routes.ANNOUNCEMENTS,
          ),
          AdminMenuItem(
            title: 'Create Announcement',
            icon: Icons.add,
            route: Routes.CREATE_ANNOUNCEMENT,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Staff',
        icon: Icons.people,
        route: Routes.STAFF,
        children: [
          AdminMenuItem(
            title: 'View staffs',
            icon: Icons.people_outline,
            route: Routes.STAFF,
          ),
          AdminMenuItem(
            title: 'Create staff',
            icon: Icons.person_add,
            route: Routes.CREATE_STAFF,
          ),
          AdminMenuItem(
            title: 'Staff Roles',
            icon: Icons.supervised_user_circle,
            route: Routes.STAFF_ROLES,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Sermons',
        icon: Icons.church,
        route: Routes.SERMONS,
        children: [
          AdminMenuItem(
            title: 'View Sermons',
            icon: Icons.list,
            route: Routes.SERMONS,
          ),
          AdminMenuItem(
            title: 'Create Sermon',
            icon: Icons.add,
            route: Routes.SERMON_CREATE,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Events',
        icon: Icons.event,
        route: Routes.EVENTS,
        children: [
          AdminMenuItem(
            title: 'View Events',
            icon: Icons.list,
            route: Routes.EVENTS,
          ),
          AdminMenuItem(
            title: 'Create Event',
            icon: Icons.add,
            route: Routes.EVENT_CREATE,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Groups',
        icon: IconlyBold.user3,
        route: Routes.GROUPS,
        children: [
          AdminMenuItem(
            title: 'View Groups',
            icon: Icons.group,
            route: Routes.GROUPS,
          ),
          AdminMenuItem(
            title: 'Create Group',
            icon: Icons.group_add,
            route: Routes.CREATE_GROUP,
          ),
        ],
      ),

      AdminMenuItem(
        title: 'Finance',
        icon: CupertinoIcons.money_dollar_circle_fill,
        route: Routes.FINANCE_ADMIN,
        children: [
          AdminMenuItem(
            title: 'Transactions',
            icon: IconlyBold.chart,
            route: Routes.TRANSACTIONS_VIEW,
          ),
          AdminMenuItem(
            title: 'View Accounts',
            icon: Icons.list,
            route: Routes.SUB_ACCOUNTS_VIEW,
          ),
          AdminMenuItem(
            title: 'Create Account',
            icon: Icons.add,
            route: Routes.CREATE_SUB_ACCOUNT,
          ),
          AdminMenuItem(
            title: 'Account Categories',
            icon: Icons.category,
            route: Routes.ACCOUNT_CATEGORIES,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Inventory',
        icon: Icons.inventory_2,
        route: Routes.INVENTORY_ITEMS,
        children: [
          AdminMenuItem(
            title: 'Items',
            icon: Icons.inventory,
            route: Routes.INVENTORY_ITEMS,
          ),
          AdminMenuItem(
            title: 'Records',
            icon: Icons.description,
            route: Routes.INVENTORY_RECORDS,
          ),
          AdminMenuItem(
            title: 'Item Categories',
            icon: Icons.category,
            route: Routes.INVENTORY_CATEGORIES,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Communications',
        icon: Icons.message,
        route: Routes.COMMUNICATIONS,
        children: [
          AdminMenuItem(title: 'Sms', icon: Icons.sms, route: Routes.SMS),
          AdminMenuItem(
            title: 'Sms Requests',
            icon: Icons.sms_outlined,
            route: Routes.SMS_REQUESTS,
          ),
        ],
      ),
      AdminMenuItem(
        title: 'Profile',
        icon: Icons.person,
        route: Routes.PROFILE,
        children: [
          AdminMenuItem(
            title: 'View Profile',
            icon: Icons.person_outline,
            route: Routes.PROFILE,
          ),
          AdminMenuItem(
            title: 'Settings',
            icon: Icons.settings,
            route: Routes.SETTINGS,
          ),
          AdminMenuItem(
            title: 'Organization Settings',
            icon: Icons.business,
            route: Routes.ORGANIZATION_SETTINGS,
          ),
        ],
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final AdminController adminController = Get.put(AdminController());
    final AuthController authController = Get.find();
    SizeConfig().init(context);

    return AdminScaffold(
      // Use the navigation key from the StatefulNavigationShell instead of a separate key
      appBar: AppBar(
        actions: [
          IconButton(
            onPressed: () {
              context.go(Routes.NOTIFICATIONS);
            },
            icon: Icon(IconlyBold.notification),
          ),
        ],
        flexibleSpace: const SizedBox.shrink(),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        centerTitle: true,
        title: Obx(() => Text(adminController.pagtitle.value)),
      ),
      sideBar: SideBar(
        backgroundColor: Theme.of(context).secondaryHeaderColor,
        items: _buildMenuItems(),
        selectedRoute: adminController.selectedRoute.value,
        // Don't use a GlobalKey for the sidebar to avoid duplicate key errors
        onSelected: (item) {
          if (item.route != null) {
            context.go('${item.route}');
            adminController.updatePage(item.route!, item.title);
          }
        },
        header: CachedNetworkImage(
          imageUrl: authController.currentOrg.value?.logo ?? '',
          progressIndicatorBuilder:
              (context, url, downloadProgress) =>
                  CircleLoadingAnimation(value: downloadProgress.progress),
          errorWidget: (context, url, error) {
            return Image.asset(
              'assets/logo/onechurch-3.png',
              alignment: Alignment.center,
              fit: BoxFit.contain,
              height: 120.h,
              width: 120.h,
            );
          },
        ),
        footer: _buildHeaderFooter('onechurch'),
      ),
      body:
          navKey == null
              ? Obx(() => adminController.selectedPage.value)
              : navKey!,
    );
  }

  Widget _buildHeaderFooter(String text) {
    return Builder(
      builder:
          (context) => Container(
            height: 50.h,
            width: double.infinity,
            color: Theme.of(context).secondaryHeaderColor,
            child: Center(child: Text(text)),
          ),
    );
  }
}
