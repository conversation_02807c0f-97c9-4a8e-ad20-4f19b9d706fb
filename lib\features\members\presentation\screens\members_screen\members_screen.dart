import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../../controllers/member_controller.dart';
import '../../widgets/member_filter_widget.dart';
import '../../../../../core/app/constants/routes.dart';
import 'widgets/index.dart';

class MemberViewScreen extends StatefulWidget {
  final bool isSelect;

  const MemberViewScreen({super.key, this.isSelect = false});

  @override
  State<MemberViewScreen> createState() => _MemberViewScreenState();
}

class _MemberViewScreenState extends State<MemberViewScreen> {
  final controller = Get.find<MemberController>();
  PlutoGridStateManager? stateManager;
  final showBulkActions = false.obs;

  @override
  Widget build(BuildContext context) {
    final showDashboard = false.obs;

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isSelect ? 'Select Members' : 'Members'),
        actions: [
          if (MediaQuery.of(context).size.width > 600) ...[
            SizedBox(width: 120.w, child: const SearchFieldWidget()),
            const SizedBox(width: 8),
          ],

          Obx(
            () => IconButton(
              onPressed: () {
                showDashboard(!showDashboard.value);
              },
              icon: Icon(
                showDashboard.value
                    ? Icons.filter_alt
                    : Icons.filter_alt_outlined,
              ),
            ),
          ),

          // Show Done button with selected count if in selection mode
          if (widget.isSelect)
            Obx(
              () => TextButton.icon(
                onPressed: () {
                  context.pop();
                },
                icon: const Icon(Icons.check),
                label: Text('Done (${controller.selectedMembers.length})'),
              ),
            ),

          // Show bulk actions button if not in selection mode and members are selected
          if (!widget.isSelect)
            Obx(
              () =>
                  controller.selectedMembers.isNotEmpty
                      ? IconButton(
                        onPressed: () {
                          showBulkActions(!showBulkActions.value);
                        },
                        icon: Icon(
                          showBulkActions.value
                              ? Icons.more_vert
                              : Icons.more_horiz,
                        ),
                        tooltip: 'Bulk Actions',
                      )
                      : const SizedBox.shrink(),
            ),
        ],
      ),
      body: Column(
        children: [
          // Filters section
          Obx(
            () =>
                showDashboard.value
                    ? const MemberFilterWidget()
                    : const SizedBox(),
          ),

          // Bulk actions menu
          if (!widget.isSelect)
            Obx(
              () =>
                  showBulkActions.value && controller.selectedMembers.isNotEmpty
                      ? BulkActionsWidget(
                        onClose: () => showBulkActions(false),
                        stateManager: stateManager,
                      )
                      : const SizedBox.shrink(),
            ),

          // Stats and info
          MemberStatsWidget(
            isSelectMode: widget.isSelect,
            onClearSelection: () {
              controller.selectedMembers.clear();
              stateManager?.notifyListeners();
              showBulkActions(false);
            },
            stateManager: stateManager,
          ),

          Gap(8.h),

          // Members list with PlutoGrid
          Expanded(
            child: MemberTableWidget(
              isSelectMode: widget.isSelect,
              onStateManagerCreated: (manager) {
                stateManager = manager;
              },
              showBulkActions: showBulkActions,
            ),
          ),
        ],
      ),
      floatingActionButton:
          widget.isSelect
              ? null
              : Obx(
                () =>
                    controller.selectedMembers.isNotEmpty
                        ? FloatingActionButton.extended(
                          onPressed: () {
                            showBulkActions(true);
                          },
                          icon: const Icon(Icons.group_work),
                          label: Text(
                            'Actions (${controller.selectedMembers.length})',
                          ),
                        )
                        : FloatingActionButton(
                          onPressed: () {
                            // Navigate to add member
                            context.go(Routes.ADD_MEMBER);
                          },
                          child: const Icon(Icons.add),
                        ),
              ),
    );
  }
}
