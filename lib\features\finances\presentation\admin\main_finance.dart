import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import 'package:onechurch/data/models/sub_account_model.dart';
import 'package:onechurch/features/finances/controllers/finance_controller.dart';
import 'package:onechurch/features/finances/presentation/widgets/finance_dashboard_widget.dart';
import 'package:onechurch/features/finances/presentation/widgets/finance_filter_widget.dart';
import 'package:pluto_grid/pluto_grid.dart';

class SubAccountsView extends StatefulWidget {
  const SubAccountsView({super.key});

  @override
  State<SubAccountsView> createState() => _SubAccountsViewState();
}

class _SubAccountsViewState extends State<SubAccountsView> {
  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  final RxBool showDashboard = false.obs;
  final RxBool showFilter = false.obs;
  String searchQuery = '';
  List<PlutoRow> setupRows(List<SubAccount> acct) {
    return acct.map((sermon) {
      return PlutoRow(
        cells: {
          'title': PlutoCell(value: sermon.title ?? ""),
          'description': PlutoCell(value: sermon.description),
          'account': PlutoCell(value: sermon.account),
          'category': PlutoCell(value: sermon.category?.title),
          'status': PlutoCell(value: sermon.status),
          'createdAt': PlutoCell(
            value:
                sermon.createdAt != null
                    ? DateFormat('dd MMM yyyy').format(sermon.createdAt!)
                    : 'N/A',
          ),
          'createdBy': PlutoCell(
            value:
                "${sermon.createdByUser?.firstName ?? ''} ${sermon.createdByUser?.secondName ?? ''}",
          ),
          'actions': PlutoCell(value: sermon.id),
        },
      );
    }).toList();
  }

  setColumns() {
    // Initialize columns
    columns = [
      PlutoColumn(
        title: 'Title',
        field: 'title',
        type: PlutoColumnType.text(),
        width: 170,
        enableRowChecked: true,
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        width: 200,
      ),
      PlutoColumn(
        title: 'Account',
        field: 'account',
        type: PlutoColumnType.text(),
        width: 180,
      ),
      PlutoColumn(
        title: 'Category',
        field: 'category',
        type: PlutoColumnType.text(),
        width: 120,
      ),

      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final status = rendererContext.row.cells['status']?.value;
          return Chip(
            backgroundColor:
                status == "ACTIVE"
                    ? Colors.green.shade100.withValues(alpha: 0.2)
                    : Colors.red.shade100.withValues(alpha: 0.2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
              side: BorderSide(
                color: status == "ACTIVE" ? Colors.green : Colors.red,
                width: 0.6,
              ),
            ),
            label: Text(
              status.toString(),
              style: TextStyle(
                color: status == "ACTIVE" ? Colors.green : Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Created',
        field: 'createdAt',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Created By',
        field: 'createdBy',
        type: PlutoColumnType.text(),
        width: 120,
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: false,
        enableColumnDrag: false,
        enableContextMenu: false,
        renderer: (rendererContext) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(IconlyLight.show, size: 18),
                onPressed: () {
                  final accountId = rendererContext.cell.value;
                  if (accountId != null) {
                    // TODO: Navigate to view account screen
                     
                  }
                },
                tooltip: 'View',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
              IconButton(
                icon: const Icon(IconlyLight.edit, size: 18),
                onPressed: () {
                  final accountId = rendererContext.cell.value;
                  if (accountId != null) {
                    // TODO: Navigate to edit account screen
                                 }
                },
                tooltip: 'Edit',
                constraints: const BoxConstraints(),
                padding: const EdgeInsets.all(4),
              ),
            ],
          );
        },
      ),
    ];
  }

  FinanceController controller = Get.find();
  @override
  void initState() {
    setColumns();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(
          () => Text(
            showDashboard.value ? 'Finance Dashboard' : 'Finance Management',
          ),
        ),
        elevation: 0,
        actions: [
          // Search Button
          IconButton(
            icon: const Icon(IconlyLight.search),
            onPressed: () {
              // TODO: Implement search functionality
            },
            tooltip: 'Search',
          ),
          // Filter Toggle Button
          Obx(
            () => IconButton(
              icon: Icon(
                showFilter.value ? IconlyBold.filter : IconlyLight.filter,
                color: showFilter.value ? Colors.blue : null,
              ),
              onPressed: () {
                showFilter.value = !showFilter.value;
              },
              tooltip: showFilter.value ? 'Hide Filters' : 'Show Filters',
            ),
          ),
          // Dashboard Toggle Button
          Obx(
            () => IconButton(
              icon: Icon(
                showDashboard.value ? IconlyBold.chart : IconlyLight.chart,
                color: showDashboard.value ? Colors.blue : null,
              ),
              onPressed: () {
                showDashboard.value = !showDashboard.value;
              },
              tooltip: 'Toggle Dashboard',
            ),
          ),
          // Refresh Button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () async {
              await controller.fetchAccounts();
              await controller.fetchCategories();
            },
            tooltip: 'Refresh Data',
          ),
        ],
      ),

      body: Column(
        children: [
          // Filter Widget - Positioned at top when not in dashboard mode
          Obx(
            () =>
                !showDashboard.value && showFilter.value
                    ? Container(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      child: Card(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 1,
                        child: const FinanceFilterWidget(),
                      ),
                    )
                    : const SizedBox.shrink(),
          ),

          // Main Content Area
          Expanded(
            child: Obx(
              () =>
                  showDashboard.value
                      ? // Dashboard Mode - Full screen dashboard
                      Container(
                        margin: const EdgeInsets.all(8),
                        child: Card(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                children: [
                                  // Dashboard Header with Filter Toggle
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.dashboard,
                                        color: Colors.blue.shade700,
                                        size: 24,
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        'Finance Dashboard',
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.grey.shade800,
                                        ),
                                      ),
                                      const Spacer(),
                                      // Filter toggle in dashboard mode
                                      if (showFilter.value)
                                        IconButton(
                                          icon: Icon(
                                            Icons.filter_list_off,
                                            color: Colors.grey.shade600,
                                          ),
                                          onPressed: () {
                                            showFilter.value = false;
                                          },
                                          tooltip: 'Hide Filters',
                                        )
                                      else
                                        IconButton(
                                          icon: Icon(
                                            Icons.filter_list,
                                            color: Colors.blue.shade600,
                                          ),
                                          onPressed: () {
                                            showFilter.value = true;
                                          },
                                          tooltip: 'Show Filters',
                                        ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),

                                  // Filter Widget in Dashboard Mode (if enabled)
                                  if (showFilter.value) ...[
                                    Container(
                                      margin: const EdgeInsets.only(bottom: 16),
                                      padding: const EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade50,
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: Colors.grey.shade200,
                                        ),
                                      ),
                                      child: const FinanceFilterWidget(),
                                    ),
                                  ],

                                  // Dashboard Content
                                  const FinanceDashboardWidget(),
                                ],
                              ),
                            ),
                          ),
                        ),
                      )
                      : // Grid Mode - Data table
                      Container(
                        margin: const EdgeInsets.all(8),
                        constraints: const BoxConstraints(minHeight: 300),
                        child: Card(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: GetX<FinanceController>(
                              init: FinanceController(),
                              key: Key("subaccounts"),
                              initState: (state) async {},
                              builder: (controll) {
                                if (kDebugMode) {
                                  debugPrint(
                                    "Finance-Accounts Fetched: ${controll.subaccounts.length}",
                                  );
                                }
                                return PlutoGrid(
                                  mode: PlutoGridMode.selectWithOneTap,
                                  columns: columns,
                                  rows: setupRows(controll.subaccounts),
                                  onLoaded: (PlutoGridOnLoadedEvent event) {
                                    event.stateManager.setShowColumnFilter(
                                      true,
                                    );
                                    if (kDebugMode) {
                                      debugPrint("onLoaded: $event");
                                    }
                                  },
                                  onSelected: (event) {
                                    if (kDebugMode) {
                                      debugPrint("onSelected: $event");
                                    }
                                  },
                                  configuration: PlutoGridConfiguration(
                                    style: PlutoGridStyleConfig(
                                      activatedColor: Color.fromARGB(
                                        255,
                                        165,
                                        205,
                                        253,
                                      ),
                                      cellTextStyle: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                        color: Color.fromARGB(
                                          255,
                                          216,
                                          108,
                                          40,
                                        ),
                                      ),
                                      columnTextStyle: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blueGrey,
                                      ),
                                      gridBorderColor: Colors.grey.shade300,
                                      borderColor: Colors.grey.shade300,
                                    ),
                                    columnSize: PlutoGridColumnSizeConfig(
                                      autoSizeMode: PlutoAutoSizeMode.scale,
                                      resizeMode: PlutoResizeMode.pushAndPull,
                                    ),
                                    scrollbar: PlutoGridScrollbarConfig(
                                      isAlwaysShown: false,
                                      scrollbarThickness: 8,
                                      scrollbarThicknessWhileDragging: 10,
                                    ),
                                  ),
                                  createFooter: (stateManager) {
                                    return PlutoLazyPagination(
                                      initialPage: 0,
                                      initialFetch: true,
                                      fetchWithSorting: true,
                                      fetchWithFiltering: true,
                                      pageSizeToMove: null,
                                      fetch: (pagReq) async {
                                        controll.currentPage.value =
                                            pagReq.page;
                                        debugPrint(
                                          "fetch page: ${pagReq.page}",
                                        );

                                        await controll.fetchAccounts();

                                        return Future.value(
                                          PlutoLazyPaginationResponse(
                                            totalPage:
                                                controll.totalPages.value,
                                            rows: setupRows(
                                              controll.subaccounts,
                                            ),
                                          ),
                                        );
                                      },
                                      stateManager: stateManager,
                                    );
                                  },
                                );
                              },
                            ),
                          ),
                        ),
                      ),
            ),
          ),
        ],
      ),
    );
  }
}
