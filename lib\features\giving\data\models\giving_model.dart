class GivingModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final double? amount;
  final String? givingType;
  final String? notes;
  final String? memberId;
  final String? organisationId;
  final String? status;
  final String? paymentMethod;
  final String? transactionReference;

  const GivingModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.amount,
    this.givingType,
    this.notes,
    this.memberId,
    this.organisationId,
    this.status,
    this.paymentMethod,
    this.transactionReference,
  });

  factory GivingModel.fromJson(Map<String, dynamic> json) {
    return GivingModel(
      id: json['id']?.toString(),
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at'].toString())
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at'].toString())
          : null,
      deletedAt: json['deleted_at'],
      amount: json['amount'] != null 
          ? double.tryParse(json['amount'].toString())
          : null,
      givingType: json['giving_type']?.toString(),
      notes: json['notes']?.toString(),
      memberId: json['member_id']?.toString(),
      organisationId: json['organisation_id']?.toString(),
      status: json['status']?.toString(),
      paymentMethod: json['payment_method']?.toString(),
      transactionReference: json['transaction_reference']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'deleted_at': deletedAt,
      'amount': amount,
      'giving_type': givingType,
      'notes': notes,
      'member_id': memberId,
      'organisation_id': organisationId,
      'status': status,
      'payment_method': paymentMethod,
      'transaction_reference': transactionReference,
    };
  }

  GivingModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    double? amount,
    String? givingType,
    String? notes,
    String? memberId,
    String? organisationId,
    String? status,
    String? paymentMethod,
    String? transactionReference,
  }) {
    return GivingModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      amount: amount ?? this.amount,
      givingType: givingType ?? this.givingType,
      notes: notes ?? this.notes,
      memberId: memberId ?? this.memberId,
      organisationId: organisationId ?? this.organisationId,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      transactionReference: transactionReference ?? this.transactionReference,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GivingModel &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          amount == other.amount &&
          givingType == other.givingType &&
          memberId == other.memberId;

  @override
  int get hashCode => Object.hash(id, amount, givingType, memberId);

  @override
  String toString() {
    return 'GivingModel{id: $id, amount: $amount, givingType: $givingType, memberId: $memberId, organisationId: $organisationId}';
  }
}
