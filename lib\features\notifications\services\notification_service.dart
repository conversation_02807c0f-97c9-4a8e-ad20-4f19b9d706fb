import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../data/models/notification_model.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';

class NotificationService {
  final HttpService _httpService = Get.find<HttpService>();
  final logger = Get.find<Logger>();

  // Fetch notifications with pagination
  Future<Map<String, dynamic>> fetchNotifications({
    int page = 0,
    int pageSize = 10,
    String? searchQuery,
    bool unreadOnly = false,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'page': page.toString(),
        'size': pageSize.toString(),
      };

      if (searchQuery != null && searchQuery.isNotEmpty) {
        queryParams['search'] = searchQuery;
      }

      if (unreadOnly) {
        queryParams['unread_only'] = 'true';
      }

      final response = await _httpService.request(
        url: ApiUrls.notifications,
        method: Method.GET,
        params: queryParams,
      );

      if (response.data['status']) {
        final List<dynamic> notificationsData =
            response.data['data']['notifications'];
        final List<NotificationModel> notifications =
            notificationsData
                .map((item) => NotificationModel.fromJson(item))
                .toList();

        return {
          'notifications': notifications,
          'total_pages': response.data['data']['total_pages'] ?? 0,
          'total_items': response.data['data']['total_items'] ?? 0,
          'current_page': response.data['data']['current_page'] ?? 0,
        };
      } else {
        throw Exception(
          response.data['message'] ?? 'Failed to fetch notifications',
        );
      }
    } catch (e) {
      logger.e('Error fetching notifications: $e');
      throw Exception('Failed to fetch notifications: $e');
    }
  }

  // Mark notification as read
  Future<bool> markAsRead(int notificationId) async {
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.notifications}/$notificationId/read',
        method: Method.PUT,
      );

      return response.data['status'] ?? false;
    } catch (e) {
      logger.e('Error marking notification as read: $e');
      return false;
    }
  }

  // Mark all notifications as read
  Future<bool> markAllAsRead() async {
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.notifications}/read-all',
        method: Method.PUT,
      );

      return response.data['status'] ?? false;
    } catch (e) {
      logger.e('Error marking all notifications as read: $e');
      return false;
    }
  }

  // Delete a notification
  Future<bool> deleteNotification(int notificationId) async {
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.notifications}/$notificationId',
        method: Method.DELETE,
      );

      return response.data['status'] ?? false;
    } catch (e) {
      logger.e('Error deleting notification: $e');
      return false;
    }
  }

  // Get unread notifications count
  Future<int> getUnreadCount() async {
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.notifications}/unread-count',
        method: Method.GET,
      );

      if (response.data['status']) {
        return response.data['data']['count'] ?? 0;
      } else {
        return 0;
      }
    } catch (e) {
      logger.e('Error getting unread count: $e');
      return 0;
    }
  }
}
