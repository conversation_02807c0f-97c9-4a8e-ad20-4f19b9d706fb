import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/notification_controller.dart';

class NotificationBadge extends StatelessWidget {
  final Widget child;
  final double top;
  final double right;
  final double size;
  final Color? backgroundColor;
  final Color? textColor;
  
  const NotificationBadge({
    super.key,
    required this.child,
    this.top = 0,
    this.right = 0,
    this.size = 18,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<NotificationController>();
    final theme = Theme.of(context);
    
    return Stack(
      clipBehavior: Clip.none,
      children: [
        child,
        Obx(() {
          final count = controller.unreadCount.value;
          if (count <= 0) return const SizedBox.shrink();
          
          return Positioned(
            top: top,
            right: right,
            child: Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                color: backgroundColor ?? theme.colorScheme.error,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  count > 99 ? '99+' : count.toString(),
                  style: TextStyle(
                    color: textColor ?? theme.colorScheme.onError,
                    fontSize: size * 0.6,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }
}