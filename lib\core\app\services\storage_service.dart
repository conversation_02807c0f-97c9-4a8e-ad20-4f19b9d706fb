import 'package:get_storage/get_storage.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../../data/models/user_model.dart';

class StorageService {
  final GetStorage _storage = GetStorage();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  // Regular storage methods
  Future<void> write(String key, dynamic value) async {
    await _storage.write(key, value);
  }

  T? read<T>(String key) {
    return _storage.read<T>(key);
  }

  Future<void> remove(String key) async {
    await _storage.remove(key);
  }

  Future<void> clearAll() async {
    await _storage.erase();
  }

  bool hasData(String key) {
    return _storage.hasData(key);
  }

  // Secure storage methods
  Future<void> writeSecure(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  Future<String?> readSecure(String key) async {
    return await _secureStorage.read(key: key);
  }

  Future<void> removeSecure(String key) async {
    await _secureStorage.delete(key: key);
  }

  Future<void> clearAllSecure() async {
    await _secureStorage.deleteAll();
  }

  Future<bool> hasSecureData(String key) async {
    final value = await _secureStorage.read(key: key);
    return value != null;
  }

  Future<Map<String, String>> getAllSecure() async {
    return await _secureStorage.readAll();
  }

  // Convenience methods for common data
  Future<void> saveToken(String token) async {
    await writeSecure('token', token);
  }

  Future<String?> getToken() async {
    return await readSecure('token');
  }

  Future<void> removeToken() async {
    await removeSecure('token');
  }

  Future<void> saveUserData(Map<String, dynamic> userData) async {
    await write('user_data', userData);
  }

  Map<String, dynamic>? getUserData() {
    return read<Map<String, dynamic>>('user_data');
  }

  Future<void> removeUserData() async {
    await remove('user_data');
  }

  // UserModel specific methods
  Future<void> saveUserModel(UserModel userModel) async {
    await write('user_model', userModel.toJson());
  }

  UserModel? getUserModel() {
    final userData = read<Map<String, dynamic>>('user_model');
    if (userData != null) {
      return UserModel.fromJson(userData);
    }
    return null;
  }

  Future<void> removeUserModel() async {
    await remove('user_model');
  }

  Future<void> clearAllData() async {
    await clearAll();
    await clearAllSecure();
  }
}
