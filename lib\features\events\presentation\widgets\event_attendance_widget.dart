import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import '../../../../core/app/widgets/right_sidebar.dart';
import '../../../../data/models/event_model.dart';
import '../../../attendance/bindings/attendance_binding.dart';
import '../../../attendance/controllers/attendance_controller.dart';
import '../../../attendance/models/attendance_model.dart';
import '../../../attendance/presentation/widgets/attendance_filter_widget.dart';
import 'mark_attendance_form_widget.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
class EventAttendanceWidget extends StatefulWidget {
  final String eventId;
  final EventModel? event;

  const EventAttendanceWidget({super.key, required this.eventId, this.event});

  @override
  State<EventAttendanceWidget> createState() => _EventAttendanceWidgetState();
}

class _EventAttendanceWidgetState extends State<EventAttendanceWidget> {
  late AttendanceController _attendanceController;
  final RxBool showFilter = false.obs;

  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  late PlutoGridStateManager stateManager;

  @override
  void initState() {
    super.initState();

    // Ensure attendance dependencies are registered
    if (!Get.isRegistered<AttendanceController>()) {
      AttendanceBinding().dependencies();
    }

    _attendanceController = Get.find<AttendanceController>();
    setColumns();

    // Set the event filter when the widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _attendanceController.setEventFilter(widget.eventId);
    });
  }

  @override
  void dispose() {
    // Clear the event filter when leaving this widget
    _attendanceController.eventIdFilter.value = '';
    super.dispose();
  }

  // Setup rows for the PlutoGrid
  List<PlutoRow> setupRows(List<AttendanceModel> attendanceList) {
    return attendanceList.map((attendance) {
      return PlutoRow(
        cells: {
          'id': PlutoCell(value: attendance.id ?? ''),
          'attendee_name': PlutoCell(value: attendance.attendeeName ?? 'N/A'),
          'attendee_email': PlutoCell(value: attendance.attendeeEmail ?? 'N/A'),
          'attendee_phone': PlutoCell(value: attendance.attendeePhone ?? 'N/A'),
          'time_in': PlutoCell(
            value:
                attendance.timeIn != null
                    ? _attendanceController.formatDateTime(attendance.timeIn!)
                    : 'N/A',
          ),
          'time_out': PlutoCell(
            value:
                attendance.timeOut != null
                    ? _attendanceController.formatDateTime(attendance.timeOut!)
                    : 'N/A',
          ),
          'location': PlutoCell(value: attendance.locationName ?? 'N/A'),
          'created_by': PlutoCell(value: attendance.createdByUserName),
          'is_visitor': PlutoCell(
            value: attendance.isVisitor == true ? 'Yes' : 'No',
          ),
          'actions': PlutoCell(value: attendance.id),
        },
      );
    }).toList();
  }

  // Set up columns for the PlutoGrid
  void setColumns() {
    columns = [
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.text(),
        width: 80,
        enableRowChecked: true,
        hide: true, // Hidden but available for filtering
      ),
      PlutoColumn(
        title: 'Attendee Name',
        field: 'attendee_name',
        type: PlutoColumnType.text(),
        width: 180,
      ),
      PlutoColumn(
        title: 'Email',
        field: 'attendee_email',
        type: PlutoColumnType.text(),
        width: 200,
      ),
      PlutoColumn(
        title: 'Phone',
        field: 'attendee_phone',
        type: PlutoColumnType.text(),
        width: 140,
      ),
      PlutoColumn(
        title: 'Clock In',
        field: 'time_in',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Clock Out',
        field: 'time_out',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Location',
        field: 'location',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Created By',
        field: 'created_by',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Visitor',
        field: 'is_visitor',
        type: PlutoColumnType.text(),
        width: 80,
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 180,
        renderer: (rendererContext) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(
                  Icons.access_time,
                  color: Colors.orange,
                  size: 20,
                ),
                onPressed: () {
                  final attendance = _attendanceController.attendanceList
                      .firstWhereOrNull(
                        (a) => a.id == rendererContext.cell.value,
                      );
                  if (attendance != null) {
                    _showTimeoutConfirmation(attendance);
                  }
                },
                tooltip: 'Mark Timeout',
                constraints: const BoxConstraints(maxWidth: 30),
                padding: EdgeInsets.zero,
              ),
            ],
          );
        },
      ),
    ];
  }

  void _showMarkAttendanceForm() {
    if (widget.event == null) return;

    RightSidebar.show(
      context: context,
      title: 'Mark Attendance',
      child: MarkAttendanceFormWidget(
        event: widget.event!,
        onSuccess: () {
          Navigator.of(context).pop();
          // Refresh the attendance list
          _attendanceController.fetchAttendance();
        },
      ),
      onClose: () {},
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header with filter toggle and mark attendance button
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Text(
                'Event Attendance',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              // Mark Attendance Button
              if (widget.event != null) ...[
                CustomButton(
                  onPressed: _showMarkAttendanceForm,
                  icon: const Icon(IconlyLight.addUser, size: 18),
                  label: const Text('Mark Attendance'),
                 
                ),
                const SizedBox(width: 12),
              ],
              Obx(
                () => IconButton(
                  onPressed: () {
                    showFilter.value = !showFilter.value;
                  },
                  icon:
                      showFilter.value
                          ? const Icon(Icons.filter_alt)
                          : const Icon(Icons.filter_alt_outlined),
                  tooltip: 'Toggle Filters',
                ),
              ),
            ],
          ),
        ),

        // Filters section
        Obx(
          () =>
              showFilter.value
                  ? const AttendanceFilterWidget()
                  : const SizedBox(),
        ),
        Gap(8.h),

        // Stats and info
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            children: [
              Obx(
                () => Text(
                  "Total Attendance Records: ${_attendanceController.totalItems}",
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const Spacer(),
              Obx(
                () =>
                    _attendanceController.isLoading.value
                        ? const CircleLoadingAnimation()
                        : const SizedBox.shrink(),
              ),
            ],
          ),
        ),
        Gap(8.h),

        // Attendance grid
        Expanded(
          child: Card(
            color: Theme.of(context).secondaryHeaderColor,
            margin: const EdgeInsets.all(12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            elevation: 4,
            child: Obx(() {
              if (_attendanceController.isLoading.value &&
                  _attendanceController.attendanceList.isEmpty) {
                return const Center(child: CircleLoadingAnimation());
              }

              if (_attendanceController.attendanceList.isEmpty &&
                  !_attendanceController.isLoading.value) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.people_outline, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'No attendance records found for this event',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              }

              rows = setupRows(_attendanceController.attendanceList);

              return PlutoGrid(
                mode: PlutoGridMode.selectWithOneTap,
                columns: columns,
                rows: rows,
                onLoaded: (PlutoGridOnLoadedEvent event) {
                  stateManager = event.stateManager;
                  stateManager.setShowColumnFilter(true);
                },
                configuration: PlutoGridConfiguration(
                  style: PlutoGridStyleConfig(
                    activatedColor: const Color.fromARGB(255, 165, 205, 253),
                    cellTextStyle: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Color.fromARGB(255, 64, 64, 64),
                    ),
                    columnTextStyle: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blueGrey,
                    ),
                  ),
                  columnSize: const PlutoGridColumnSizeConfig(
                    autoSizeMode: PlutoAutoSizeMode.scale,
                  ),
                ),
              );
            }),
          ),
        ),
      ],
    );
  }

  // Show timeout confirmation dialog
  void _showTimeoutConfirmation(AttendanceModel attendance) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Mark Timeout'),
          content: const Text(
            'Are you sure you want to mark timeout for this attendance record?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            CustomButton(
              onPressed: () {
                Navigator.of(context).pop();
                _attendanceController.markTimeout(attendance);
              },
              label: const Text('Mark Timeout'),
            ),
          ],
        );
      },
    );
  }
}
