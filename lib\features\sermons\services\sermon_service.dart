import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../data/models/sermon_model.dart';

class SermonService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();

  // Initialize the service
  SermonService() {
    _httpService.initializeDio();
  }

  // Fetch sermons with pagination and filters
  Future<Map<String, dynamic>> fetchSermons({
    required int page,
    required int size,
    String? searchQuery,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
    String? organisationId,
  }) async {
    try {
      // Prepare request parameters
      final Map<String, dynamic> params = {
        'organisation_id': organisationId,
        'page': page,
        'size': size,
      };

      // Add search query if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        params['search'] = searchQuery;
      }

      // Add status filter if provided
      if (status != null && status.isNotEmpty) {
        params['status'] = status;
      }

      // Add date filters if provided
      if (startDate != null) {
        params['start_date'] = startDate.toIso8601String();
      }

      if (endDate != null) {
        params['end_date'] = endDate.toIso8601String();
      }

      final response = await _httpService.request(
        url: "${ApiUrls.sermons}?page=$page&size=$size",
        method: Method.GET,
        params: params,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching sermons: $e');
      rethrow;
    }
  }

  // Get sermon by ID
  Future<Map<String, dynamic>> getSermonById(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.sermonById}$id",
        method: Method.GET,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching sermon by ID: $e');
      rethrow;
    }
  }

  // Parse sermon items from API response
  List<SermonModel> parseSermons(List<dynamic> items) {
    // Directly parse to SermonModel objects
    return items.map((item) => SermonModel.fromJson(item)).toList();
  }

  // Create a new sermon
  Future<Map<String, dynamic>> createSermon({
    required String title,
    required String description,
    String? category,
    String status = 'active',
    List<Map<String, dynamic>>? media,
  }) async {
    try {
      final Map<String, dynamic> sermonData = {
        'title': title,
        'description': description,
        'status': status,
        'organisation_id': "0196af91-32c4-713a-b994-7d3ce787e312",
      };

      if (category != null && category.isNotEmpty) {
        sermonData['category'] = category;
      }

      if (media != null && media.isNotEmpty) {
        sermonData['media'] = media;
      }
      logger.d('Creating sermon with payload: $sermonData');

      final response = await _httpService.request(
        url: ApiUrls.createSermon,
        method: Method.POST,
        params: sermonData,
      );

      logger.d('Sermon creation response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error creating sermon: $e');
      rethrow;
    }
  }

  // Update an existing sermon
  Future<Map<String, dynamic>> updateSermon({
    required String id,
    String? title,
    String? description,
    String? category,
    String? status,
    List<Map<String, dynamic>>? media,
  }) async {
    try {
      final Map<String, dynamic> sermonData = {'id': id};

      if (title != null) sermonData['title'] = title;
      if (description != null) sermonData['description'] = description;
      if (category != null && category.isNotEmpty) {
        sermonData['category'] = category;
      }
      if (status != null) sermonData['status'] = status;
      if (media != null) sermonData['media'] = media;

      // Wrap the sermon data in an array as expected by the API
      final List<Map<String, dynamic>> requestData = [sermonData];

      logger.d('Updating sermon with payload: $requestData');

      final response = await _httpService.request(
        url: "${ApiUrls.sermonById}$id/",
        method: Method.PUT,
        params: requestData, // Send as array
      );

      logger.d('Sermon update response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error updating sermon: $e');
      rethrow;
    }
  }

  // Delete a sermon
  Future<Map<String, dynamic>> deleteSermon(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.sermonById}$id/",
        method: Method.DELETE,
      );

      logger.d('Sermon deletion response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error deleting sermon: $e');
      rethrow;
    }
  }
}
