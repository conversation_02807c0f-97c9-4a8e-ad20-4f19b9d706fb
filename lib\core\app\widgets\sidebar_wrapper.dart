import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A right sidebar wrapper that can contain any form widget
/// This implementation uses a Dialog approach to ensure proper overlay behavior
class SideBarWrapper extends StatefulWidget {
  final Widget child;
  final String title;
  final VoidCallback onClose;

  const SideBarWrapper({
    super.key,
    required this.child,
    required this.title,
    required this.onClose,
  });

  @override
  State<SideBarWrapper> createState() => _SideBarWrapperState();
}

class _SideBarWrapperState extends State<SideBarWrapper>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _close() {
    _animationController.reverse().then((_) {
      widget.onClose();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final size = MediaQuery.of(context).size;
    final sidebarWidth = (size.width * 0.3).clamp(350.0, 450.0);

    // Use Dialog approach for proper overlay behavior
    return Dialog(
      // Make the dialog transparent and full-screen
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.zero,
      // Disable the default dialog behavior
      child: SizedBox(
        width: size.width,
        height: size.height,
        child: Stack(
          children: [
            // Semi-transparent backdrop
            Positioned.fill(
              child: GestureDetector(
                onTap: _close,
                child: Container(color: Colors.black.withOpacity(0.1)),
              ),
            ),

            // Animated sidebar
            AnimatedBuilder(
              animation: _animation,
              builder: (context, _) {
                return Positioned(
                  right:
                      _animation.value * 0 +
                      (1 - _animation.value) * -sidebarWidth,
                  top: 0,
                  bottom: 0,
                  width: sidebarWidth,
                  child: GestureDetector(
                    // Prevent taps from closing the sidebar
                    onTap: () {},
                    child: Container(
                      decoration: BoxDecoration(
                        color: colorScheme.surface,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 10,
                            offset: const Offset(-2, 0),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // Header
                          Container(
                            padding: EdgeInsets.all(16.r),
                            decoration: BoxDecoration(
                              color: colorScheme.surface,
                              boxShadow: [
                                BoxShadow(
                                  color: colorScheme.shadow.withOpacity(0.05),
                                  blurRadius: 2,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                              border: Border(
                                bottom: BorderSide(
                                  color: colorScheme.outline.withOpacity(0.1),
                                  width: 1,
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                Text(
                                  widget.title,
                                  style: Theme.of(context).textTheme.titleLarge
                                      ?.copyWith(fontWeight: FontWeight.bold),
                                ),
                                const Spacer(),
                                IconButton(
                                  icon: const Icon(Icons.close),
                                  onPressed: _close,
                                  tooltip: 'Close',
                                ),
                              ],
                            ),
                          ),

                          // Form content
                          Expanded(child: widget.child),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
