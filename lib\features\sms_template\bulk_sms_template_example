import 'package:http/http.dart';

class Bulk {
  Funtion(String message, List users)? onsend;
  Bulk({required this.onsend});
}

class Example {

  DisplaySmsGrid(
    organisationId: '',
    includeFilter: true,
    includeDashboard: true,
    sizePerPage: 10, 
    // start_date end_date, message, status, phone_number    
  ),

  SmsStatistics( 
    organizationId : '', 
    // should show total_sms_send, wallet_balance, total_succesful, total_blocked, total_pending, graph(sms agains data)-include 3 graphLines: purple(totalsent),red(totalBlocked),green(totalSuccess)    
  )


  
  SendBulkSms(
    // for excel ,
    prefilledMembers: <Member>[], 
    organisationId : '', 
    onsend: (String message, List<Member> users){
                  String api = "api.sms.salticon.com/sendsms";
                  Map<String, dynamic> body = { 
                    "organisation_id" : 'id',
                    "entity_type" : '', // entity_type: CUSTOMER_ID, MEMBER_ID, STAFF_ID, GROUP_ID, PHONE_NUMBER, 
                    "entity_ids" : <String>[] ,
                    "message" : '', 
                    "is_confirm" : false, // start with false for summary - then true for actual send

                   };
                 final response = await _httpService.request(url: api, method: Method.POST, params: body);
                final backendResponse = {
                  'status' : true, 
                  'message' : 'backend message', 
                  'data' : {
                    'total_receipients' : 10,
                    'total_characters' : 10,
                    'total_sms' : 1,
                    'total_charges' : 1.0,
                    'price_per_unit' : 1.0,
                    'current_units_balance' : 1.0, 
                    'after_units_balance' : 1.0,
                    'should_topup' : false, // if true route to topup
                    'sender_id' : 'ONEKITTY',
                  },

                };
              }
   );

}