import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../controllers/inventory_dashboard_controller.dart';
import '../widgets/dashboard/dashboard_app_bar.dart';
import '../widgets/dashboard/enhanced_stats_widget.dart';
import '../widgets/dashboard/quick_actions_widget.dart';
import '../widgets/dashboard/toggle_status_indicator.dart';
import '../widgets/inventory_filter_widget.dart';
import '../widgets/inventory_items_table_widget.dart';
import '../widgets/inventory_records_table_widget.dart';

class InventoryDashboardScreen extends StatelessWidget {
  const InventoryDashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize the dashboard controller
    final controller = Get.put(InventoryDashboardController());
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.background,
      appBar: const DashboardAppBar(),
      body: Column(
        children: [
          // Toggle Status Indicator
          const ToggleStatusIndicator(),

          // Enhanced Stats Section - Toggleable
          Obx(
            () =>
                controller.showDashboard.value
                    ? EnhancedStatsWidget(
                      isItemsView: controller.isItemsView.value,
                    )
                    : const SizedBox.shrink(),
          ),

          // Quick Actions Section - Toggleable
          Obx(
            () =>
                controller.showQuickActions.value
                    ? const QuickActionsWidget()
                    : const SizedBox.shrink(),
          ),

          // Filter Section - Toggleable
          Obx(
            () =>
                controller.showFilters.value
                    ? InventoryFilterWidget(
                      isItemsView: controller.isItemsView.value,
                    )
                    : const SizedBox.shrink(),
          ),

          // Content Section
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              child: TabBarView(
                controller: controller.tabController,
                children: const [
                  InventoryItemsTableWidget(),
                  InventoryRecordsTableWidget(),
                ],
              ),
            ),
          ),

          // Bottom spacing
          SizedBox(height: 16.h),
        ],
      ),
      floatingActionButton: Obx(
        () => FloatingActionButton.extended(
          onPressed: () => controller.handleCreateAction(context),
          icon: Icon(
            controller.isItemsView.value
                ? IconlyLight.plus
                : IconlyLight.document,
            size: 20,
          ),
          label: Text(
            controller.currentActionLabel,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
          backgroundColor: colorScheme.primary,
          foregroundColor: Colors.white,
          elevation: 4,
          extendedPadding: EdgeInsets.symmetric(
            horizontal: 20.w,
            vertical: 12.h,
          ),
          tooltip: controller.currentActionTooltip,
        ),
      ),
    );
  }
}
