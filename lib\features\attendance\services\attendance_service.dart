import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../models/attendance_model.dart';

class AttendanceService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();

  // Initialize the service
  AttendanceService() {
    _httpService.initializeDio();
  }

  // Fetch attendance records with pagination and filters
  Future<Map<String, dynamic>> fetchAttendance({
    required int page,
    required int size,
    String? memberId,
    String? eventId,
    DateTime? timeIn,
    DateTime? timeOut,
    String? attendeePhone,
    String? attendeeName,
    String? organisationId,
  }) async {
    try {
      // Build URL with query parameters
      String url = "${ApiUrls.getAttendance}?page=$page&size=$size";

      // Add optional filters to URL
      if (memberId != null && memberId.isNotEmpty) {
        url += "&member_id=${Uri.encodeComponent(memberId)}";
      }

      if (eventId != null && eventId.isNotEmpty) {
        url += "&event_id=${Uri.encodeComponent(eventId)}";
        logger.d('Adding event_id to attendance request: $eventId');
      }

      if (attendeePhone != null && attendeePhone.isNotEmpty) {
        url += "&attendee_phone=${Uri.encodeComponent(attendeePhone)}";
      }

      if (attendeeName != null && attendeeName.isNotEmpty) {
        url += "&attendee_name=${Uri.encodeComponent(attendeeName)}";
      }

      // Add date filters if provided
      if (timeIn != null) {
        final formattedTimeIn = timeIn.toIso8601String().split('T')[0];
        url += "&time_in=${Uri.encodeComponent(formattedTimeIn)}";
      }

      if (timeOut != null) {
        final formattedTimeOut = timeOut.toIso8601String().split('T')[0];
        url += "&time_out=${Uri.encodeComponent(formattedTimeOut)}";
      }

      logger.d('Fetching attendance with URL: $url');

      final response = await _httpService.request(url: url, method: Method.GET);

      return response.data;
    } catch (e) {
      logger.e('Error fetching attendance: $e');
      rethrow;
    }
  }

  // Mark attendance (create or update)
  Future<Map<String, dynamic>> markAttendance(
    AttendanceModel attendance,
  ) async {
    try {
      final Map<String, dynamic> attendanceData = attendance.toJson();

      logger.d('Marking attendance with payload: $attendanceData');

      final response = await _httpService.request(
        url: ApiUrls.markAttendance,
        method: Method.POST,
        params: attendanceData,
      );

      logger.d('Attendance marking response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error marking attendance: $e');
      rethrow;
    }
  }

  // Update attendance record (for timeout functionality)
  Future<Map<String, dynamic>> updateAttendance(
    AttendanceModel attendance,
  ) async {
    try {
      logger.d('Updating attendance ${attendance.id} with timeout ');

      final response = await _httpService.request(
        url: "${ApiUrls.markAttendance}/",
        method: Method.PUT,
        params: attendance.copyWith(timeOut: DateTime.now().toUtc().toIso8601String()).toJson(),
      );

      logger.d('Attendance update response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error updating attendance: $e');
      rethrow;
    }
  }

  // Mark timeout for attendance
  Future<Map<String, dynamic>> markTimeout(AttendanceModel attendance) async {
    try {
     

      return await updateAttendance(attendance);
    } catch (e) {
      logger.e('Error marking timeout: $e');
      rethrow;
    }
  }

  // Parse attendance items from API response
  List<AttendanceModel> parseAttendance(List<dynamic> items) {
    return items.map((item) => AttendanceModel.fromJson(item)).toList();
  }
}
