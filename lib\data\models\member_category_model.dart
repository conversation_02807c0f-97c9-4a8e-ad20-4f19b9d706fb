import 'dart:convert';

class MemberCategory {
    final String? id;
    final DateTime? createdAt;
    final DateTime? updatedAt;
    final dynamic deletedAt;
    final String? title;
    final String? description;
    final String? code;
    final String? organisationId;
    final bool? isGeneral;
    final bool? isActive;

    MemberCategory({
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.title,
        this.description,
        this.code,
        this.organisationId,
        this.isGeneral,
        this.isActive,
    });

    MemberCategory copyWith({
        String? id,
        DateTime? createdAt,
        DateTime? updatedAt,
        dynamic deletedAt,
        String? title,
        String? description,
        String? code,
        String? organisationId,
        bool? isGeneral,
        bool? isActive,
      }) => 
        MemberCategory(
            id: id ?? this.id,
            createdAt: createdAt ?? this.createdAt,
            updatedAt: updatedAt ?? this.updatedAt,
            deletedAt: deletedAt ?? this.deletedAt,
            title: title ?? this.title,
            description: description ?? this.description,
            code: code ?? this.code,
            organisationId: organisationId ?? this.organisationId,
            isGeneral: isGeneral ?? this.isGeneral,
            isActive: isActive ?? this.isActive,
        );

    factory MemberCategory.fromRawJson(String str) => MemberCategory.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory MemberCategory.fromJson(Map<String, dynamic> json) => MemberCategory(
        id: json["id"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        deletedAt: json["deleted_at"],
        title: json["title"],
        description: json["description"],
        code: json["code"],
        organisationId: json["organisation_id"],
        isGeneral: json["is_general"],
        isActive: json["is_active"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt,
        "title": title,
        "description": description,
        "code": code,
        "organisation_id": organisationId,
        "is_general": isGeneral,
    };
}
