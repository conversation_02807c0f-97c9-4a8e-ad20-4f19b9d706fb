import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import '../../../../../core/app/constants/enums.dart';
import '../../../controllers/relationship_controller.dart';

// Import the split widget files
import 'relationship_types_table.dart';
import 'add_relationship_type_dialog.dart';

/// Build relationship types dialog for larger screens
Widget buildRelationshipTypesDialog(
  BuildContext context,
  RelationshipController relationshipController,
) {
  final theme = Theme.of(context);

  return Dialog(
    backgroundColor: theme.colorScheme.surface,
    insetPadding: const EdgeInsets.all(16),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
    child: Container(
      constraints: BoxConstraints(
        maxWidth: 800.w,
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          _buildRelationshipTypesHeader(context, theme, relationshipController),
          Gap(16.h),
          _buildRelationshipTypesFilter(context, theme, relationshipController),
          Gap(16.h),
          Expanded(
            child: buildRelationshipTypesTable(context, relationshipController),
          ),
        ],
      ),
    ),
  );
}

/// Build relationship types bottom sheet for smaller screens
Widget buildRelationshipTypesSheet(
  BuildContext context,
  RelationshipController relationshipController,
) {
  final theme = Theme.of(context);

  return Container(
    padding: EdgeInsets.only(
      bottom: MediaQuery.of(context).viewInsets.bottom,
      left: 16.w,
      right: 16.w,
      top: 16.h,
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildRelationshipTypesHeader(context, theme, relationshipController),
        Gap(16.h),
        _buildRelationshipTypesFilter(context, theme, relationshipController),
        Gap(16.h),
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.6,
          child: buildRelationshipTypesTable(context, relationshipController),
        ),
        Gap(16.h),
      ],
    ),
  );
}

/// Build relationship types header with title and add button
Widget _buildRelationshipTypesHeader(
  BuildContext context,
  ThemeData theme,
  RelationshipController relationshipController,
) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Text('Manage Relationship Types', style: theme.textTheme.titleLarge),
      IconButton(
        icon: const Icon(IconlyLight.plus),
        onPressed: () {
          Navigator.pop(context); // Close current dialog/sheet
          showAddRelationshipTypeDialog(context, relationshipController);
        },
        tooltip: 'Add Relationship Type',
      ),
    ],
  );
}

/// Build relationship types filter with search and category dropdown
Widget _buildRelationshipTypesFilter(
  BuildContext context,
  ThemeData theme,
  RelationshipController relationshipController,
) {
  final colorScheme = theme.colorScheme;

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      CustomTextField(
        controller: relationshipController.typeSearchController,
        labelText: 'Search',
        hintText: 'Search relationship types',
        prefixIcon: const Icon(IconlyLight.search),
        onChanged: (value) {
          relationshipController.searchQueryTypes.value = value;
          relationshipController.fetchRelationshipTypes();
        },
      ),
      Gap(12.h),
      Container(
        decoration: BoxDecoration(
          border: Border.all(color: colorScheme.outline.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(8.r),
        ),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
        child: DropdownButtonHideUnderline(
          child: DropdownButton<String>(
            value:
                relationshipController.selectedCategory.value.isEmpty
                    ? null
                    : relationshipController.selectedCategory.value,
            hint: Text(
              'Filter by Category',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            icon: const Icon(IconlyLight.arrowDown2),
            isExpanded: true,
            items: [
              DropdownMenuItem<String>(
                value: '',
                child: Text(
                  'All Categories',
                  style: theme.textTheme.bodyMedium,
                ),
              ),
              ...MemberRelationshipCategory.values.map((category) {
                return DropdownMenuItem<String>(
                  value: category.displayName,
                  child: Text(
                    category.displayName,
                    style: theme.textTheme.bodyMedium,
                  ),
                );
              }),
            ],
            onChanged: (value) {
              relationshipController.selectedCategory.value = value ?? '';
              relationshipController.fetchRelationshipTypes();
            },
          ),
        ),
      ),
    ],
  );
}
