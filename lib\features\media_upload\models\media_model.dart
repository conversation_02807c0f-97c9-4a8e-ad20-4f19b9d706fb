import 'dart:convert';

class MediaModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final String? mediaUrl;
  final String? type;
  final String? category;
  final int? sizeBytes;
  final dynamic invoice;
  final dynamic invoiceId;
  final dynamic invoiceItem;
  final dynamic invoiceItemId;
  final String? organisationId;
  final dynamic event;
  final String? eventId;
  final dynamic group;
  final dynamic groupId;
  final dynamic sermon;
  final String? sermonId;
  final dynamic announcement;
  final dynamic announcementId;
  final dynamic notification;
  final dynamic notificationId;

  const MediaModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.mediaUrl,
    this.type,
    this.category,
    this.sizeBytes,
    this.invoice,
    this.invoiceId,
    this.invoiceItem,
    this.invoiceItemId,
    this.organisationId,
    this.event,
    this.eventId,
    this.group,
    this.groupId,
    this.sermon,
    this.sermonId,
    this.announcement,
    this.announcementId,
    this.notification,
    this.notificationId,
  });

  MediaModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? title,
    String? mediaUrl,
    String? type,
    String? category,
    int? sizeBytes,
    dynamic invoice,
    dynamic invoiceId,
    dynamic invoiceItem,
    dynamic invoiceItemId,
    String? organisationId,
    dynamic event,
    String? eventId,
    dynamic group,
    dynamic groupId,
    dynamic sermon,
    String? sermonId,
    dynamic announcement,
    dynamic announcementId,
    dynamic notification,
    dynamic notificationId,
  }) => MediaModel(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    title: title ?? this.title,
    mediaUrl: mediaUrl ?? this.mediaUrl,
    type: type ?? this.type,
    category: category ?? this.category,
    sizeBytes: sizeBytes ?? this.sizeBytes,
    invoice: invoice ?? this.invoice,
    invoiceId: invoiceId ?? this.invoiceId,
    invoiceItem: invoiceItem ?? this.invoiceItem,
    invoiceItemId: invoiceItemId ?? this.invoiceItemId,
    organisationId: organisationId ?? this.organisationId,
    event: event ?? this.event,
    eventId: eventId ?? this.eventId,
    group: group ?? this.group,
    groupId: groupId ?? this.groupId,
    sermon: sermon ?? this.sermon,
    sermonId: sermonId ?? this.sermonId,
    announcement: announcement ?? this.announcement,
    announcementId: announcementId ?? this.announcementId,
    notification: notification ?? this.notification,
    notificationId: notificationId ?? this.notificationId,
  );

  factory MediaModel.fromRawJson(String str) =>
      MediaModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MediaModel.fromJson(Map<String, dynamic> json) => MediaModel(
    id: json["ID"]?.toString() ?? json["id"]?.toString(),
    createdAt:
        json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : json["created_at"] != null
            ? DateTime.parse(json["created_at"])
            : null,
    updatedAt:
        json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : json["updated_at"] != null
            ? DateTime.parse(json["updated_at"])
            : null,
    deletedAt: json["DeletedAt"] ?? json["deleted_at"],
    title: json["title"],
    mediaUrl: json["media_url"],
    type: json["type"],
    category: json["category"],
    sizeBytes: json["size_bytes"],
    invoice: json["invoice"],
    invoiceId: json["invoice_id"],
    invoiceItem: json["invoice_item"],
    invoiceItemId: json["invoice_item_id"],
    organisationId: json["organisation_id"],
    event: json["event"],
    eventId: json["event_id"],
    group: json["group"],
    groupId: json["group_id"],
    sermon: json["sermon"],
    sermonId: json["sermon_id"]?.toString(),
    announcement: json["announcement"],
    announcementId: json["announcement_id"],
    notification: json["notification"],
    notificationId: json["notification_id"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "title": title,
    "media_url": mediaUrl,
    "type": type,
    "category": category,
    "size_bytes": sizeBytes,
    "invoice": invoice,
    "invoice_id": invoiceId,
    "invoice_item": invoiceItem,
    "invoice_item_id": invoiceItemId,
    "organisation_id": organisationId,
    "event": event,
    "event_id": eventId,
    "group": group,
    "group_id": groupId,
    "sermon": sermon,
    "sermon_id": sermonId,
    "announcement": announcement,
    "announcement_id": announcementId,
    "notification": notification,
    "notification_id": notificationId,
  };

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MediaModel && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
