import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import '../../controllers/inventory_controller.dart';
import '../../controllers/inventory_item_controller.dart';

class InventoryStatsWidget extends StatelessWidget {
  final bool isItemsView;

  const InventoryStatsWidget({super.key, required this.isItemsView});

  @override
  Widget build(BuildContext context) {
    // Check if controllers are available before building
    if (!Get.isRegistered<InventoryItemController>() ||
        !Get.isRegistered<InventoryController>()) {
      return const SizedBox.shrink(); // Return empty widget if controllers not ready
    }

    final mediaQuery = MediaQuery.of(context);
    final isMobile = mediaQuery.size.width < 600;

    return Container(
      margin: const EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    isItemsView ? IconlyLight.category : IconlyLight.document,
                    color: Theme.of(context).primaryColor,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      isItemsView
                          ? 'Inventory Items Overview'
                          : 'Inventory Records Overview',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).primaryColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              if (isMobile)
                _buildMobileStats(context)
              else
                _buildDesktopStats(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMobileStats(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                isItemsView ? 'Total Items' : 'Total Records',
                _getTotalValue(),
                IconlyLight.category,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                context,
                isItemsView ? 'Categories' : 'Types',
                _getCategoriesValue(),
                IconlyLight.folder,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                'Current Page',
                _getCurrentPageValue(),
                IconlyLight.document,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                context,
                'Total Pages',
                _getTotalPagesValue(),
                IconlyLight.paper,
                Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDesktopStats(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            isItemsView ? 'Total Items' : 'Total Records',
            _getTotalValue(),
            IconlyLight.category,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            context,
            isItemsView ? 'Categories' : 'Types',
            _getCategoriesValue(),
            IconlyLight.folder,
            Colors.green,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            context,
            'Current Page',
            _getCurrentPageValue(),
            IconlyLight.document,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            context,
            'Total Pages',
            _getTotalPagesValue(),
            IconlyLight.paper,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  // Safe helper methods to get values from controllers
  String _getTotalValue() {
    try {
      if (isItemsView) {
        if (Get.isRegistered<InventoryItemController>()) {
          final controller = Get.find<InventoryItemController>();
          return controller.totalItems.value.toString();
        }
      } else {
        if (Get.isRegistered<InventoryController>()) {
          final controller = Get.find<InventoryController>();
          return controller.totalItems.value.toString();
        }
      }
    } catch (e) {
      // Return default value if controller not available
    }
    return '0';
  }

  String _getCategoriesValue() {
    try {
      if (Get.isRegistered<InventoryItemController>()) {
        final controller = Get.find<InventoryItemController>();
        return isItemsView
            ? controller.categoryOptions.length.toString()
            : controller.inventoryTypeOptions.length.toString();
      }
    } catch (e) {
      // Return default value if controller not available
    }
    return '0';
  }

  String _getCurrentPageValue() {
    try {
      if (isItemsView) {
        if (Get.isRegistered<InventoryItemController>()) {
          final controller = Get.find<InventoryItemController>();
          return (controller.currentPage.value + 1).toString();
        }
      } else {
        if (Get.isRegistered<InventoryController>()) {
          final controller = Get.find<InventoryController>();
          return (controller.currentPage.value + 1).toString();
        }
      }
    } catch (e) {
      // Return default value if controller not available
    }
    return '1';
  }

  String _getTotalPagesValue() {
    try {
      if (isItemsView) {
        if (Get.isRegistered<InventoryItemController>()) {
          final controller = Get.find<InventoryItemController>();
          return controller.totalPages.value.toString();
        }
      } else {
        if (Get.isRegistered<InventoryController>()) {
          final controller = Get.find<InventoryController>();
          return controller.totalPages.value.toString();
        }
      }
    } catch (e) {
      // Return default value if controller not available
    }
    return '1';
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  value,
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
