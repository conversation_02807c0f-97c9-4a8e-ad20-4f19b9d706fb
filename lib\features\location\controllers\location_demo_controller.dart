import 'package:get/get.dart';
import '../../../core/app/services/location_service.dart';
import '../../../core/app/constants/enums.dart' as enums;

/// Controller demonstrating how to use LocationService in a real application
class LocationDemoController extends GetxController {
  // Get the LocationService instance
  late final LocationService _locationService;

  // Reactive variables for demo purposes
  final RxBool _isInitialized = false.obs;
  final RxString _statusMessage = ''.obs;
  final RxList<String> _locationHistory = <String>[].obs;

  // Getters
  bool get isInitialized => _isInitialized.value;
  String get statusMessage => _statusMessage.value;
  List<String> get locationHistory => _locationHistory;

  // Location service getters (for convenience)
  bool get hasLocation => _locationService.hasValidLocation;
  bool get isTracking => _locationService.isTracking;
  bool get hasPermission => _locationService.isPermissionGranted;
  String get currentLocation => _locationService.formattedCoordinates;
  String get locationName => _locationService.locationName;

  @override
  void onInit() {
    super.onInit();
    _initializeLocationService();
  }

  /// Initialize the location service and set up listeners
  Future<void> _initializeLocationService() async {
    try {
      _statusMessage.value = 'Initializing location service...';

      // Get the LocationService instance
      _locationService = Get.find<LocationService>();

      // Set up reactive listeners to track location changes
      _setupLocationListeners();

      _isInitialized.value = true;
      _statusMessage.value = 'Location service initialized successfully';

      // Add initial status to history
      _addToHistory('Location service initialized');
    } catch (e) {
      _statusMessage.value = 'Failed to initialize location service: $e';
      _addToHistory('Initialization failed: $e');
    }
  }

  /// Set up reactive listeners for location changes
  void _setupLocationListeners() {
    // Note: Since the LocationService reactive variables are private,
    // we'll use a different approach - periodic checking or manual updates
    // In a real implementation, you might want to expose reactive streams
    // from the LocationService for external listeners

    // For now, we'll just log the initial setup
    _addToHistory('Location listeners set up (manual monitoring)');

    // You could implement a timer to periodically check for changes
    // or modify the LocationService to expose public reactive streams
  }

  /// Request location permissions
  Future<void> requestPermissions() async {
    try {
      _statusMessage.value = 'Requesting location permissions...';
      _addToHistory('Requesting permissions...');

      final granted = await _locationService.requestPermissions();

      if (granted) {
        _statusMessage.value = 'Location permissions granted';
        _addToHistory('Permissions granted successfully');
      } else {
        _statusMessage.value = 'Location permissions denied';
        _addToHistory('Permissions denied');
      }
    } catch (e) {
      _statusMessage.value = 'Error requesting permissions: $e';
      _addToHistory('Permission request failed: $e');
    }
  }

  /// Start location tracking
  Future<void> startTracking() async {
    try {
      _statusMessage.value = 'Starting location tracking...';
      _addToHistory('Starting tracking...');

      final started = await _locationService.startTracking();

      if (started) {
        _statusMessage.value = 'Location tracking started';
        _addToHistory('Tracking started successfully');
      } else {
        _statusMessage.value = 'Failed to start location tracking';
        _addToHistory('Failed to start tracking');
      }
    } catch (e) {
      _statusMessage.value = 'Error starting tracking: $e';
      _addToHistory('Tracking start failed: $e');
    }
  }

  /// Stop location tracking
  Future<void> stopTracking() async {
    try {
      _statusMessage.value = 'Stopping location tracking...';
      _addToHistory('Stopping tracking...');

      await _locationService.stopTracking();

      _statusMessage.value = 'Location tracking stopped';
      _addToHistory('Tracking stopped');
    } catch (e) {
      _statusMessage.value = 'Error stopping tracking: $e';
      _addToHistory('Tracking stop failed: $e');
    }
  }

  /// Get current location once
  Future<void> getCurrentLocation() async {
    try {
      _statusMessage.value = 'Getting current location...';
      _addToHistory('Getting current location...');

      final position = await _locationService.getCurrentLocation();

      if (position != null) {
        _statusMessage.value = 'Current location retrieved';
        _addToHistory(
          'Current location: ${_locationService.formattedCoordinates}',
        );
      } else {
        _statusMessage.value = 'Failed to get current location';
        _addToHistory('Failed to get current location');
      }
    } catch (e) {
      _statusMessage.value = 'Error getting location: $e';
      _addToHistory('Get location failed: $e');
    }
  }

  /// Update location settings to high accuracy
  Future<void> setHighAccuracy() async {
    try {
      _statusMessage.value = 'Updating location settings...';
      _addToHistory('Setting high accuracy...');

      await _locationService.updateSettings(
        accuracy: enums.LocationAccuracy.best,
        updateInterval: 5,
        distanceFilter: 5,
        enableBackgroundTracking: true,
      );

      _statusMessage.value = 'Location settings updated to high accuracy';
      _addToHistory('Settings updated to high accuracy');
    } catch (e) {
      _statusMessage.value = 'Error updating settings: $e';
      _addToHistory('Settings update failed: $e');
    }
  }

  /// Check if user is near a specific location (example: church)
  bool isNearChurch() {
    // Example church coordinates (replace with actual coordinates)
    const churchLat = 37.7749;
    const churchLng = -122.4194;
    const radiusInMeters = 100.0;

    return _locationService.isWithinRadius(
      churchLat,
      churchLng,
      radiusInMeters,
    );
  }

  /// Get distance to church
  double getDistanceToChurch() {
    // Example church coordinates (replace with actual coordinates)
    const churchLat = 37.7749;
    const churchLng = -122.4194;

    return _locationService.getDistanceTo(churchLat, churchLng);
  }

  /// Example method for attendance checking
  Future<bool> checkAttendanceLocation() async {
    try {
      // Ensure we have current location
      if (!hasLocation) {
        await getCurrentLocation();
      }

      // Check if user is near the church
      if (isNearChurch()) {
        _addToHistory('User is at church location - attendance can be marked');
        return true;
      } else {
        final distance = getDistanceToChurch();
        _addToHistory(
          'User is ${distance.toStringAsFixed(0)}m away from church',
        );
        return false;
      }
    } catch (e) {
      _addToHistory('Attendance location check failed: $e');
      return false;
    }
  }

  /// Example method for event location verification
  Future<bool> verifyEventLocation(
    double eventLat,
    double eventLng,
    double radiusMeters,
  ) async {
    try {
      // Ensure we have current location
      if (!hasLocation) {
        await getCurrentLocation();
      }

      // Check if user is within the event radius
      final isWithinRadius = _locationService.isWithinRadius(
        eventLat,
        eventLng,
        radiusMeters,
      );
      final distance = _locationService.getDistanceTo(eventLat, eventLng);

      if (isWithinRadius) {
        _addToHistory('User is at event location');
      } else {
        _addToHistory(
          'User is ${distance.toStringAsFixed(0)}m away from event',
        );
      }

      return isWithinRadius;
    } catch (e) {
      _addToHistory('Event location verification failed: $e');
      return false;
    }
  }

  /// Open location settings
  Future<void> openLocationSettings() async {
    try {
      _addToHistory('Opening location settings...');
      final opened = await _locationService.openLocationSettings();

      if (opened) {
        _addToHistory('Location settings opened');
      } else {
        _addToHistory('Failed to open location settings');
      }
    } catch (e) {
      _addToHistory('Error opening settings: $e');
    }
  }

  /// Clear location history
  void clearHistory() {
    _locationHistory.clear();
    _addToHistory('History cleared');
  }

  /// Add entry to location history
  void _addToHistory(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    _locationHistory.insert(0, '[$timestamp] $message');

    // Keep only last 50 entries
    if (_locationHistory.length > 50) {
      _locationHistory.removeRange(50, _locationHistory.length);
    }
  }

  /// Get location service status summary
  Map<String, dynamic> getStatusSummary() {
    return {
      'hasPermission': hasPermission,
      'isServiceEnabled': _locationService.isServiceEnabled,
      'isTracking': isTracking,
      'hasValidLocation': hasLocation,
      'currentLocation': currentLocation,
      'locationName': locationName,
      'accuracy': _locationService.accuracy.displayName,
      'updateInterval': _locationService.updateInterval,
      'distanceFilter': _locationService.distanceFilter,
      'backgroundTracking': _locationService.enableBackgroundTracking,
    };
  }

  @override
  void onClose() {
    // Clean up if needed
    super.onClose();
  }
}
