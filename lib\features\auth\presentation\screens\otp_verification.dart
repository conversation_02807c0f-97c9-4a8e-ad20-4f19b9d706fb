import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:get_storage/get_storage.dart';
import '../../controllers/auth_controller.dart';
import '../../../../core/app/services/http_service.dart';
import '../../../../core/app/services/api_urls.dart';
import '../../../../core/app/utils/cache_keys.dart';
import '../../../../core/app/constants/routes.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';

class OtpVerificationScreen extends StatefulWidget {
  const OtpVerificationScreen({super.key});

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final AuthController _authController = Get.find<AuthController>();
  final _formKey = GlobalKey<FormState>();
  bool _isResendingOtp = false;
  int _resendSeconds = 300; // 5 minutes
  bool _canResend = false;

  // Create a local TextEditingController to avoid disposal issues
  late final TextEditingController _otpController;

  // Stream controller for error animations
  final StreamController<ErrorAnimationType> _errorController =
      StreamController<ErrorAnimationType>.broadcast();

  // Flag to determine if we're in password reset flow
  final RxBool _isPasswordReset = false.obs;

  @override
  void initState() {
    super.initState();
    // Initialize the local controller with the value from auth controller if available
    _otpController = TextEditingController(
      text: _authController.otpController.text,
    );
    _startResendTimer();

    // Check if we are in password reset flow by looking at routes
    _checkPasswordResetFlow();
  }

  void _checkPasswordResetFlow() {
    // Check if this is from a forgot password flow
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // final currentRoute = GoRouter.of(context);
      _isPasswordReset.value =
          ModalRoute.of(context)?.settings.name?.contains('forgot-password') ??
          false;
    });
  }

  @override
  void dispose() {
    _errorController.close();
    _otpController.dispose(); // Dispose the local controller
    // Clear auth controller's OTP field
    _authController.otpController.clear();
    super.dispose();
  }

  void _startResendTimer() {
    _canResend = false;
    _resendSeconds = 300; // 5 minutes

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _resendSeconds--;
        });

        if (_resendSeconds > 0) {
          _startResendTimer();
        } else {
          setState(() {
            _canResend = true;
          });
        }
      }
    });
  }

  Future<void> _resendOtp() async {
    if (!_canResend) return;

    setState(() {
      _isResendingOtp = true;
    });

    try {
      await _authController.sendOtp();
      _startResendTimer();

      // Clear OTP field
      _otpController.clear();
    } catch (e) {
      // Error is handled in the controller
    } finally {
      setState(() {
        _isResendingOtp = false;
      });
    }
  }

  Future<bool> _verifyOtpWithCustomPayload(BuildContext context) async {
    _authController.isLoading.value = true;
    _authController.errorMessage.value = '';

    try {
      // Create a new instance of HttpService
      final httpService = HttpService();
      httpService.initializeDio();

      // Different params based on whether we're in password reset flow
      final Map<String, dynamic> params =
          _isPasswordReset.value
              ? {
                'phone_number': _authController.phoneNumber,
                'checkout_id': _authController.checkoutId.value,
                'code': int.parse(_otpController.text),
              }
              : {
                'phone_number': _authController.phoneNumber,
                'user_id': _authController.userID,
                'otp': int.parse(_otpController.text),
                'checkout_id': _authController.checkoutId.value,
              };

      final response = await httpService.request(
        url: ApiUrls.confirmOtp,
        method: Method.POST,
        params: params,
      );

      if (response.data["status"]) {
        final userData = response.data["data"]["user"];
        Get.find<GetStorage>().write(CacheKeys.user, userData);

        // Navigate based on flow
        if (_isPasswordReset.value) {
          // In password reset flow, go to set new password
          context.go(Routes.SET_PIN, extra: {'isReset': true});
        } else {
          // In regular flow, go to set PIN
          context.go(Routes.SET_PIN);
        }
        return true;
      } else {
        _authController.errorMessage.value =
            response.data["message"] ?? 'Invalid OTP';
        return false;
      }
    } catch (e) {
      _authController.logger.e(e);
      _authController.errorMessage.value =
          'OTP verification failed: ${e.toString()}';
      return false;
    } finally {
      _authController.isLoading.value = false;
    }
  }

  void _navigateBack() {
    if (_isPasswordReset.value) {
      context.go(Routes.FORGOT_PASSWORD);
    } else {
      context.go(Routes.SIGNUP);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: _navigateBack,
          icon: Icon(IconlyLight.arrowLeft, color: colorScheme.onSurface),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isPasswordReset.value
                      ? 'Verify to Reset Password'
                      : 'Verify Your Phone',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'We\'ve sent a verification code to ${_authController.phoneController.text.isNotEmpty ? '${_authController.countryCodeController.text} ${_authController.phoneController.text}' : 'your phone'}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                const SizedBox(height: 40),

                // OTP Input using PinCodeTextField
                PinCodeTextField(
                  appContext: context,
                  length: 5,
                  controller: _otpController, // Use the local controller
                  errorAnimationController: _errorController,
                  animationType: AnimationType.fade,
                  pinTheme: PinTheme(
                    shape: PinCodeFieldShape.box,
                    borderRadius: BorderRadius.circular(12),
                    fieldHeight: 56,
                    fieldWidth: 48,
                    activeFillColor: colorScheme.surfaceContainerHighest
                        .withValues(alpha: 0.3),
                    inactiveFillColor: colorScheme.surfaceContainerHighest
                        .withValues(alpha: 0.3),
                    selectedFillColor: colorScheme.surfaceContainerHighest
                        .withValues(alpha: 0.5),
                    activeColor: colorScheme.outline.withValues(alpha: 0.3),
                    inactiveColor: colorScheme.outline.withValues(alpha: 0.3),
                    selectedColor: colorScheme.primary,
                  ),
                  cursorColor: colorScheme.primary,
                  animationDuration: const Duration(milliseconds: 300),
                  enableActiveFill: true,
                  keyboardType: TextInputType.number,
                  onCompleted: (value) {
                    // Ready to verify when all digits are filled
                  },
                  onChanged: (value) {
                    // No need to handle individual focus changes as the widget manages it
                  },
                ),

                const SizedBox(height: 24),

                // Resend OTP
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Didn\'t receive a code?',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                    const SizedBox(width: 8),
                    _canResend
                        ? TextButton(
                          onPressed: _isResendingOtp ? null : _resendOtp,
                          child:
                              _isResendingOtp
                                  ? SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircleLoadingAnimation(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        colorScheme.primary,
                                      ),
                                    ),
                                  )
                                  : Text(
                                    'Resend',
                                    style: TextStyle(
                                      color: colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                        )
                        : Text(
                          'Resend in ${_resendSeconds ~/ 60}:${(_resendSeconds % 60).toString().padLeft(2, '0')}',
                          style: TextStyle(
                            color: colorScheme.primary.withValues(alpha: 0.5),
                          ),
                        ),
                  ],
                ),

                const SizedBox(height: 40),

                // Error message
                Obx(
                  () =>
                      _authController.errorMessage.isNotEmpty
                          ? Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 12,
                              horizontal: 16,
                            ),
                            margin: const EdgeInsets.only(bottom: 24),
                            decoration: BoxDecoration(
                              color: colorScheme.error.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: colorScheme.error.withValues(alpha: 0.5),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  color: colorScheme.error,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    _authController.errorMessage.value,
                                    style: TextStyle(color: colorScheme.error),
                                  ),
                                ),
                              ],
                            ),
                          )
                          : const SizedBox.shrink(),
                ),

                // Verify button
                SizedBox(
                  width: double.infinity,
                  child: Obx(
                    () => CustomButton(
                      isLoading: _authController.isLoading.value,
                      onPressed:
                          _authController.isLoading.value
                              ? null
                              : () {
                                if (_formKey.currentState!.validate() &&
                                    _otpController.text.length == 5) {
                                  _verifyOtpWithCustomPayload(context);
                                } else if (_otpController.text.length < 5) {
                                  _errorController.add(
                                    ErrorAnimationType.shake,
                                  );
                                }
                              },
                      icon: Icon(IconlyLight.tickSquare, size: 20),
                      label: Text(
                        'Verify',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
