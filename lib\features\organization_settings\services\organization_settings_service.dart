import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../core/app/services/http_service.dart';

class OrganizationSettingsService {
  final HttpService _httpService = Get.find();
  final Logger logger = Get.find<Logger>();

  // Initialize the service
  OrganizationSettingsService() {
    _httpService.initializeDio();
  }

  /// Fetch organization details
  Future<Map<String, dynamic>> getOrganization() async {
    try {
      logger.i('Fetching organization details');

      final response = await _httpService.request(
        url: ApiUrls.getOrganisation,
        method: Method.GET,
      );

      logger.i('Organization details fetched successfully');
      return response.data;
    } catch (e) {
      logger.e('Error fetching organization details: $e');
      return {
        'status': false,
        'message': 'Failed to fetch organization details: ${e.toString()}',
        'data': null,
        'errors': e.toString(),
      };
    }
  }

  /// Update organization details
  Future<Map<String, dynamic>> updateOrganization(
    Map<String, dynamic> organizationData,
  ) async {
    try {
      logger.i('Updating organization details with data: $organizationData');

      final response = await _httpService.request(
        url: ApiUrls.updateOrganisation,
        method: Method.PUT,
        params: organizationData,
      );

      logger.i('Organization details updated successfully');
      return response.data;
    } catch (e) {
      logger.e('Error updating organization details: $e');
      return {
        'status': false,
        'message': 'Failed to update organization details: ${e.toString()}',
        'data': null,
        'errors': e.toString(),
      };
    }
  }

  /// Get organization accounts
  Future<Map<String, dynamic>> getOrganizationAccounts({
    int page = 0,
    int size = 10,
  }) async {
    try {
      logger.i('Fetching organization accounts');

      final response = await _httpService.request(
        url: "${ApiUrls.getOrganisationAccounts}?page=$page&size=$size",
        method: Method.GET,
      );

      logger.i('Organization accounts fetched successfully');
      return response.data;
    } catch (e) {
      logger.e('Error fetching organization accounts: $e');
      return {
        'status': false,
        'message': 'Failed to fetch organization accounts: ${e.toString()}',
        'data': null,
        'errors': e.toString(),
      };
    }
  }
}
