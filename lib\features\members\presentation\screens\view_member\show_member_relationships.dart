import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/utils/screen_breakpoints.dart';
import 'package:onechurch/features/members/bindings/relationship_binding.dart';
import 'package:onechurch/features/members/controllers/relationship_controller.dart';
import 'package:onechurch/features/members/presentation/screens/view_member/widgets/section_header.dart';
import 'package:onechurch/features/members/presentation/screens/member_relationships/view_member_relations_screen.dart';

class ShowMemberRelationships extends StatelessWidget {
  final String memberId;
  const ShowMemberRelationships({super.key, required this.memberId});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < ScreenBreakpoints.mobile;
    final isTablet =
        screenWidth >= ScreenBreakpoints.mobile &&
        screenWidth < ScreenBreakpoints.tablet;
    final isDesktop = screenWidth >= ScreenBreakpoints.tablet;

    // Initialize relationship controller if needed
    if (!Get.isRegistered<RelationshipController>()) {
      RelationshipBinding().dependencies();
    }

    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 16.h, bottom: 16.h),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Theme.of(context).dividerColor.withOpacity(0.1),
          width: 1.r,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(
              isDesktop ? 24.r : (isMobile ? 16.r : 20.r),
            ),
            child: SectionHeader(title: 'Member Relationships'),
          ),
          SizedBox(
            height: isDesktop ? 600.h : (isTablet ? 500.h : 400.h),
            child: ViewMemberRelationsScreen(memberId: memberId),
          ),
        ],
      ),
    );
  }
}
