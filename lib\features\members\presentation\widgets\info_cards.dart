import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:onechurch/core/app/utils/screen_breakpoints.dart';
import 'package:onechurch/data/models/member_model.dart';

class InfoCards extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final VoidCallback? action;

  const InfoCards({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < ScreenBreakpoints.mobile;
    final isTablet =
        screenWidth >= ScreenBreakpoints.mobile &&
        screenWidth < ScreenBreakpoints.tablet;
    final isDesktop = screenWidth >= ScreenBreakpoints.tablet;

    // Adjust padding and elevation based on screen size
    final padding = isMobile ? 10.r : (isTablet ? 12.r : 16.r);
    final elevation = isMobile ? 0.5 : (isDesktop ? 2.0 : 1.0);
    final iconSize = isMobile ? 20.0 : (isDesktop ? 24.0 : 22.0);

    return Card(
      elevation: elevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(isMobile ? 6.r : 8.r),
        side: BorderSide(
          color: theme.colorScheme.outline.withOpacity(isDesktop ? 0.1 : 0.2),
          width: isDesktop ? 1.0 : 0.5,
        ),
      ),
      child: InkWell(
        onTap: action,
        borderRadius: BorderRadius.circular(isMobile ? 6.r : 8.r),
        child: Padding(
          padding: EdgeInsets.all(padding),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(isDesktop ? 8.r : 6.r),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(isDesktop ? 8.r : 6.r),
                ),
                child: Icon(
                  icon,
                  color: theme.colorScheme.primary,
                  size: iconSize,
                ),
              ),
              Gap(12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Gap(2.h),
                    Text(
                      value,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: isMobile ? 14 : (isDesktop ? 16 : 15),
                      ),
                    ),
                  ],
                ),
              ),
              if (action != null)
                Container(
                  padding: EdgeInsets.all(isDesktop ? 6.r : 4.r),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(isDesktop ? 8.r : 6.r),
                  ),
                  child: Icon(
                    IconlyLight.arrowRight,
                    color: theme.colorScheme.primary.withOpacity(0.7),
                    size: isMobile ? 14 : 16,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class MemberInfoCard extends StatelessWidget {
  final MemberModel member;
  const MemberInfoCard({super.key, required this.member});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isMobile =
        MediaQuery.of(context).size.width < ScreenBreakpoints.mobile;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: isMobile ? 20.r : 24.r,
                  backgroundColor: colorScheme.primary.withOpacity(0.2),
                  backgroundImage:
                      member.profileUrl != null
                          ? NetworkImage(member.profileUrl!)
                          : null,
                  child:
                      member.profileUrl == null
                          ? Icon(
                            IconlyBold.profile,
                            color: colorScheme.primary,
                            size: isMobile ? 20.r : 24.r,
                          )
                          : null,
                ),
                Gap(16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${member.firstName ?? ''} ${member.secondName ?? ''}',
                        style: theme.textTheme.titleLarge,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Gap(4.h),
                      Row(
                        children: [
                          Icon(
                            IconlyLight.call,
                            size: 14,
                            color: colorScheme.primary,
                          ),
                          Gap(4.w),
                          Text(
                            member.phoneNumber ?? 'No phone number',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                      if (member.email != null && member.email!.isNotEmpty)
                        Padding(
                          padding: EdgeInsets.only(top: 4.h),
                          child: Row(
                            children: [
                              Icon(
                                IconlyLight.message,
                                size: 14,
                                color: colorScheme.primary,
                              ),
                              Gap(4.w),
                              Text(
                                member.email!,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: colorScheme.onSurface.withOpacity(0.7),
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
