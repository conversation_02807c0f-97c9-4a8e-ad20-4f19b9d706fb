import 'package:go_router/go_router.dart';

import '../../core/app/constants/routes.dart';
import 'bindings/attendance_binding.dart';
import 'presentation/screens/attendance_screen.dart';
import 'presentation/screens/mark_attendance_screen.dart';
import 'presentation/screens/view_attendance_screen.dart';

class AttendanceRoutes {
  static List<RouteBase> routes = [
    GoRoute(
      path: Routes.ATTENDANCE,
      builder: (context, state) {
        // Initialize bindings
        AttendanceBinding().dependencies();
        return const AttendanceScreen();
      },
    ),
    GoRoute(
      path: '${Routes.ATTENDANCE}/mark',
      builder: (context, state) {
        // Initialize bindings
        AttendanceBinding().dependencies();
        return const MarkAttendanceScreen();
      },
    ),
    GoRoute(
      path: '${Routes.ATTENDANCE}/:id',
      builder: (context, state) {
        // Initialize bindings
        AttendanceBinding().dependencies();
        final attendanceId = state.pathParameters['id'] ?? '';
        return ViewAttendanceScreen(attendanceId: attendanceId);
      },
    ),
    GoRoute(
      path: '${Routes.ATTENDANCE}/:id/mark',
      builder: (context, state) {
        // Initialize bindings
        AttendanceBinding().dependencies();
        final attendanceId = state.pathParameters['id'];
        return MarkAttendanceScreen(attendanceId: attendanceId);
      },
    ),
  ];
}
