import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import '../../controllers/sms_controller.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';

class SmsFilterWidget extends StatelessWidget {
  const SmsFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SmsController>();
    final screenWidth = MediaQuery.of(context).size.width;
    final colorScheme = Theme.of(context).colorScheme;

    Widget filterContent = Obx(
      () => Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
                left: 16,
                right: 16,
                top: 16,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(
                            IconlyLight.filter,
                            color: colorScheme.primary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Filter SMS Messages',
                            style: Theme.of(
                              context,
                            ).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.grey.shade100,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  // Filter Controls
                  _buildFilterControls(controller, context),
                  const SizedBox(height: 24),
                  // Action Buttons
                  _buildActionButtons(controller, context),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
          if (controller.isLoading.value)
            Positioned.fill(
              child: Container(
                color: Colors.black26,
                child: const Center(child: CircleLoadingAnimation()),
              ),
            ),
        ],
      ),
    );

    // For larger screens, show in dialog
    if (screenWidth > 500) {
      return Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(16),
          child: filterContent,
        ),
      );
    }

    // For smaller screens, return the content directly for bottom sheet
    return filterContent;
  }

  Widget _buildFilterControls(SmsController controller, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search/Message Filter
        CustomTextFormField(
          controller: controller.messageFilterController,
          labelText: 'Search Messages',
          hintText: 'Filter by message content...',
          prefixIcon: const Icon(IconlyLight.search),
          onChanged: (value) => controller.messageFilter.value = value,
        ),
        const SizedBox(height: 16),

        // Phone Number Filter
        CustomTextFormField(
          controller: controller.phoneNumberController,
          labelText: 'Phone Number',
          hintText: 'Enter phone number to filter...',
          prefixIcon: const Icon(IconlyLight.call),
          onChanged: (value) => controller.phoneNumberFilter.value = value,
        ),
        const SizedBox(height: 16),

        // Date Range Section
        Text(
          'Date Range',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildDateTimeField(
                context: context,
                controller: controller,
                isStartDate: true,
                label: 'Start Date & Time',
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDateTimeField(
                context: context,
                controller: controller,
                isStartDate: false,
                label: 'End Date & Time',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateTimeField({
    required BuildContext context,
    required SmsController controller,
    required bool isStartDate,
    required String label,
  }) {
    return Obx(() {
      final date =
          isStartDate ? controller.startDate.value : controller.endDate.value;

      return InkWell(
        onTap:
            controller.isLoading.value
                ? null
                : () => _selectDateTime(context, controller, isStartDate),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  controller.isLoading.value
                      ? Colors.grey.shade300
                      : Theme.of(context).primaryColor.withOpacity(0.3),
            ),
            borderRadius: BorderRadius.circular(12),
            color:
                controller.isLoading.value
                    ? Colors.grey.shade50
                    : Theme.of(context).primaryColor.withOpacity(0.05),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      date != null
                          ? DateFormat('dd MMM yyyy HH:mm').format(date)
                          : 'Select $label',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color:
                            date != null
                                ? Colors.black87
                                : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                IconlyLight.calendar,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildActionButtons(SmsController controller, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton.icon(
          onPressed:
              controller.isLoading.value
                  ? null
                  : () {
                    controller.clearFilters();
                    Navigator.pop(context);
                  },
          icon: const Icon(Icons.clear_all),
          label: const Text('Clear'),
          style: TextButton.styleFrom(
            foregroundColor: Colors.red,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
        const SizedBox(width: 16),
        CustomButton(
          onPressed:
              controller.isLoading.value
                  ? null
                  : () {
                    controller.fetchMessages();
                    Navigator.pop(context);
                  },
          isLoading: controller.isLoading.value,
          icon: const Icon(IconlyLight.filter),
          label: Text(controller.isLoading.value ? 'Applying...' : 'Apply'),
        ),
      ],
    );
  }

  Future<void> _selectDateTime(
    BuildContext context,
    SmsController controller,
    bool isStartDate,
  ) async {
    final currentDate =
        isStartDate
            ? controller.startDate.value ?? DateTime.now()
            : controller.endDate.value ?? DateTime.now();

    final date = await showDatePicker(
      context: context,
      initialDate: currentDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (date != null) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(currentDate),
      );

      if (time != null) {
        final selectedDateTime = DateTime(
          date.year,
          date.month,
          date.day,
          time.hour,
          time.minute,
        );

        if (isStartDate) {
          controller.startDate.value = selectedDateTime;
        } else {
          controller.endDate.value = selectedDateTime;
        }
      }
    }
  }
}
