{"buildFiles": ["C:\\Users\\<USER>\\Documents\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\Documents\\sdks\\android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\onechurch v3\\john316-flutter-front\\android\\app\\.cxx\\RelWithDebInfo\\i6r1s2g2\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\Documents\\sdks\\android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\onechurch v3\\john316-flutter-front\\android\\app\\.cxx\\RelWithDebInfo\\i6r1s2g2\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}