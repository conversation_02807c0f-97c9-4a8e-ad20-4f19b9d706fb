import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/app/constants/routes.dart';
import '../../controllers/inventory_controller.dart';
import '../widgets/inventory_filter_widget.dart';
import '../widgets/inventory_records_table_widget.dart';

class InventoryRecordsScreen extends StatelessWidget {
  const InventoryRecordsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize the inventory controller
    final controller = Get.put(InventoryController());
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.background,
      appBar: AppBar(
        title: const Text('Inventory Records'),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () => controller.fetchInventoryRecords(),
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // Filter Section
          Container(
            padding: EdgeInsets.all(16.w),
            child: InventoryFilterWidget(isItemsView: false),
          ),

          // Content Section
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              child: const InventoryRecordsTableWidget(),
            ),
          ),

          // Bottom spacing
          SizedBox(height: 16.h),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.go(Routes.RECORD_INVENTORY),
        icon: const Icon(IconlyLight.document, size: 20),
        label: const Text(
          'Record',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        backgroundColor: colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 4,
        extendedPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
        tooltip: 'Record Inventory',
      ),
    );
  }
}
