import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'bindings/staff_roles_binding.dart';
import 'presentation/screens/staff_roles_screen.dart';
import 'presentation/screens/create_staff_role_screen.dart';
import 'presentation/screens/staff_role_assignment_screen.dart';
import '../../core/app/constants/routes.dart';
import '../staff/models/staff_model.dart';
import 'models/staff_role_model.dart';

List<RouteBase> getStaffRolesGoRoutes() {
  return [
    GoRoute(
      path: Routes.STAFF_ROLES,
      builder: (context, state) {
        // Register the binding
        Get.put(StaffRolesBinding());
        return const StaffRolesScreen();
      },
    ),
    GoRoute(
      path: Routes.CREATE_STAFF_ROLE,
      builder: (context, state) {
        // Register the binding
        Get.put(StaffRolesBinding());
        return const CreateStaffRoleScreen();
      },
    ),
    GoRoute(
      path: Routes.EDIT_STAFF_ROLE,
      builder: (context, state) {
        // Register the binding
        Get.put(StaffRolesBinding());
        // Safely cast the extra data to StaffRoleModel
        final role =
            state.extra is StaffRoleModel
                ? state.extra as StaffRoleModel
                : null;
        return CreateStaffRoleScreen(role: role);
      },
    ),
    GoRoute(
      path: Routes.STAFF_ROLE_ASSIGNMENTS,
      builder: (context, state) {
        // Register the binding
        Get.put(StaffRolesBinding());
        // Safely cast the extra data to StaffModel
        final staff =
            state.extra is StaffModel ? state.extra as StaffModel : null;
        if (staff == null) {
          // Handle the case where staff is null
          return const Scaffold(
            body: Center(child: Text('Staff information not provided')),
          );
        }
        return StaffRoleAssignmentScreen(staff: staff);
      },
    ),
  ];
}
