import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/services/api_urls.dart';
import 'package:onechurch/core/app/services/http_service.dart';

class FinanceService {
  final HttpService _httpService = Get.find();
  final Logger logger = Get.find<Logger>();

  // Fetch sub-accounts with categories
  Future<Map<String, dynamic>> fetchSubAccounts({
    required int page,
    required int size,
    String? organisationId,
  }) async {
    try {
      final Map<String, dynamic> params = {'page': page, 'size': size};

      if (organisationId != null) {
        params['organisation_id'] = organisationId;
      }

      final response = await _httpService.request(
        url: ApiUrls.subAccounts,
        method: Method.GET,
        params: params,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching sub-accounts: $e');
      rethrow;
    }
  }

  // Fetch categories only
  Future<Map<String, dynamic>> fetchCategories({String? organisationId}) async {
    try {
      final Map<String, dynamic> params = {};

      if (organisationId != null) {
        params['organisation_id'] = organisationId;
      }

      final response = await _httpService.request(
        url: ApiUrls.subAccountsCategories,
        method: Method.GET,
        params: params,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching categories: $e');
      rethrow;
    }
  }

  // Create a new category
  Future<Map<String, dynamic>> createCategory({
    required String title,
    required String description,
    required String organisationId,
  }) async {
    try {
      final Map<String, dynamic> categoryData = {
        'title': title,
        'description': description,
        'organisation_id': organisationId,
      };

      logger.d('Creating category with payload: $categoryData');

      final response = await _httpService.request(
        url: ApiUrls.createSubAccountCategory,
        method: Method.POST,
        params: categoryData,
      );

      logger.d('Category creation response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error creating category: $e');
      rethrow;
    }
  }

  // Update an existing category
  Future<Map<String, dynamic>> updateCategory({
    required String categoryId,
    required String title,
    required String description,
  }) async {
    try {
      final Map<String, dynamic> categoryData = {
        'title': title,
        'description': description,
      };

      logger.d('Updating category $categoryId with payload: $categoryData');

      final response = await _httpService.request(
        url: '${ApiUrls.createSubAccountCategory}$categoryId/',
        method: Method.PUT,
        params: categoryData,
      );

      logger.d('Category update response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error updating category: $e');
      rethrow;
    }
  }

  // Create a new sub-account
  Future<Map<String, dynamic>> createSubAccount({
    required String title,
    required String description,
    required String categoryId,
    required String account,
    required String organisationId,
    bool isPinned = false,
  }) async {
    try {
      final Map<String, dynamic> subAccountData = {
        'title': title,
        'description': description,
        'category_id': categoryId,
        'account': account,
        'organisation_id': organisationId,
        'is_pinned': isPinned,
      };

      logger.d('Creating sub-account with payload: $subAccountData');

      final response = await _httpService.request(
        url: ApiUrls.subAcct,
        method: Method.POST,
        params: subAccountData,
      );

      logger.d('Sub-account creation response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error creating sub-account: $e');
      rethrow;
    }
  }
}
