import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/services/api_urls.dart';
import 'package:onechurch/core/app/services/http_service.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/data/models/sub_account_model.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import 'package:onechurch/features/finances/services/finance_service.dart';

class FinanceController extends GetxController {
  final logger = Get.find<Logger>();
  final FinanceService _financeService = FinanceService();

  // State variables
  RxBool isLoading = false.obs;
  RxBool isSubmitting = false.obs;
  RxString message = ''.obs;
  RxBool apiStatus = false.obs;
  RxInt currentPage = 0.obs;
  RxInt totalPages = 0.obs;
  RxInt totalItems = 0.obs;
  RxInt pageSize = 10.obs;
  RxBool isLastPage = false.obs;
  RxBool isFirstPage = true.obs;
  final HttpService _httpService = Get.find();

  // Form controllers for category creation
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();

  // Filter controllers and state
  final TextEditingController searchController = TextEditingController();
  Rx<DateTime?> startDate = Rx<DateTime?>(null);
  Rx<DateTime?> endDate = Rx<DateTime?>(null);
  RxString searchQuery = ''.obs;
  RxString selectedStatus = 'All'.obs;
  RxString selectedCategory = 'All'.obs;

  final RxList<SubAccount> _subAccountModel = RxList<SubAccount>([]);
  final RxList<SubAccountCategory> _subAccountCategories =
      RxList<SubAccountCategory>([]);
  List<SubAccountCategory> get categories => _subAccountCategories;
  List<SubAccount> get subaccounts => _subAccountModel;
  final AuthController _authController = Get.find();

  @override
  void onInit() {
    fetchCategories();
    super.onInit();
  }

  fetchAccounts() async {
    isLoading(true);
    update();
    try {
      var response = await _httpService.request(
        url: ApiUrls.subAccounts,
        method: Method.GET,
        params: {
          "page": currentPage.value,
          "size": pageSize.value,
          "organisation_id": _authController.currentOrg.value?.id,
          if (searchQuery.value.isNotEmpty) "search": searchQuery.value,
          if (startDate.value != null)
            "start_date": startDate.value!.toIso8601String(),
          if (endDate.value != null)
            "end_date": endDate.value!.toIso8601String(),
          if (selectedStatus.value != 'All') "status": selectedStatus.value,
          if (selectedCategory.value != 'All')
            "category": selectedCategory.value,
        },
      );
      if (response.data["status"]) {
        var data = response.data["data"];
        currentPage.value = data["page"] ?? 0;
        totalPages.value = data["total_pages"] ?? 0;
        totalItems.value = data["total"] ?? 0;
        isLastPage.value = data["last"] ?? false;
        isFirstPage.value = data["first"] ?? true;

        //mock the number of pages
        // totalPages.value = 10;

        if (data["accounts"]["items"] != null) {
          _subAccountModel.value =
              data["accounts"]["items"]
                  .map<SubAccount>((item) => SubAccount.fromJson(item))
                  .toList();
        }
        if (data["categories"] != null) {
          _subAccountCategories.clear();
          _subAccountCategories.value =
              data["categories"]
                  .map<SubAccountCategory>(
                    (item) => SubAccountCategory.fromJson(item),
                  )
                  .toList();
        }
      } else {
        message(response.data["message"] ?? "Failed to fetch accounts");
      }
      isLoading(false);
      logger.i("Fetched ${_subAccountModel.length} accounts from server");
    } catch (e) {
      isLoading(false);
      logger.e(e);
      message("Failed to fetch accounts: ${e.toString()}");
    }
    update();
  }

  fetchCategories() async {
    isLoading(true);
    update();
    try {
      var response = await _httpService.request(
        url: ApiUrls.subAccounts,
        method: Method.GET,
        params: {
          "page": 0,
          "size": 100, // Get a large number to fetch all categories
          "organisation_id": _authController.currentOrg.value?.id,
        },
      );
      if (response.data["status"]) {
        var data = response.data["data"];
        if (data != null && data["categories"] != null) {
          _subAccountCategories.clear();
          _subAccountCategories.value =
              (data["categories"] as List)
                  .map<SubAccountCategory>(
                    (item) => SubAccountCategory.fromJson(item),
                  )
                  .toList();
        }
      } else {
        message(response.data["message"] ?? "Failed to fetch categories");
      }
      isLoading(false);
      logger.i(
        "Fetched ${_subAccountCategories.length} categories from server",
      );
    } catch (e) {
      isLoading(false);
      logger.e(e);
      message("Failed to fetch categories: ${e.toString()}");
    }
    update();
  }

  createSubAccount({
    required String title,
    required String description,
    bool? isPinned,
    String? category,
    String? account,
  }) async {
    isLoading(true);
    update();
    try {
      var response = await _httpService.request(
        url: ApiUrls.subAcct,
        method: Method.POST,
        params: {
          "title": title,
          "description": description,
          "is_pinned": isPinned ?? false,
          "category_id": category,
          "account": account,
          "organisation_id": _authController.currentOrg.value?.id,
        },
      );

      apiStatus(response.data["status"]);
      if (response.data["status"]) {
        await fetchAccounts();
        message(response.data["message"]);
      } else {
        message(response.data["message"] ?? "Failed to create subaccount");
      }

      isLoading(false);
      update();
    } catch (e) {
      isLoading(false);
      logger.e(e);
      message("Failed to create subaccount");
    }
    update();
  }

  // Create a new category
  Future<bool> createCategory() async {
    try {
      isSubmitting.value = true;
      message.value = '';

      if (titleController.text.isEmpty || descriptionController.text.isEmpty) {
        message.value = 'Title and description are required';
        ToastUtils.showInfoToast('Error', 'Title and description are required');
        return false;
      }

      final response = await _financeService.createCategory(
        title: titleController.text,
        description: descriptionController.text,
        organisationId: _authController.currentOrg.value?.id ?? '',
      );

      if (response["status"] ?? false) {
        message.value = response["message"] ?? "Category created successfully";
        ToastUtils.showInfoToast(
          'Success',
          response["message"] ?? "Category created successfully",
        );

        // Clear form
        titleController.clear();
        descriptionController.clear();

        // Refresh categories
        await fetchCategories();

        return true;
      } else {
        message.value = response["message"] ?? "Failed to create category";
        ToastUtils.showInfoToast(
          'Error',
          response["message"] ?? "Failed to create category",
        );
        return false;
      }
    } catch (e) {
      logger.e('Error creating category: $e');
      message.value = "Failed to create category: ${e.toString()}";
      ToastUtils.showInfoToast("Error", "Failed to create category");
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Update an existing category
  Future<bool> updateCategory({
    required String categoryId,
    required String title,
    required String description,
  }) async {
    try {
      isSubmitting.value = true;
      message.value = '';

      if (title.isEmpty || description.isEmpty) {
        message.value = 'Title and description are required';
        ToastUtils.showInfoToast('Error', 'Title and description are required');
        return false;
      }

      final response = await _financeService.updateCategory(
        categoryId: categoryId,
        title: title,
        description: description,
      );

      if (response["status"] ?? false) {
        message.value = response["message"] ?? "Category updated successfully";
        ToastUtils.showInfoToast(
          'Success',
          response["message"] ?? "Category updated successfully",
        );

        // Clear form
        titleController.clear();
        descriptionController.clear();

        // Refresh categories
        await fetchCategories();

        return true;
      } else {
        message.value = response["message"] ?? "Failed to update category";
        ToastUtils.showInfoToast(
          'Error',
          response["message"] ?? "Failed to update category",
        );
        return false;
      }
    } catch (e) {
      logger.e('Error updating category: $e');
      message.value = "Failed to update category: ${e.toString()}";
      ToastUtils.showInfoToast("Error", "Failed to update category");
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Filter methods
  void setSearchQuery(String query) {
    searchQuery.value = query;
    searchController.text = query;
    currentPage.value = 0; // Reset to first page when filtering
    fetchAccounts();
  }

  void setDateFilters(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0; // Reset to first page when filtering
    fetchAccounts();
  }

  void setStatusFilter(String status) {
    selectedStatus.value = status;
    currentPage.value = 0; // Reset to first page when filtering
    fetchAccounts();
  }

  void setCategoryFilter(String category) {
    selectedCategory.value = category;
    currentPage.value = 0; // Reset to first page when filtering
    fetchAccounts();
  }

  void clearFilters() {
    searchQuery.value = '';
    searchController.clear();
    startDate.value = null;
    endDate.value = null;
    selectedStatus.value = 'All';
    selectedCategory.value = 'All';
    currentPage.value = 0;
    fetchAccounts();
  }

  // Clear form data
  void clearForm() {
    titleController.clear();
    descriptionController.clear();
    message.value = '';
  }

  @override
  void onClose() {
    titleController.dispose();
    descriptionController.dispose();
    searchController.dispose();
    super.onClose();
  }
}
