import 'dart:convert';

class MemberCategory {
  final String? id;
  final String? createdAt;
  final String? updatedAt;
  final String? deletedAt;
  final String? title;
  final String? description;
  final String? code;
  final String? organisationId;
  final bool? isGeneral;

  MemberCategory({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.description,
    this.code,
    this.organisationId,
    this.isGeneral,
  });

  MemberCategory copyWith({
    String? id,
    String? createdAt,
    String? updatedAt,
    String? deletedAt,
    String? title,
    String? description,
    String? code,
    String? organisationId,
    bool? isGeneral,
  }) =>
      MemberCategory(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        title: title ?? this.title,
        description: description ?? this.description,
        code: code ?? this.code,
        organisationId: organisationId ?? this.organisationId,
        isGeneral: isGeneral ?? this.isGeneral,
      );

  factory MemberCategory.fromRawJson(String str) => MemberCategory.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MemberCategory.fromJson(Map<String, dynamic> json) => MemberCategory(
        id: json["id"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        deletedAt: json["deleted_at"],
        title: json["title"],
        description: json["description"],
        code: json["code"],
        organisationId: json["organisation_id"],
        isGeneral: json["is_general"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "deleted_at": deletedAt,
        "title": title,
        "description": description,
        "code": code,
        "organisation_id": organisationId,
        "is_general": isGeneral,
      };
}

class MemberCategoryResponse {
  final bool? status;
  final String? message;
  final List<MemberCategory>? data;
  final dynamic errors;

  MemberCategoryResponse({
    this.status,
    this.message,
    this.data,
    this.errors,
  });

  factory MemberCategoryResponse.fromRawJson(String str) => MemberCategoryResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  

  factory MemberCategoryResponse.fromJson(Map<String, dynamic> json) => MemberCategoryResponse(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null ? null : List<MemberCategory>.from(json["data"]!.map((x) => MemberCategory.fromJson(x))),
        
        errors: json["errors"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        // "data": data?.toJson(),
        "errors": errors,
      };
}

class MemberCategoryData {
  final List<MemberCategory>? items;
  final int? page;
  final int? size;
  final int? maxPage;
  final int? totalPages;
  final int? total;
  final bool? last;
  final bool? first;
  final int? visible;

  MemberCategoryData({
    this.items,
    this.page,
    this.size,
    this.maxPage,
    this.totalPages,
    this.total,
    this.last,
    this.first,
    this.visible,
  });

  factory MemberCategoryData.fromRawJson(String str) => MemberCategoryData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MemberCategoryData.fromJson(Map<String, dynamic> json) => MemberCategoryData(
        items: json["items"] == null ? [] : List<MemberCategory>.from(json["items"]!.map((x) => MemberCategory.fromJson(x))),
        page: json["page"],
        size: json["size"],
        maxPage: json["max_page"],
        totalPages: json["total_pages"],
        total: json["total"],
        last: json["last"],
        first: json["first"],
        visible: json["visible"],
      );

  Map<String, dynamic> toJson() => {
        "items": items == null ? [] : List<dynamic>.from(items!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "max_page": maxPage,
        "total_pages": totalPages,
        "total": total,
        "last": last,
        "first": first,
        "visible": visible,
      };
}