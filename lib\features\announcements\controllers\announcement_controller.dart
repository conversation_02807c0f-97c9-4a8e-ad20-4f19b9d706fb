import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import 'package:onechurch/features/media_upload/models/media_model.dart';
import '../../../core/app/utils/show_toast.dart';
import '../../../data/models/announcement_model.dart';
import '../services/announcement_service.dart';

class AnnouncementController extends GetxController {
  final AnnouncementService _announcementService = AnnouncementService();
  final logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<AnnouncementModel> announcements = <AnnouncementModel>[].obs;
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalItems = 0.obs;
  final RxInt pageSize = 10.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;
  final RxString searchQuery = ''.obs;
  final TextEditingController searchController = TextEditingController();

  // Filter variables
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);
  final RxString statusFilter = ''.obs;
  final TextEditingController statusController = TextEditingController();
  final RxString titleFilter = ''.obs;
  final TextEditingController titleController = TextEditingController();

  // Form controllers for create/edit
  final TextEditingController titleFormController = TextEditingController();
  final TextEditingController descriptionFormController =
      TextEditingController();

  // New controllers for the updated payload structure
  final TextEditingController generalTitleController = TextEditingController();
  final TextEditingController categoryController = TextEditingController();
  final TextEditingController locationNameController = TextEditingController();
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController typeController = TextEditingController();

  // Reactive list of media items
  final RxList<MediaModel> mediaItems = <MediaModel>[].obs;

  // item-categories
  final itemCategories = <String>[].obs;

  // Add a media item
  void addMediaItem() {
    final title = mediaTitleController.text.trim();
    final url = mediaUrlController.text.trim();

    if (title.isNotEmpty && url.isNotEmpty) {
      mediaItems.add(MediaModel(title: title, mediaUrl: url));
      mediaTitleController.clear();
      mediaUrlController.clear();
    }
  }

  // Remove a media item
  void removeMediaItem(int index) {
    mediaItems.removeAt(index);
  }

  final TextEditingController mediaTitleController = TextEditingController();
  final TextEditingController mediaUrlController = TextEditingController();
  final RxString statusFormValue = 'active'.obs;
  final RxBool isSubmitting = false.obs;

  // Reactive variable for current announcement being edited
  final Rx<AnnouncementModel?> announcement = Rx<AnnouncementModel?>(null);

  // Multiple announcements functionality
  final RxList<Map<String, dynamic>> multipleAnnouncements =
      <Map<String, dynamic>>[].obs;
  final RxBool isSubmittingMultiple = false.obs;

  // Load announcement data into form for editing
  void loadAnnouncementForEditById(AnnouncementModel announcement) {
    titleFormController.text = announcement.title ?? '';
    descriptionFormController.text = announcement.description ?? '';
    statusFormValue.value = announcement.status ?? 'active';
    usernameController.text = announcement.username ?? '';
    categoryController.text = announcement.category ?? '';
    typeController.text = announcement.type ?? '';

    // Load location name from the first item if available
    if (announcement.items != null && announcement.items!.isNotEmpty) {
      locationNameController.text =
          announcement.items!.first.locationName ?? '';
    } else {
      locationNameController.clear();
    }

    // Clear and load media items
    mediaItems.clear();
    if (announcement.media != null && announcement.media!.isNotEmpty) {
      mediaItems.value =
          announcement.media!
              .map(
                (item) => MediaModel(
                  title: item.title,
                  mediaUrl: item.mediaUrl,
                  type: item.type,
                  sizeBytes: item.sizeBytes,
                  organisationId:
                      item.organisationId ?? announcement.organisationId,
                ),
              )
              .toList();
    }
  }

  // Update announcement
  Future<bool> updateAnnouncement(String announcementId) async {
    try {
      isSubmitting.value = true;

      // Convert media items to the required format
      final mediaList =
          mediaItems
              .map(
                (item) => {
                  'title': item.title,
                  'media_url': item.mediaUrl,
                  'type': item.type,
                  'size_bytes': item.sizeBytes,
                  'organisation_id': item.organisationId,
                },
              )
              .toList();

      // Call service to update announcement
      final response = await _announcementService.updateAnnouncement(
        id: announcementId,
        title: titleFormController.text,
        description: descriptionFormController.text,
        status: statusFormValue.value,
        media: mediaList,
      );

      if (response['status'] == true) {
        // Update local state
        final index = announcements.indexWhere((a) => a.id == announcementId);
        if (index != -1) {
          final updatedAnnouncement = AnnouncementModel(
            id: announcementId,
            title: titleFormController.text,
            description: descriptionFormController.text,
            status: statusFormValue.value,
            username:
                usernameController.text.isEmpty
                    ? null
                    : usernameController.text,
            category:
                categoryController.text.isEmpty
                    ? null
                    : categoryController.text,
            type: typeController.text.isEmpty ? null : typeController.text,
            media: mediaItems.toList(),
          );
          announcements[index] = updatedAnnouncement;
        }

        ToastUtils.showSuccessToast('Announcement updated successfully', null);
        return true;
      } else {
        ToastUtils.showErrorToast(
          response['message'] ?? 'Failed to update announcement',
          null,
        );
        return false;
      }
    } catch (e) {
      logger.e('Error updating announcement: $e');
      ToastUtils.showErrorToast('Failed to update announcement: $e', null);
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  @override
  void onInit() {
    super.onInit();
    fetchAnnouncements();
  }

  // Initialize announcement for editing
  void initEditAnnouncement(String announcementId) {
    // Reset form controllers
    titleFormController.clear();
    descriptionFormController.clear();
    mediaTitleController.clear();
    mediaUrlController.clear();
    usernameController.clear();
    categoryController.clear();
    typeController.clear();
    locationNameController.clear();

    // Reset reactive variables
    announcement.value = null;
    mediaItems.clear();
    statusFormValue.value = 'active';

    // Fetch announcement by ID
    getAnnouncementById(announcementId);
  }

  // Initialize announcement for editing with initial data
  void initEditAnnouncementWithData(
    String announcementId,
    AnnouncementModel? initialAnnouncement,
  ) {
    // Reset form controllers
    titleFormController.clear();
    descriptionFormController.clear();
    mediaTitleController.clear();
    mediaUrlController.clear();
    usernameController.clear();
    categoryController.clear();
    typeController.clear();
    locationNameController.clear();

    // Reset reactive variables
    announcement.value = null;
    mediaItems.clear();
    statusFormValue.value = 'active';

    if (initialAnnouncement != null) {
      // Load the initial announcement immediately
      announcement.value = initialAnnouncement;
      loadAnnouncementForEditById(initialAnnouncement);

      // Optionally fetch fresh data in the background
      _refreshAnnouncementForEdit(announcementId);
    } else {
      // If no initial announcement, fetch it
      getAnnouncementById(announcementId);
    }
  }

  // Background refresh for edit screen
  Future<void> _refreshAnnouncementForEdit(String announcementId) async {
    try {
      final response = await _announcementService.getAnnouncementById(
        announcementId,
      );

      if (response["status"] ?? false) {
        final data = response["data"];
        final freshAnnouncement = AnnouncementModel.fromJson(data);

        // Update the announcement and form data
        announcement.value = freshAnnouncement;
        loadAnnouncementForEditById(freshAnnouncement);
      }
    } catch (e) {
      // Silently fail for background refresh
      logger.d('Background refresh failed for edit: ${e.toString()}');
    }
  }

  @override
  void onClose() {
    searchController.dispose();
    statusController.dispose();
    titleController.dispose();
    titleFormController.dispose();
    descriptionFormController.dispose();
    generalTitleController.dispose();
    categoryController.dispose();
    locationNameController.dispose();
    usernameController.dispose();
    typeController.dispose();
    mediaTitleController.dispose();
    mediaUrlController.dispose();
    super.onClose();
  }

  // Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    currentPage.value = 0; // Reset to first page when searching
    fetchAnnouncements();
  }

  // Set date filters
  void setDateFilters(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0; // Reset to first page when filtering
    fetchAnnouncements();
  }

  // Set status filter
  void setStatusFilter(String status) {
    statusFilter.value = status;
    currentPage.value = 0; // Reset to first page when filtering
    fetchAnnouncements();
  }

  // Set title filter
  void setTitleFilter(String title) {
    titleFilter.value = title;
    currentPage.value = 0; // Reset to first page when filtering
    fetchAnnouncements();
  }

  // Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    searchController.clear();
    startDate.value = null;
    endDate.value = null;
    statusFilter.value = '';
    statusController.clear();
    titleFilter.value = '';
    titleController.clear();
    currentPage.value = 0;
    fetchAnnouncements();
  }

  // Navigate to next page
  void nextPage() {
    if (!isLastPage.value) {
      currentPage.value++;
      fetchAnnouncements();
    }
  }

  // Navigate to previous page
  void previousPage() {
    if (currentPage.value > 0) {
      currentPage.value--;
      fetchAnnouncements();
    }
  }

  // Fetch announcements with current filters
  Future<void> fetchAnnouncements() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final response = await _announcementService.fetchAnnouncements(
        page: currentPage.value,
        size: pageSize.value,
        searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
        startDate: startDate.value,
        endDate: endDate.value,
        status: statusFilter.value.isEmpty ? null : statusFilter.value,
      );

      if (response['status'] == true && response['data'] != null) {
        final data = response['data'] as Map<String, dynamic>;

        if (data['data'] != null) {
          final paginationData = data['data'] as Map<String, dynamic>;

          if (paginationData['items'] != null) {
            final items = paginationData['items'] as List<dynamic>;
            announcements.value = _announcementService.parseAnnouncements(
              items,
            );

            // Set categories if available
            if (data['item_categories'] != null) {
              itemCategories.value = List<String>.from(data['item_categories']);
            }

            // Update pagination metadata
            totalPages.value = paginationData['total_pages'] as int? ?? 0;
            totalItems.value = paginationData['total'] as int? ?? 0;
            isLastPage.value = paginationData['last'] as bool? ?? false;
            isFirstPage.value = paginationData['first'] as bool? ?? true;

            logger.d('Fetched ${items.length} announcements');
          } else {
            announcements.clear();
            logger.w('No items found in response');
          }
        } else {
          announcements.clear();
          logger.w('Invalid data structure in response');
        }
      } else {
        announcements.clear();
        logger.w('Invalid response format or status false');
      }
    } catch (e) {
      errorMessage.value = 'Failed to fetch announcements: ${e.toString()}';
      logger.e('Error in fetchAnnouncements: $e');
      announcements.clear();
    } finally {
      isLoading.value = false;
    }
  }

  // Refresh announcements
  Future<void> refreshAnnouncements() async {
    currentPage.value = 0;
    await fetchAnnouncements();
  }

  // Get announcement by ID
  Future<AnnouncementModel?> getAnnouncementById(String id) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final response = await _announcementService.getAnnouncementById(id);

      if (response["status"] ?? false) {
        final data = response["data"];
        return AnnouncementModel.fromJson(data);
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to fetch announcement';
        return null;
      }
    } catch (e) {
      errorMessage.value = 'Failed to fetch announcement: ${e.toString()}';
      logger.e('Error in getAnnouncementById: $e');
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // Delete announcement
  Future<bool> deleteAnnouncement(String id) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      await _announcementService.deleteAnnouncement(id);

      ToastUtils.showSuccessToast('Announcement deleted successfully', null);
      refreshAnnouncements();
      return true;
    } catch (e) {
      errorMessage.value = 'Failed to delete announcement: ${e.toString()}';
      logger.e('Error in deleteAnnouncement: $e');
      ToastUtils.showErrorToast(errorMessage.value, null);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Clear form fields (but keep general title)
  void clearFormFields() {
    titleFormController.clear();
    descriptionFormController.clear();
    categoryController.clear();
    locationNameController.clear();
    usernameController.clear();
    typeController.clear();
    statusFormValue.value = 'active';
    mediaItems.clear();
    mediaTitleController.clear();
    mediaUrlController.clear();
  }

  // Clear all fields including general title
  void clearAllFields() {
    clearFormFields();
    generalTitleController.clear();
  }

  // Multiple announcements methods
  void addAnnouncementToList({
    required String title,
    required String description,
    String? category,
    String? locationName,
    String status = 'active',
    List<MediaModel>? media,
  }) {
    final announcementItemData = <String, dynamic>{
      'organisation_id': Get.find<AuthController>().currentOrg.value?.id,
      'title': title,
      'description': description,
    };

    if (category != null && category.isNotEmpty) {
      announcementItemData['category'] = category;
    }

    if (locationName != null && locationName.isNotEmpty) {
      announcementItemData['location_name'] = locationName;
    }

    if (media != null && media.isNotEmpty) {
      announcementItemData['media'] =
          media
              .map(
                (item) => {
                  'id': item.id ?? '',
                  'media_url': item.mediaUrl,
                  'type': item.type ?? 'image',
                  'category': 'ANNOUNCEMENT',
                  'organisation_id':
                      Get.find<AuthController>().currentOrg.value?.id,
                },
              )
              .toList();
    }

    multipleAnnouncements.add(announcementItemData);
  }

  void removeAnnouncementFromList(int index) {
    if (index >= 0 && index < multipleAnnouncements.length) {
      multipleAnnouncements.removeAt(index);
    }
  }

  void clearMultipleAnnouncements() {
    multipleAnnouncements.clear();
  }

  // Edit an announcement item
  void editAnnouncementItem(int index) {
    if (index >= 0 && index < multipleAnnouncements.length) {
      final item = multipleAnnouncements[index];

      // Load the item data into form controllers
      titleFormController.text = item['title'] ?? '';
      categoryController.text = item['category'] ?? '';
      locationNameController.text = item['location_name'] ?? '';

      // Load media items if they exist
      mediaItems.clear();
      if (item['media'] != null && item['media'] is List) {
        final mediaList = item['media'] as List;
        for (final mediaItem in mediaList) {
          mediaItems.add(
            MediaModel(
              id: mediaItem['id'],
              mediaUrl: mediaItem['media_url'],
              type: mediaItem['type'],
              title: mediaItem['title'],
            ),
          );
        }
      }

      // Store the description for the UI to handle
      // The UI will need to set this in the markup editor
      if (item['description'] != null) {
        // We'll use a reactive variable to communicate with the UI
        editingDescription.value = item['description'];
      } else {
        editingDescription.value = '';
      }

      // Remove the item from the list so it can be re-added after editing
      removeAnnouncementFromList(index);
    }
  }

  // Add a reactive variable for editing description
  final RxString editingDescription = ''.obs;

  // Create multiple announcements
  Future<bool> createMultipleAnnouncements() async {
    try {
      isSubmittingMultiple.value = true;
      errorMessage.value = '';

      if (multipleAnnouncements.isEmpty) {
        errorMessage.value = 'No announcements to create';
        ToastUtils.showErrorToast('No announcements to create', null);
        return false;
      }

      if (generalTitleController.text.trim().isEmpty) {
        errorMessage.value = 'General title is required';
        ToastUtils.showErrorToast('General title is required', null);
        return false;
      }

      // Structure the payload according to the new format
      final payload = [
        {
          'title': generalTitleController.text.trim(),
          'status': 'active',
          'organisation_id': Get.find<AuthController>().currentOrg.value?.id,
          'items': multipleAnnouncements.toList(),
        },
      ];

      final response = await _announcementService.createMultipleAnnouncements(
        payload,
      );

      if (response['status'] == true) {
        ToastUtils.showSuccessToast(
          '${multipleAnnouncements.length} announcements created successfully',
          null,
        );
        clearMultipleAnnouncements();
        clearAllFields();
        await refreshAnnouncements();
        return true;
      } else {
        ToastUtils.showErrorToast(
          response['message'] ?? 'Failed to create announcements',
          null,
        );
        return false;
      }
    } catch (e) {
      errorMessage.value = 'Failed to create announcements: ${e.toString()}';
      logger.e('Error in createMultipleAnnouncements: $e');
      ToastUtils.showErrorToast(errorMessage.value, null);
      return false;
    } finally {
      isSubmittingMultiple.value = false;
    }
  }
}
