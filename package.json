{"name": "onechurch-web-dev", "version": "1.0.0", "description": "Development server for OneChurch Flutter web app", "main": "web_server.js", "scripts": {"build": "flutter build web --web-renderer html", "start": "node web_server.js", "dev": "flutter build web --web-renderer html && node web_server.js", "deploy": "flutter build web --web-renderer html --release && firebase deploy --only hosting"}, "dependencies": {"express": "^4.18.2"}}