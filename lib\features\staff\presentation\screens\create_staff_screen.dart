import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/constants/enums.dart';
import 'package:onechurch/core/app/constants/routes.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/core/widgets/custom_phone_input.dart';
import '../../controllers/staff_controller.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';

class CreateStaffScreen extends StatefulWidget {
  const CreateStaffScreen({super.key});

  @override
  State<CreateStaffScreen> createState() => _CreateStaffScreenState();
}

class _CreateStaffScreenState extends State<CreateStaffScreen> {
  final StaffController controller = Get.find<StaffController>();
  final RxInt currentStep = 0.obs;
  final PageController pageController = PageController(initialPage: 0);

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Obx(
          () => Text(
            controller.isEditing.value ? 'Edit Staff' : 'Add New Staff',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        leading: IconButton(
          icon: const Icon(IconlyLight.arrowLeft),
          onPressed: () {
            controller.clearForm();
            context.go(Routes.STAFF);
          },
        ),
      ),
      body: SizedBox(
        width: 350.w,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.0.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Center(child: _buildStepperHeader()),
              Expanded(
                child: PageView(
                  controller: pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  onPageChanged: (index) {
                    currentStep.value = index;
                  },
                  children: [
                    _buildPhoneVerificationStep(context),
                    _buildPersonalDetailsStep(context),
                    _buildRolePermissionsStep(context),
                  ],
                ),
              ),
              Center(child: _buildStepperControls(context)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepperHeader() {
    final stepTitles = [
      'Phone Number Verification',
      'Personal Details',
      'Role & Permissions',
    ];

    return Obx(
      () => Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: List.generate(
            stepTitles.length,
            (index) => Expanded(
              child: Row(
                children: [
                  // Step circle
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: _getStepColor(index),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child:
                          index < currentStep.value
                              ? const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 16,
                              )
                              : Text(
                                '${index + 1}',
                                style: TextStyle(
                                  color:
                                      index == currentStep.value
                                          ? Colors.white
                                          : Colors.grey,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                    ),
                  ),
                  // Connecting line
                  if (index < stepTitles.length - 1)
                    Expanded(
                      child: Container(
                        height: 2,
                        color:
                            index < currentStep.value
                                ? Colors.blue
                                : Colors.grey.shade300,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStepperControls(BuildContext context) {
    return Obx(
      () => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Back button
            if (currentStep.value > 0)
              CustomButton(
                style: CustomButtonStyle.outlined,
                onPressed: () {
                  currentStep.value--;
                  pageController.animateToPage(
                    currentStep.value,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                label: const Text('Back'),
              )
            else
              const SizedBox.shrink(),

            // Continue/Submit button
            CustomButton(
              onPressed: () {
                if (currentStep.value < 2) {
                  // Validate current step before proceeding
                  if (currentStep.value == 0 &&
                      !controller.isPhoneValid.value) {
                    ToastUtils.showInfoToast(
                      'Validation Error',
                      'Please enter a valid phone number',
                    );
                    return;
                  }

                  if (currentStep.value == 1) {
                    // Validate personal details
                    if (controller.firstNameController.text.isEmpty ||
                        controller.secondNameController.text.isEmpty ||
                        controller.emailController.text.isEmpty) {
                      ToastUtils.showInfoToast(
                        'Validation Error',
                        'Please fill all required fields',
                      );
                      return;
                    }
                  }

                  currentStep.value++;
                  pageController.animateToPage(
                    currentStep.value,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                } else {
                  _submitForm(context);
                }
              },
              label: Text(currentStep.value == 2 ? 'Submit' : 'Continue'),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStepColor(int stepIndex) {
    if (stepIndex < currentStep.value) {
      return Theme.of(context).primaryColor; // Completed step
    } else if (stepIndex == currentStep.value) {
      return Theme.of(context).primaryColor; // Current step
    } else {
      return Colors.grey.shade300; // Future step
    }
  }

  // Step content methods updated to work with PageView

  Widget _buildPhoneVerificationStep(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomPhoneInput(
          label: 'Phone Number',
          controller: controller.phoneNumberController,
          onPhoneNumberChanged: controller.onPhoneNumberChanged,
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: CustomButton(
            onPressed: () {
              controller.memberData.clear();
              controller.isMemberFound.value = false;
              if (controller.isPhoneValid.value) {
                controller.onPhoneNumberSubmitted();
              } else {
                ToastUtils.showErrorToast(
                  'Invalid Phone Number',
                  'Please enter a valid phone number',
                );
              }
            },
            icon: const Icon(IconlyLight.search),
            label: const Text('Verify Member'),
          ),
        ),
        const SizedBox(height: 16),
        Obx(() {
          if (controller.isPhoneChecking.value) {
            return const Center(child: CircleLoadingAnimation());
          }

          if (controller.isMemberFound.value) {
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.green),
                      const SizedBox(width: 8),
                      Text(
                        'Member Found',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Name: ${controller.firstNameController.text} ${controller.secondNameController.text}',
                    style: theme.textTheme.bodyMedium,
                  ),
                  if (controller.emailController.text.isNotEmpty)
                    Text(
                      'Email: ${controller.emailController.text}',
                      style: theme.textTheme.bodyMedium,
                    ),
                  if (controller.idNumberController.text.isNotEmpty)
                    Text(
                      'ID Number: ${controller.idNumberController.text}',
                      style: theme.textTheme.bodyMedium,
                    ),
                ],
              ),
            );
          }

          if (controller.errorMessage.value.isNotEmpty &&
              controller.phoneNumberController.text.isNotEmpty) {
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.error, color: Colors.red),
                      const SizedBox(width: 8),
                      Text(
                        'Member Not Found',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You can still add this person as staff, but they are not a registered member.',
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),
            );
          }

          return const SizedBox.shrink();
        }),
      ],
    );
  }

  Widget _buildPersonalDetailsStep(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextField(
          controller: controller.firstNameController,
          label: 'First Name',
          prefixIcon: IconlyLight.profile,
          isRequired: true,
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: controller.secondNameController,
          label: 'Last Name',
          prefixIcon: IconlyLight.profile,
          isRequired: true,
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: controller.emailController,
          label: 'Email Address',
          prefixIcon: IconlyLight.message,
          keyboardType: TextInputType.emailAddress,
          isRequired: true,
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: controller.idNumberController,
          label: 'ID Number',
          prefixIcon: IconlyLight.document,
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: controller.codeController,
          label: 'Staff Code/Number',
          prefixIcon: IconlyLight.scan,
          helperText: 'Unique identifier for this staff member',
        ),
      ],
    );
  }

  Widget _buildRolePermissionsStep(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Role dropdown
        Text(
          'Role',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Obx(
            () => DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                isExpanded: true,
                value:
                    controller.roleValue.value.isEmpty
                        ? null
                        : controller.roleValue.value,
                hint: const Text('Select Role'),
                items:
                    StaffRole.values.map((role) {
                      return DropdownMenuItem(
                        value: role.name,
                        child: Text(role.name),
                      );
                    }).toList(),

                onChanged: (value) {
                  if (value != null) {
                    controller.roleValue.value = value;
                  }
                },
              ),
            ),
          ),
        ),
        const SizedBox(height: 24),

        // Permissions
        Text(
          'Permissions',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Obx(
          () => SwitchListTile(
            title: const Text('Maker'),
            subtitle: const Text('Can create new records'),
            value: controller.isMakerValue.value,
            onChanged: (value) => controller.isMakerValue.value = value,
            activeColor: colorScheme.primary,
            contentPadding: EdgeInsets.zero,
          ),
        ),
        Obx(
          () => SwitchListTile(
            title: const Text('Checker'),
            subtitle: const Text('Can approve/review records'),
            value: controller.isCheckerValue.value,
            onChanged: (value) => controller.isCheckerValue.value = value,
            activeColor: colorScheme.primary,
            contentPadding: EdgeInsets.zero,
          ),
        ),
        Obx(
          () => SwitchListTile(
            title: const Text('Signatory'),
            subtitle: const Text('Can sign off on documents'),
            value: controller.isSignatoryValue.value,
            onChanged: (value) => controller.isSignatoryValue.value = value,
            activeColor: colorScheme.primary,
            contentPadding: EdgeInsets.zero,
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData prefixIcon,
    TextInputType? keyboardType,
    String? helperText,
    bool isRequired = false,
  }) {
    return CustomTextField(
      controller: controller,
      keyboardType: keyboardType,
      labelText: isRequired ? '$label *' : label,
      prefixIcon: Icon(prefixIcon),
      hintText: helperText,
    );
  }

  void _submitForm(BuildContext context) async {
    if (controller.isSubmitting.value) return;

    final result =
        controller.isEditing.value
            ? await controller.updateStaff()
            : await controller.createStaff();

    if (result) {
      ToastUtils.showSuccessToast(
        'Success',
        controller.isEditing.value
            ? 'Staff member updated successfully'
            : 'Staff member created successfully',
      );
      context.go(Routes.STAFF);
    } else {
      ToastUtils.showErrorToast('Error', controller.errorMessage.value);
    }
  }
}
