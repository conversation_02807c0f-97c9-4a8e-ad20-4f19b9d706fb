import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import '../../controllers/staff_controller.dart';

class StaffFilterWidget extends StatelessWidget {
  const StaffFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<StaffController>();

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(controller, context),
            const Divider(height: 24),
            _buildFilterControls(controller, context),
            const SizedBox(height: 16),
            _buildActionButtons(controller),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(StaffController controller, BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Row(
      children: [
        Icon(IconlyLight.filter, color: colorScheme.primary),
        const SizedBox(width: 8),
        Text(
          'Filter Staff',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const Spacer(),
        _buildClearButton(controller, context),
      ],
    );
  }

  Widget _buildClearButton(StaffController controller, BuildContext context) {
    return TextButton(
      onPressed: controller.clearFilters,
      style: TextButton.styleFrom(
        foregroundColor: Colors.red,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: Row(
        children: [
          const Icon(Icons.clear_all, size: 18),
          const SizedBox(width: 4),
          Text('Clear All', style: Theme.of(context).textTheme.labelMedium),
        ],
      ),
    );
  }

  Widget _buildFilterControls(
    StaffController controller,
    BuildContext context,
  ) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    return Wrap(
      runSpacing: 12,
      spacing: 12,
      children: [
        if (isMobile)
          SizedBox(
            width: 280.w,
            child: buildTextFilter(
              context,
              controller,
              label: 'Search (Phone, Email, ID, Code)',
              icon: IconlyLight.search,
              onChanged: (value) => controller.setIdentifierFilter(value),
              initialValue: controller.identifierFilter.value,
            ),
          ),

        // Role filter
        SizedBox(width: 150.w, child: _buildRoleFilter(controller, context)),

        // Date filters
        SizedBox(
          width: 130.w,
          child: _buildDateField(context, controller, isStartDate: true),
        ),

        SizedBox(
          width: 130.w,
          child: _buildDateField(context, controller, isStartDate: false),
        ),
      ],
    );
  }

  Widget buildTextFilter(
    BuildContext context,
    StaffController controller, {
    required String label,
    required IconData icon,
    required Function(String) onChanged,
    required String initialValue,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textController = TextEditingController(text: initialValue);

    return CustomTextField(
      controller: textController,
      labelText: label,
      prefixIcon: Icon(icon, color: colorScheme.primary),
      suffixIcon:
          initialValue.isNotEmpty
              ? _buildClearIcon(() {
                textController.clear();
                onChanged('');
              })
              : null,
      onChanged: onChanged,
    );
  }

  Widget _buildRoleFilter(StaffController controller, BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Obx(
      () => DropdownButtonFormField<String>(
        decoration: InputDecoration(
          labelText: 'Role',
          prefixIcon: Icon(IconlyLight.profile, color: colorScheme.primary),
          border: _inputBorder(),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 14,
            horizontal: 16,
          ),
        ),
        value:
            controller.roleFilter.value.isEmpty
                ? 'all'
                : controller.roleFilter.value,
        items: const [
          DropdownMenuItem(value: 'all', child: Text('All Roles')),
          DropdownMenuItem(value: 'admin', child: Text('Admin')),
          DropdownMenuItem(value: 'manager', child: Text('Manager')),
          DropdownMenuItem(value: 'staff', child: Text('Staff')),
        ],
        onChanged: (value) {
          if (value != null) {
            if (value == 'all') {
              controller.roleFilter.value = '';
            } else {
              controller.roleFilter.value = value;
            }
            controller.refreshStaffs();
          }
        },
        icon: Icon(Icons.arrow_drop_down, color: colorScheme.primary),
      ),
    );
  }

  Widget _buildDateField(
    BuildContext context,
    StaffController controller, {
    required bool isStartDate,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Obx(() {
      final date =
          isStartDate ? controller.startDate.value : controller.endDate.value;
      final label = isStartDate ? 'Start Date' : 'End Date';

      return CustomTextFormField(
        readOnly: true,
        labelText: label,
        prefixIcon: Icon(IconlyLight.calendar, color: colorScheme.primary),
        suffixIcon: _buildClearIcon(
          () =>
              isStartDate
                  ? controller.setDateFilters(null, controller.endDate.value)
                  : controller.setDateFilters(controller.startDate.value, null),
          showClear: date != null,
        ),
        hintText: 'Select $label',

        controller: TextEditingController(
          text: date != null ? DateFormat('MMM dd, yyyy').format(date) : null,
        ),
        onTap:
            () =>
                isStartDate
                    ? _selectStartDate(context, controller)
                    : _selectEndDate(context, controller),
      );
    });
  }

  Widget _buildActionButtons(StaffController controller) {
    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        onPressed: controller.refreshStaffs,
        icon: const Icon(IconlyLight.filter),
        label: const Text('Apply Filters'),
      ),
    );
  }

  Widget _buildClearIcon(VoidCallback onPressed, {bool showClear = true}) {
    if (!showClear) return const SizedBox.shrink();

    return IconButton(
      icon: const Icon(Icons.clear, size: 18),
      onPressed: onPressed,
      splashRadius: 20,
    );
  }

  InputBorder _inputBorder() {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: Colors.grey),
    );
  }

  Future<void> _selectStartDate(
    BuildContext context,
    StaffController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.startDate.value ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      controller.setDateFilters(picked, controller.endDate.value);
    }
  }

  Future<void> _selectEndDate(
    BuildContext context,
    StaffController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.endDate.value ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      // Set to end of day for inclusive filtering
      final endOfDay = DateTime(
        picked.year,
        picked.month,
        picked.day,
        23,
        59,
        59,
      );
      controller.setDateFilters(controller.startDate.value, endOfDay);
    }
  }
}
