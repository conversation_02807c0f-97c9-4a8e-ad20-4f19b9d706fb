import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:go_router/go_router.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/data/models/membership_model.dart';
import 'package:onechurch/data/models/organisation_model.dart';
import 'package:onechurch/data/models/user_model.dart';
import 'package:onechurch/features/auth/widgets/membership_selection_dialog.dart';
import 'package:onechurch/features/members/presentation/select-org/select_new_org.dart';
import '../../../core/app/services/auth_service.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/constants/enums.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../core/app/constants/routes.dart';
import 'package:get_storage/get_storage.dart';
import '../../../core/app/services/storage_service.dart';
import '../models/registration_data.dart';

class AuthController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();
  final HttpService _httpService = HttpService();
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    clientId:
        '891422507690-1am5mn2lg58vi1gge6pnsgvl8p9bfii9.apps.googleusercontent.com',
    scopes: ['email', 'profile'],
  );
  final box = Get.put<GetStorage>(GetStorage());
  final StorageService _storageService = Get.find<StorageService>();
  String phoneNumber = '';
  // Form controllers
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController otpController = TextEditingController();
  final TextEditingController pinController = TextEditingController();
  final TextEditingController confirmPinController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController secondNameController = TextEditingController();
  final TextEditingController idNumberController = TextEditingController();
  final TextEditingController referredByCodeController =
      TextEditingController();
  final TextEditingController countyController = TextEditingController();
  final TextEditingController subCountyController = TextEditingController();
  final TextEditingController countryCodeController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  // State variables
  final RxBool isLoading = false.obs;
  final RxBool isGoogleLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final Rx<UserRole> selectedRole = Rx<UserRole>(UserRole.member);
  final RxBool isOtpSent = false.obs;
  final RxBool isPinSet = false.obs;
  final RxString userCountry = 'US'.obs;
  final RxString checkoutId = ''.obs;
  RxDouble? latitude;
  RxDouble? longitude;
  final logger = Get.put<Logger>(Logger());
  final RxBool isPasswordReset = false.obs;
  Rx<Organisation?> currentOrg = Rx<Organisation?>(
    Organisation(id: "0196af91-32c4-713a-b994-7d3ce787e312"),
  );
  final RxString organizationId = ''.obs;
  final RxString membershipId = ''.obs;
  final RxList<Membership> userMemberships = <Membership>[].obs;
  final RxList<Map<String, dynamic>> userOrganizations =
      <Map<String, dynamic>>[].obs;

  // User model variable that can be accessed from elsewhere
  final Rx<UserModel?> user = Rx<UserModel?>(null);

  @override
  void onInit() {
    super.onInit();
    _httpService.initializeDio();
    phoneController.addListener(() {
      if (phoneController.text.isNotEmpty) {
        if (countryCodeController.text.isNotEmpty) {
          phoneNumber = '${countryCodeController.text}${phoneController.text}'
              .replaceAll('+', '');
        } else {
          phoneNumber = phoneController.text.replaceAll('+', '');
        }
      }
    });
    _getCurrentLocation();
    loadUserOrganizations();
    _loadUserData();
  }

  // Load user data if user is logged in
  Future<void> _loadUserData() async {
    try {
      if (_authService.isLoggedIn) {
        // Try to load UserModel from storage
        final userModel = _storageService.getUserModel();
        if (userModel != null) {
          user.value = userModel;
          logger.d(
            'User data loaded from storage: ${userModel.firstName} ${userModel.secondName}',
          );
        } else {
          // Fallback to raw user data if UserModel is not available
          final rawUserData = _storageService.getUserData();
          if (rawUserData != null) {
            try {
              user.value = UserModel.fromJson(rawUserData);
              // Save as UserModel for future use
              await _storageService.saveUserModel(user.value!);
              logger.d('User data converted and saved as UserModel');
            } catch (e) {
              logger.e('Error converting raw user data to UserModel: $e');
            }
          }
        }
      }
    } catch (e) {
      logger.e('Error loading user data: $e');
    }
  }

  // Load user organizations from storage
  Future<void> loadUserOrganizations() async {
    try {
      // Load memberships from storage
      final membershipsData = _storageService.read('user_memberships');
      if (membershipsData != null &&
          membershipsData is List &&
          membershipsData.isNotEmpty) {
        userOrganizations.value = List<Map<String, dynamic>>.from(
          membershipsData.map((item) => Map<String, dynamic>.from(item)),
        );

        // Load selected membership - tries 3 fallback options
        final selectedMembershipModel = _storageService.read(
          'selected_membership_model',
        );
        final selectedMembershipData = _storageService.read(
          'selected_membership',
        );

        //  1: Try loading from model storage
        if (selectedMembershipModel != null) {
          final membership = Membership.fromJson(selectedMembershipModel);
          if (membership.organisation != null) {
            currentOrg.value = membership.organisation!;
            organizationId.value = membership.organisationId ?? '';
            membershipId.value = membership.id ?? '';
            return; // Exit if successful
          }
        }

        //  2: Try loading from raw storage
        if (selectedMembershipData != null &&
            selectedMembershipData['organisation'] != null) {
          currentOrg.value = Organisation.fromJson(
            selectedMembershipData['organisation'],
          );
          organizationId.value =
              selectedMembershipData['organisation']['id'] ?? '';
          membershipId.value = selectedMembershipData['id'] ?? '';
          return; // Exit if successful
        }

        //  3: Fallback to first membership if nothing else works
        if (membershipsData.isNotEmpty &&
            membershipsData[0]['organisation'] != null) {
          currentOrg.value = Organisation.fromJson(
            membershipsData[0]['organisation'],
          );
          organizationId.value = membershipsData[0]['organisation']['id'] ?? '';
          membershipId.value = membershipsData[0]['id'] ?? '';
        }
      }
    } catch (e) {
      logger.e('Error loading user organizations: $e');
    }
  }

  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    nameController.dispose();
    otpController.dispose();
    pinController.dispose();
    confirmPinController.dispose();
    phoneController.dispose();
    firstNameController.dispose();
    secondNameController.dispose();
    idNumberController.dispose();
    referredByCodeController.dispose();
    countyController.dispose();
    subCountyController.dispose();
    countryCodeController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }

  // Set selected role
  void setRole(UserRole role) {
    selectedRole.value = role;
  }

  // Get FCM token
  Future<String?> getFcmToken() async {
    try {
      NotificationSettings settings = await FirebaseMessaging.instance
          .requestPermission(alert: true, badge: true, sound: true);

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        String? fcmToken = await FirebaseMessaging.instance.getToken();
        if (fcmToken != null) {
          logger.i('FCM Token: $fcmToken');
          return fcmToken;
        } else {
          logger.w('FCM Token is null');
        }
      } else {
        logger.w('Notification permissions not granted');
      }
      return null;
    } catch (e) {
      logger.e('Error getting FCM token: $e');
      return null;
    }
  }

  // Get current location
  Future<void> _getCurrentLocation() async {
    try {} catch (e) {
      logger.e('Error getting location: $e');
    }
  }

  int? userID;
  // Register user
  Future<bool> register(BuildContext context) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      // Validate inputs
      if (firstNameController.text.isEmpty ||
          secondNameController.text.isEmpty) {
        errorMessage.value = 'Please fill all required fields';
        isLoading.value = false;
        return false;
      }

      // Check if either email or phone is provided
      if (emailController.text.isEmpty && phoneController.text.isEmpty) {
        errorMessage.value = 'Please provide either email or phone number';
        isLoading.value = false;
        return false;
      }

      // Format phone number if provided
      String? formattedphoneNumber;
      if (phoneController.text.isNotEmpty) {
        // Ensure country code is included
        if (countryCodeController.text.isNotEmpty) {
          formattedphoneNumber =
              '${countryCodeController.text}${phoneController.text}';
        } else {
          formattedphoneNumber = phoneController.text;
        }
      }

      // Prepare registration data
      final registrationData = RegistrationData(
        phoneNumber: formattedphoneNumber,
        firstName: firstNameController.text,
        secondName: secondNameController.text,
        email: emailController.text.isNotEmpty ? emailController.text : null,
        idNumber: idNumberController.text,
        referredByCode: referredByCodeController.text,
        countryCode: countryCodeController.text,
        county: countyController.text,
        subCounty: subCountyController.text,
        latitude: latitude?.value,
        longitude: longitude?.value,
      );

      // Register with backend
      final response = await _httpService.request(
        url: ApiUrls.register,
        method: Method.POST,
        params: registrationData.toJson(),
      );

      if (response.data["status"]) {
        checkoutId.value = response.data["data"]["checkout_id"];

        userID = response.data["data"]["user_id"];
        final userData = response.data["data"]["user"];

        // Parse and store UserModel
        try {
          user.value = UserModel.fromJson(userData);
          await _storageService.saveUserModel(user.value!);
          logger.d(
            'User model parsed and saved during registration: ${user.value!.firstName} ${user.value!.secondName}',
          );
        } catch (e) {
          logger.e(
            'Error parsing user data to UserModel during registration: $e',
          );
        }

        // Save user data
        await _storageService.saveUserData(userData);
        if (response.data["data"]["memberships"] == null ||
            (response.data["data"]["memberships"] is List &&
                response.data["data"]["memberships"].isEmpty)) {
          await SelectNewOrg.show(context);
        }

        // Send OTP if phone number was used
        if (phoneController.text.isNotEmpty) {
          await sendOtp();
          isOtpSent.value = true;

          // Navigate to OTP verification
          context.go(Routes.OTP_VERIFICATION);
        } else {
          // If email was used, navigate directly to home
          Get.offAllNamed(Routes.HOME);
        }
        return true;
      } else {
        errorMessage.value = response.data["message"] ?? 'Registration failed';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Registration failed: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Login user
  Future<bool> login(BuildContext context) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      // Validate inputs
      if (phoneController.text.isEmpty && emailController.text.isEmpty) {
        errorMessage.value = 'Please enter your phone number or email';
        isLoading.value = false;
        return false;
      }

      if (passwordController.text.isEmpty) {
        errorMessage.value = 'Please enter your password';
        isLoading.value = false;
        return false;
      }

      // Get FCM token and log it
      final fcmToken = await getFcmToken();
      logger.i('FCM Token for login: $fcmToken');

      // Prepare request parameters
      final Map<String, dynamic> params = {
        'password': passwordController.text.trim(),
        'device_token': fcmToken ?? "",
      };

      // Add email if provided
      if (emailController.text.isNotEmpty) {
        // Validate email format
        if (!RegExp(
          r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
        ).hasMatch(emailController.text)) {
          errorMessage.value = 'Please enter a valid email';
          isLoading.value = false;
          return false;
        }
        params['email'] = emailController.text.trim();
      }

      // Add phone number if provided
      if (phoneController.text.isNotEmpty) {
        // Format phone number
        String formattedphoneNumber;
        if (countryCodeController.text.isNotEmpty) {
          formattedphoneNumber =
              '${countryCodeController.text}${phoneController.text.trim().replaceAll(RegExp(r'[^\d]'), '')}';
        } else {
          formattedphoneNumber = phoneController.text.trim().replaceAll(
            RegExp(r'[^\d]'),
            '',
          );
        }

        if (formattedphoneNumber.isEmpty) {
          errorMessage.value = 'Please enter a valid phone number';
          isLoading.value = false;
          return false;
        }
        params['phone_number'] = formattedphoneNumber;
      }

      final response = await _httpService.request(
        url: ApiUrls.login,
        method: Method.POST,
        params: params,
      );

      if (response.data["status"] ?? false) {
        // Parse userData to UserModel
        final userData = response.data["data"]["user"];
        userID = response.data["data"]["user_id"];
        final token = response.data["data"]["token"];
        final memberships = response.data["data"]["memberships"];

        // Parse and store UserModel
        try {
          user.value = UserModel.fromJson(userData);
          await _storageService.saveUserModel(user.value!);
          logger.d(
            'User model parsed and saved: ${user.value!.firstName} ${user.value!.secondName}',
          );
        } catch (e) {
          logger.e('Error parsing user data to UserModel: $e');
        }

        // Save user data and token
        await _storageService.saveUserData(userData);
        await _storageService.saveToken(token);

        // Handle memberships
        if (memberships != null && memberships is List) {
          // Save all memberships
          await _storageService.write('user_memberships', memberships);

          // Log memberships for debugging
          logger.d('Memberships from login: $memberships');

          if (memberships.isEmpty) {
            // If no memberships, show organization selection dialog
            logger.d('No memberships found, showing SelectNewOrg dialog');
            await SelectNewOrg.show(context);
          }
          if (memberships.length > 1) {
            userOrganizations.value = List<Map<String, dynamic>>.from(
              memberships.map((item) => Map<String, dynamic>.from(item)),
            );
            // Show membership selection dialog if multiple memberships
            await showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return MembershipSelectionDialog(
                  memberships: memberships,
                  onMembershipSelected: (selectedMembership) async {
                    await handleMembershipSelection(selectedMembership);
                    // Navigation is now handled in the dialog itself
                    // Login with auth service after membership selection
                    await _authService.login(
                      token,
                      userData,
                      role: selectedRole.value,
                    );
                    // Firebase login
                    await firebaseLogin(token);
                    // Clear controllers
                    emailController.clear();
                    passwordController.clear();
                    phoneController.clear();
                    // Navigation to home happens in the dialog
                    context.go(Routes.HOME);
                  },
                );
              },
            );
          } else if (memberships.length == 1) {
            // If only one membership, select it automatically
            await handleMembershipSelection(memberships[0]);

            // Login with auth service
            await _authService.login(token, userData, role: selectedRole.value);

            // Firebase login
            await firebaseLogin(token);

            // Clear controllers
            emailController.clear();
            passwordController.clear();
            phoneController.clear();

            // Navigate to home for single membership case
            context.go(Routes.HOME);
            return true;
          }
        } else {
          // No memberships list, proceed with normal login flow

          // Login with auth service
          await _authService.login(token, userData, role: selectedRole.value);

          // Firebase login
          await firebaseLogin(token);

          // Clear controllers
          emailController.clear();
          passwordController.clear();
          phoneController.clear();

          // Navigate to home
          context.go(Routes.HOME);
          return true;
        }

        // For multiple memberships case, navigation happens in the dialog callback
        return true;
      } else {
        errorMessage.value = response.data["message"] ?? 'Login failed';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Login failed: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Firebase login
  Future<void> firebaseLogin(String token) async {
    try {
      await _firebaseAuth.signInWithCustomToken(token);
      logger.w("Signed in with custom token.");
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case "operation-not-allowed":
          logger.e("Custom token auth hasn't been enabled for this project.");
          break;
        default:
          logger.e("Unknown error during Firebase login.");
      }
    }
  }

  // Send OTP
  Future<bool> sendOtp() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _httpService.request(
        url: ApiUrls.forgotPassword,
        method: Method.POST,
        params: {'phone_number': phoneNumber.replaceAll('+', '')},
      );

      if (response.data["status"]) {
        checkoutId.value = response.data["data"]["checkout_id"];
        return true;
      } else {
        errorMessage.value = response.data["message"] ?? 'Failed to send OTP';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to send OTP: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Verify OTP
  Future<bool> verifyOtp(BuildContext context) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _httpService.request(
        url: ApiUrls.confirmOtp,
        method: Method.POST,
        params: {
          'user_id': userID,
          'phone_number': phoneNumber.replaceAll('+', ''),
          'otp': int.parse(otpController.text),
          'checkout_id': checkoutId.value,
        },
      );

      if (response.data["status"]) {
        final userData = response.data["data"]["user"];

        // Parse and store UserModel
        try {
          user.value = UserModel.fromJson(userData);
          await _storageService.saveUserModel(user.value!);
          logger.d(
            'User model parsed and saved during OTP verification: ${user.value!.firstName} ${user.value!.secondName}',
          );
        } catch (e) {
          logger.e(
            'Error parsing user data to UserModel during OTP verification: $e',
          );
        }

        await _storageService.saveUserData(userData);
        return true;
      } else {
        errorMessage.value =
            response.data["message"] ?? 'OTP verification failed';
        return false;
      }
    } catch (error) {
      logger.e(error);
      errorMessage.value = 'OTP verification failed: ${error.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Set Password
  Future<bool> setPassword(BuildContext context, bool isPasswordReset) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      // Validate passwords match
      if (pinController.text != confirmPasswordController.text) {
        errorMessage.value = 'Passwords do not match';
        isLoading.value = false;
        return false;
      }

      // Different request based on whether this is password reset or regular flow
      final String url =
          isPasswordReset ? ApiUrls.resetPassword : ApiUrls.setPin;

      // Prepare parameters based on flow
      final Map<String, dynamic> params =
          isPasswordReset
              ? {
                'phone_number': phoneNumber,
                'checkout_id': checkoutId.value,
                'code': int.parse(otpController.text),
                'new_password': pinController.text,
              }
              : {
                'phone_number': phoneNumber,
                'password': pinController.text,
                'user_id': userID,
              };

      // Send the request
      final response = await _httpService.request(
        url: url,
        method: Method.POST,
        params: params,
      );

      if (response.data["status"]) {
        final userData = response.data["data"]["user"];
        final token = response.data["data"]["token"];

        // Parse and store UserModel
        try {
          user.value = UserModel.fromJson(userData);
          await _storageService.saveUserModel(user.value!);
          logger.d(
            'User model parsed and saved during password set: ${user.value!.firstName} ${user.value!.secondName}',
          );
        } catch (e) {
          logger.e(
            'Error parsing user data to UserModel during password set: $e',
          );
        }

        // Save user data and token
        await _storageService.saveUserData(userData);
        await _storageService.saveToken(token);

        // Login with auth service
        final authService = Get.find<AuthService>();
        await authService.login(token, userData, role: selectedRole.value);

        // Firebase login
        await firebaseLogin(token);

        isPinSet.value = true;

        // Navigate to home
        Get.offAllNamed(Routes.HOME);
        return true;
      } else {
        errorMessage.value =
            response.data["message"] ?? 'Failed to set password';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to set password: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Logout user
  Future<void> logout(BuildContext context) async {
    isLoading.value = true;

    try {
      await _authService.logout();
      await _firebaseAuth.signOut();

      // Clear stored data
      await _storageService.removeUserData();
      await _storageService.removeUserModel();
      await _storageService.removeToken();

      // Clear user variable
      user.value = null;

      // Navigate to login screen
      Get.offAllNamed(Routes.LOGIN);
    } catch (e) {
      errorMessage.value = 'Logout failed: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
  }

  // Check if user is authenticated
  bool get isAuthenticated => _authService.isLoggedIn;

  // Check if user is admin
  bool get isAdmin => _authService.isAdmin;

  // Get user data
  Map<String, dynamic> get userData => _authService.userData;

  // Get user role
  UserRole get userRole => _authService.userRole;

  // Sign in with Google
  Future<void> signInWithGoogle(BuildContext context) async {
    isGoogleLoading.value = true;
    errorMessage.value = '';

    try {
      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        errorMessage.value = 'Google sign in was cancelled';
        isGoogleLoading.value = false;
        return;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Get FCM token
      final fcmToken = await getFcmToken();

      // Login with backend
      final response = await _httpService.request(
        url: ApiUrls.login,
        method: Method.POST,
        params: {
          'google_token': googleAuth.idToken,
          'device_token': fcmToken ?? "",
        },
      );

      if (response.data["status"]) {
        final userData = response.data["data"]["user"];
        final token = response.data["data"]["token"];

        // Parse and store UserModel
        try {
          user.value = UserModel.fromJson(userData);
          await _storageService.saveUserModel(user.value!);
          logger.d(
            'User model parsed and saved during Google sign-in: ${user.value!.firstName} ${user.value!.secondName}',
          );
        } catch (e) {
          logger.e(
            'Error parsing user data to UserModel during Google sign-in: $e',
          );
        }

        // Save user data and token
        await _storageService.saveUserData(userData);
        await _storageService.saveToken(token);

        // Login with auth service
        await _authService.login(token, userData, role: selectedRole.value);

        // Firebase login
        await firebaseLogin(token);

        // Clear controllers
        emailController.clear();
        passwordController.clear();
        phoneController.clear();

        // Navigate to home
        Get.offAllNamed(Routes.HOME);
      } else {
        errorMessage.value =
            response.data["message"] ?? 'Google sign in failed';
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Google sign in failed: ${e.toString()}';
    } finally {
      isGoogleLoading.value = false;
    }
  }

  // Password reset flow - Step 1: Initiate password reset
  Future<bool> initiatePasswordReset(BuildContext context) async {
    isLoading.value = true;
    errorMessage.value = '';
    isPasswordReset.value = true;

    try {
      final phoneNumber = phoneController.text.trim();
      if (phoneNumber.isEmpty) {
        errorMessage.value = 'Please enter a valid phone number';
        return false;
      }

      // Initialize HTTP service
      _httpService.initializeDio();

      // Call password reset API
      final response = await _httpService.request(
        url: ApiUrls.forgotPassword,
        method: Method.POST,
        params: {'phone_number': phoneNumber},
      );

      if (response.data["status"]) {
        // Store phone number and checkout ID for next step
        this.phoneNumber = phoneNumber;
        checkoutId.value = response.data["data"]["checkout_id"];

        // Navigate to OTP verification
        context.go(Routes.OTP_VERIFICATION);
        return true;
      } else {
        errorMessage.value =
            response.data["message"] ?? 'Failed to reset password';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Password reset failed: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> confirmOtp(String otp, BuildContext context) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      _httpService.initializeDio();

      // Different params based on whether we're in password reset flow
      final Map<String, dynamic> params =
          isPasswordReset.value
              ? {
                'phone_number': phoneNumber,
                'checkout_id': checkoutId.value,
                'code': int.parse(otp),
              }
              : {
                'phone_number': phoneNumber,
                'user_id': userID,
                'otp': int.parse(otp),
                'checkout_id': checkoutId.value,
              };

      final response = await _httpService.request(
        url: ApiUrls.confirmOtp,
        method: Method.POST,
        params: params,
      );

      if (response.data["status"]) {
        final userData = response.data["data"]["user"];

        // Parse and store UserModel
        try {
          user.value = UserModel.fromJson(userData);
          await _storageService.saveUserModel(user.value!);
          logger.d(
            'User model parsed and saved during OTP confirmation: ${user.value!.firstName} ${user.value!.secondName}',
          );
        } catch (e) {
          logger.e(
            'Error parsing user data to UserModel during OTP confirmation: $e',
          );
        }

        await _storageService.saveUserData(userData);

        // Navigate based on flow
        if (isPasswordReset.value) {
          // In password reset flow, go to set new password
          context.go(Routes.SET_PIN, extra: {'isReset': true});
        } else {
          context.go(Routes.SET_PIN);
        }
        return true;
      } else {
        errorMessage.value = response.data["message"] ?? 'Invalid OTP';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'OTP verification failed: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> handleMembershipSelection(
    Map<String, dynamic> selectedMembership,
  ) async {
    // Log the selected membership for debugging
    logger.d('Selected membership: $selectedMembership');

    // Save the raw membership data
    await _storageService.write('selected_membership', selectedMembership);

    // Try to parse as Membership model
    try {
      final membership = Membership.fromJson(selectedMembership);
      await _storageService.write(
        'selected_membership_model',
        membership.toJson(),
      );

      // Log successful parsing
      logger.d('Successfully parsed membership model');
    } catch (e) {
      logger.e('Error parsing membership model: $e');
      // Continue even if parsing fails
    }

    // Handle organization data
    if (selectedMembership['organisation'] != null) {
      var orgData = selectedMembership['organisation'];

      // Check if organization data is a Map
      if (orgData is Map<dynamic, dynamic>) {
        // Extract ID from organization data
        String orgId =
            orgData['id']?.toString() ?? orgData['ID']?.toString() ?? '';
        organizationId.value = orgId;
        logger.d('Set organization ID to: $orgId');

        // Try to parse as Organisation model
        try {
          // Convert dynamic keys to string keys
          Map<String, dynamic> stringOrgData = {};
          orgData.forEach((key, value) {
            stringOrgData[key.toString()] = value;
          });

          currentOrg.value = Organisation.fromJson(stringOrgData);
          logger.d('Successfully parsed organization model');
        } catch (e) {
          logger.e('Error parsing organization model: $e');
        }
      }

      // Set membership ID
      membershipId.value = selectedMembership['id']?.toString() ?? '';
    } else {
      membershipId.value = selectedMembership['id'] ?? '';
      currentOrg.value = Organisation.fromJson(
        selectedMembership['organisation'],
      );
    }
  }
}
