import 'package:get/get.dart';
import 'package:onechurch/data/models/member_model.dart';

/// Model for relationship types
class RelationshipType {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final String? description;
  final String? category;
  final String? organisationId;
  final bool? isGeneral;

  RelationshipType({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.description,
    this.category,
    this.organisationId,
    this.isGeneral,
  });

  factory RelationshipType.fromJson(Map<String, dynamic> json) {
    return RelationshipType(
      id: json['id'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
      deletedAt: json['deleted_at'],
      title: json['title'],
      description: json['description'],
      category: json['category'],
      organisationId: json['organisation_id'],
      isGeneral: json['is_general'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'deleted_at': deletedAt,
      'title': title,
      'description': description,
      'category': category,
      'organisation_id': organisationId,
      'is_general': isGeneral,
    };
  }
}

/// Model for member relationships
class MemberRelationship {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? fromMemberId;
  final String? toMemberId;
  final String? relationshipTypeId;
  final RelationshipType? relationshipType;
  final MemberModel? fromMember;
  final MemberModel? toMember;
  final String? description;

  MemberRelationship({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.fromMemberId,
    this.toMemberId,
    this.relationshipTypeId,
    this.relationshipType,
    this.fromMember,
    this.toMember,
    this.description,
  });

  factory MemberRelationship.fromJson(Map<String, dynamic> json) {
    return MemberRelationship(
      id: json['id'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
      deletedAt: json['deleted_at'],
      fromMemberId: json['from_member_id'],
      toMemberId: json['to_member_id'],
      relationshipTypeId: json['relationship_type_id'],
      relationshipType:
          json['relationship_type'] != null
              ? RelationshipType.fromJson(json['relationship_type'])
              : null,
      fromMember:
          json['from_member'] != null
              ? MemberModel.fromJson(json['from_member'])
              : null,
      toMember:
          json['to_member'] != null
              ? MemberModel.fromJson(json['to_member'])
              : null,
      description: json['description'],
    );
  }

  // Helper methods to get member names
  String getFromMemberName() {
    if (fromMember == null) return 'Unknown';
    return '${fromMember?.firstName ?? ''} ${fromMember?.secondName ?? ''}'
        .trim();
  }

  String getToMemberName() {
    if (toMember == null) return 'Unknown';
    return '${toMember?.firstName ?? ''} ${toMember?.secondName ?? ''}'.trim();
  }

  // Helper method to get profile URL
  String? getFromMemberProfileUrl() {
    return fromMember?.profileUrl;
  }

  String? getToMemberProfileUrl() {
    return toMember?.profileUrl;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'deleted_at': deletedAt,
      'from_member_id': fromMemberId,
      'to_member_id': toMemberId,
      'relationship_type_id': relationshipTypeId,
      'description': description,
      'to_member': toMember?.toJson(),
      'from_member': fromMember?.toJson(),
    };
  }
}

/// Response model for relationship types
class RelationshipTypeResponse {
  final bool status;
  final String message;
  final RelationshipTypeData? data;

  RelationshipTypeResponse({
    required this.status,
    required this.message,
    this.data,
  });

  factory RelationshipTypeResponse.fromJson(Map<String, dynamic> json) {
    // Handle case where data is a list instead of a map
    if (json['data'] is List) {
      final items =
          (json['data'] as List)
              .map((item) => RelationshipType.fromJson(item))
              .toList();

      return RelationshipTypeResponse(
        status: json['status'] ?? false,
        message: json['message'] ?? '',
        data: RelationshipTypeData(
          items: items,
          totalItems: items.length,
          currentPage: 0,
          totalPages: 1,
        ),
      );
    }

    return RelationshipTypeResponse(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      data:
          json['data'] != null && json['data'] is Map
              ? RelationshipTypeData.fromJson(json['data'])
              : null,
    );
  }
}

/// Data model for relationship type response
class RelationshipTypeData {
  final List<RelationshipType> items;
  final int totalItems;
  final int currentPage;
  final int totalPages;

  RelationshipTypeData({
    required this.items,
    required this.totalItems,
    required this.currentPage,
    required this.totalPages,
  });

  factory RelationshipTypeData.fromJson(Map<String, dynamic> json) {
    // Handle case where items might not be a list
    List<RelationshipType> itemsList = [];
    if (json['items'] is List) {
      itemsList =
          (json['items'] as List)
              .map((item) => RelationshipType.fromJson(item))
              .toList();
    }

    return RelationshipTypeData(
      items: itemsList,
      totalItems: json['total_items'] ?? 0,
      currentPage: json['current_page'] ?? 0,
      totalPages: json['total_pages'] ?? 0,
    );
  }
}

/// Response model for member relationships
class MemberRelationshipResponse {
  final bool status;
  final String message;
  final MemberRelationshipData? data;

  MemberRelationshipResponse({
    required this.status,
    required this.message,
    this.data,
  });

  factory MemberRelationshipResponse.fromJson(Map<String, dynamic> json) {
    // Handle case where data is a list instead of a map
    if (json['data'] is List) {
      List<MemberRelationship> items = [];

      // Only try to map items if the list is not empty
      if ((json['data'] as List).isNotEmpty) {
        try {
          items =
              (json['data'] as List)
                  .map((item) => MemberRelationship.fromJson(item))
                  .toList();
        } catch (e) {
          // If mapping fails, return empty list but log the error
          Get.log('Error mapping relationship items: $e');
        }
      }

      return MemberRelationshipResponse(
        status: json['status'] ?? false,
        message: json['message'] ?? '',
        data: MemberRelationshipData(
          items: items,
          totalItems: items.length,
          currentPage: 0,
          totalPages: 1,
        ),
      );
    }

    // Handle case where data is null or empty
    if (json['data'] == null ||
        (json['data'] is List && (json['data'] as List).isEmpty)) {
      return MemberRelationshipResponse(
        status: json['status'] ?? false,
        message: json['message'] ?? '',
        data: MemberRelationshipData(
          items: [],
          totalItems: 0,
          currentPage: 0,
          totalPages: 0,
        ),
      );
    }

    return MemberRelationshipResponse(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      data:
          json['data'] != null && json['data'] is Map
              ? MemberRelationshipData.fromJson(json['data'])
              : null,
    );
  }
}

/// Data model for member relationship response
class MemberRelationshipData {
  final List<MemberRelationship> items;
  final int totalItems;
  final int currentPage;
  final int totalPages;

  MemberRelationshipData({
    required this.items,
    required this.totalItems,
    required this.currentPage,
    required this.totalPages,
  });

  factory MemberRelationshipData.fromJson(Map<String, dynamic> json) {
    // Handle case where items might not be a list or might not exist
    List<MemberRelationship> itemsList = [];
    if (json['items'] != null && json['items'] is List) {
      itemsList =
          (json['items'] as List)
              .map((item) => MemberRelationship.fromJson(item))
              .toList();
    }

    return MemberRelationshipData(
      items: itemsList,
      totalItems: json['total_items'] ?? 0,
      currentPage: json['current_page'] ?? 0,
      totalPages: json['total_pages'] ?? 0,
    );
  }
}
