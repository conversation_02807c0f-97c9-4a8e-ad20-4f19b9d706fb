import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../controllers/sms_controller.dart';
import '../widgets/new_sms_filter_widget.dart';
import '../widgets/sms_stats_widget.dart';
import '../../../../core/app/constants/routes.dart';
import '../../../../core/app/utils/show_toast.dart';
import '../../../../data/models/sms_model.dart';
import 'package:go_router/go_router.dart';

class SmsScreen extends StatefulWidget {
  const SmsScreen({super.key});

  @override
  State<SmsScreen> createState() => _SmsScreenState();
}

class _SmsScreenState extends State<SmsScreen> {
  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  final controller = Get.find<SmsController>();

  List<PlutoRow> setupRows(List<SmsModel> messages) {
    return messages.map((message) {
      return PlutoRow(
        cells: {
          'id': PlutoCell(value: message.id ?? ''),
          'message': PlutoCell(value: message.message ?? ''),
          'status': PlutoCell(value: message.status ?? 'Pending'),
          'created_at': PlutoCell(
            value:
                message.createdAt != null
                    ? DateFormat(
                      'dd MMM yyyy HH:mm a',
                    ).format(message.createdAt!)
                    : 'N/A',
          ),
          'delivered_at': PlutoCell(
            value:
                message.createdAt != null
                    ? DateFormat(
                      'dd MMM yyyy HH:mm a',
                    ).format(message.createdAt!)
                    : 'N/A',
          ),
          'actions': PlutoCell(value: message.id),
        },
      );
    }).toList();
  }

  void setColumns() {
    columns = [
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.text(),
        width: 80,
        enableRowChecked: true,
      ),
      PlutoColumn(
        title: 'Message',
        field: 'message',
        type: PlutoColumnType.text(),
        width: 200,
      ),
      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final status = rendererContext.row.cells['status']?.value;
          return Chip(
            backgroundColor:
                status == "Delivered"
                    ? Colors.green.shade100.withOpacity(0.2)
                    : Colors.orange.shade100.withOpacity(0.2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
              side: BorderSide(
                color: status == "Delivered" ? Colors.green : Colors.orange,
                width: 0.6,
              ),
            ),
            label: Text(
              status.toString(),
              style: TextStyle(
                color: status == "Delivered" ? Colors.green : Colors.orange,
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Created At',
        field: 'created_at',
        type: PlutoColumnType.text(),
        width: 150,
      ),
      PlutoColumn(
        title: 'Delivered At',
        field: 'delivered_at',
        type: PlutoColumnType.text(),
        width: 150,
      ),

      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.visibility, size: 20),
                onPressed: () {
                  final messageId = rendererContext.row.cells['id']?.value;
                  if (messageId != null) {
                    // View message details
                    final message = controller.messages.firstWhere(
                      (msg) => msg.id == messageId,
                      orElse: () => SmsModel(),
                    );

                    showDialog(
                      context: context,
                      builder:
                          (context) => AlertDialog(
                            title: const Text('Message Details'),
                            content: SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text('ID: ${message.id ?? "N/A"}'),
                                  const Divider(),
                                  Text('Content: ${message.message ?? "N/A"}'),
                                  const Divider(),
                                  Text(
                                    'Status: ${message.status ?? "Pending"}',
                                  ),

                                  const Divider(),
                                  Text(
                                    'Created: ${message.createdAt != null ? DateFormat('dd MMM yyyy HH:mm a').format(message.createdAt!) : "N/A"}',
                                  ),
                                  const Divider(),
                                  Text(
                                    'Delivered: ${message.createdAt != null ? DateFormat('dd MMM yyyy HH:mm a').format(message.createdAt!) : "N/A"}',
                                  ),
                                ],
                              ),
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text('Close'),
                              ),
                            ],
                          ),
                    );
                  }
                },
              ),
            ],
          );
        },
      ),
    ];
  }

  @override
  void initState() {
    setColumns();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SMS Messages'),
        actions: [
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.fetchMessages(),
            tooltip: 'Refresh Messages',
          ),

          // Toggle Dashboard
          Obx(
            () => IconButton(
              onPressed: controller.toggleDashboard,
              icon: Icon(
                controller.showDashboard.value
                    ? IconlyBold.chart
                    : IconlyLight.chart,
              ),
              tooltip:
                  controller.showDashboard.value
                      ? 'Hide Dashboard'
                      : 'Show Dashboard',
            ),
          ),

          // Toggle Filters
          Obx(
            () => IconButton(
              onPressed: controller.toggleFilters,
              icon: Icon(
                controller.showFilters.value
                    ? IconlyBold.filter
                    : IconlyLight.filter,
              ),
              tooltip:
                  controller.showFilters.value
                      ? 'Hide Filters'
                      : 'Show Filters',
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Dashboard section - Toggleable
          Obx(
            () =>
                controller.showDashboard.value
                    ? const SmsStatsWidget()
                    : const SizedBox(),
          ),

          // Filters section - Toggleable
          Obx(
            () =>
                controller.showFilters.value
                    ? const NewSmsFilterWidget()
                    : const SizedBox(),
          ),

          // Messages list with PlutoGrid - Wrapped in Expanded to prevent overflow
          Expanded(
            child: Card(
              color: Theme.of(context).secondaryHeaderColor,
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              elevation: 4,
              child: PlutoGrid(
                mode: PlutoGridMode.selectWithOneTap,
                columns: columns,
                rows: rows,
                onLoaded: (PlutoGridOnLoadedEvent event) {
                  event.stateManager.setShowColumnFilter(true);
                  if (kDebugMode) {
                    debugPrint("onLoaded: $event");
                  }
                },
                onSelected: (event) {
                  if (kDebugMode) {
                    debugPrint("onSelected: $event");
                  }
                },
                configuration: PlutoGridConfiguration(
                  style: PlutoGridStyleConfig(
                    activatedColor: const Color.fromARGB(255, 165, 205, 253),
                    cellTextStyle: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Color.fromARGB(255, 216, 108, 40),
                    ),
                    columnTextStyle: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blueGrey,
                    ),
                  ),
                  columnSize: const PlutoGridColumnSizeConfig(
                    autoSizeMode: PlutoAutoSizeMode.scale,
                    resizeMode: PlutoResizeMode.normal,
                  ),
                ),
                createFooter: (stateManager) {
                  return PlutoLazyPagination(
                    initialPage: 0,
                    initialFetch: true,
                    fetchWithSorting: true,
                    fetchWithFiltering: true,
                    pageSizeToMove: null,
                    fetch: (pagReq) async {
                      controller.currentPage.value = pagReq.page;
                      debugPrint("fetch page: ${pagReq.page}");

                      await controller.fetchMessages();
                      if (controller.errorMessage.isNotEmpty) {
                        ToastUtils.showErrorToast(
                          controller.errorMessage.value,
                          null,
                        );
                      }

                      return Future.value(
                        PlutoLazyPaginationResponse(
                          totalPage: controller.totalPages.value,
                          rows: setupRows(controller.messages),
                        ),
                      );
                    },
                    stateManager: stateManager,
                  );
                },
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.go(Routes.SMS_CREATE);
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
