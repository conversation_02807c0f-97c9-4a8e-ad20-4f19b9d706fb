import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vsc_quill_delta_to_html/vsc_quill_delta_to_html.dart';
import 'package:flutter_quill_delta_from_html/flutter_quill_delta_from_html.dart';

class MarkupEditorWidget extends StatefulWidget {
  final String? initialValue;
  final String? hintText;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final bool readOnly;
  final double? height;
  final String? label;
  final bool isRequired;
  final TextEditingController? controller;

  const MarkupEditorWidget({
    super.key,
    this.initialValue,
    this.hintText,
    this.onChanged,
    this.onSubmitted,
    this.readOnly = false,
    this.height,
    this.label,
    this.isRequired = false,
    this.controller,
  });

  @override
  State<MarkupEditorWidget> createState() => _MarkupEditorWidgetState();
}

class _MarkupEditorWidgetState extends State<MarkupEditorWidget> {
  late QuillController _quillController;
  String _currentContent = '';
  TextEditingController? _externalController;

  @override
  void initState() {
    super.initState();

    // Use external controller if provided, otherwise use initial value
    _externalController = widget.controller;
    _currentContent = _externalController?.text ?? widget.initialValue ?? '';

    // Initialize QuillController with initial content
    Document doc = Document();
    if (_currentContent.isNotEmpty) {
      try {
        // Try to convert HTML to Delta for editing
        final delta = HtmlToDelta().convert(_currentContent);
        doc = Document.fromDelta(delta);
      } catch (e) {
        // If HTML conversion fails, treat as plain text
        doc = Document()..insert(0, _currentContent);
      }
    }

    _quillController = QuillController(
      document: doc,
      selection: const TextSelection.collapsed(offset: 0),
    );

    // Listen for changes
    _quillController.addListener(_onContentChanged);

    // Listen to external controller changes if provided
    if (_externalController != null) {
      _externalController!.addListener(_onExternalControllerChanged);
    }
  }

  @override
  void dispose() {
    _quillController.removeListener(_onContentChanged);
    if (_externalController != null) {
      _externalController!.removeListener(_onExternalControllerChanged);
    }
    _quillController.dispose();
    super.dispose();
  }

  void _onContentChanged() {
    final htmlContent = _convertQuillToHtml();
    _currentContent = htmlContent;

    // Update external controller if provided
    if (_externalController != null) {
      _externalController!.removeListener(_onExternalControllerChanged);
      _externalController!.text = htmlContent;
      _externalController!.addListener(_onExternalControllerChanged);
    }

    if (widget.onChanged != null) {
      widget.onChanged!(htmlContent);
    }
  }

  /// Convert Quill document to HTML using the proper package
  String _convertQuillToHtml() {
    try {
      final delta = _quillController.document.toDelta();
      final converter = QuillDeltaToHtmlConverter(delta.toJson());
      return converter.convert();
    } catch (e) {
      // Fallback to plain text if conversion fails
      return _quillController.document.toPlainText();
    }
  }

  void _onExternalControllerChanged() {
    if (_externalController != null &&
        _externalController!.text != _currentContent) {
      final htmlContent = _externalController!.text;
      _currentContent = htmlContent;

      try {
        // Convert HTML to Delta and update Quill controller
        final delta = HtmlToDelta().convert(htmlContent);
        final doc = Document.fromDelta(delta);
        _quillController.removeListener(_onContentChanged);
        _quillController.document = doc;
        _quillController.addListener(_onContentChanged);
      } catch (e) {
        // If HTML conversion fails, treat as plain text
        final doc = Document()..insert(0, htmlContent);
        _quillController.removeListener(_onContentChanged);
        _quillController.document = doc;
        _quillController.addListener(_onContentChanged);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Row(
            children: [
              Text(
                widget.label!,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              if (widget.isRequired)
                Text(
                  ' *',
                  style: TextStyle(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.bold,
                  ),
                ),
            ],
          ),
          SizedBox(height: 8.h),
        ],
        Container(
          padding: EdgeInsets.all(8),
          height: widget.height ?? 300.h,
          decoration: BoxDecoration(
            color: theme.disabledColor.withAlpha(10),
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(width: 0.4, color: theme.dividerColor),
          ),
          child: Column(
            children: [
              // Responsive toolbar
              _buildResponsiveToolbar(context, isSmallScreen),
              SizedBox(height: 8.h),
              // Rich text editor
              Expanded(child: QuillEditor.basic(controller: _quillController)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildResponsiveToolbar(BuildContext context, bool isSmallScreen) {
    // Primary tools to always show
    final primaryTools = QuillSimpleToolbar(
      controller: _quillController,
      config: const QuillSimpleToolbarConfig(
        showBoldButton: true,
        showItalicButton: true,
        showUnderLineButton: true,
        showStrikeThrough: true,
        showColorButton: true,
        showListBullets: true,
        showListNumbers: true,
        showLink: true,
        showUndo: true,
        showRedo: true,

        // Advanced formatting options
        showInlineCode: true,
        showBackgroundColorButton: true,
        showClearFormat: true,
        showHeaderStyle: true,
        showListCheck: true,
        showCodeBlock: true,
        showQuote: true,
        showAlignmentButtons: true,
        showDirection: true,
        showIndent: true,
        showSubscript: true,
        showSuperscript: true,
        showFontFamily: true,
        showFontSize: true,
        showSearchButton: true,

        // Layout
        showDividers: true,
        multiRowsDisplay: false,
      ),
    );
    return primaryTools;
  }

  /// Get the current content as HTML markup
  Future<String> getHtmlContent() async {
    return _convertQuillToHtml();
  }

  /// Set content from HTML or plain text
  Future<void> setHtmlContent(String content) async {
    try {
      // Convert HTML to Delta and update Quill controller
      final delta = HtmlToDelta().convert(content);
      final document = Document.fromDelta(delta);
      _quillController.removeListener(_onContentChanged);
      _quillController.document = document;
      _quillController.addListener(_onContentChanged);
      _currentContent = content;

      // Update external controller if provided
      if (_externalController != null) {
        _externalController!.removeListener(_onExternalControllerChanged);
        _externalController!.text = content;
        _externalController!.addListener(_onExternalControllerChanged);
      }
    } catch (e) {
      // If HTML conversion fails, treat as plain text
      final document = Document()..insert(0, content);
      _quillController.removeListener(_onContentChanged);
      _quillController.document = document;
      _quillController.addListener(_onContentChanged);
      _currentContent = content;

      // Update external controller if provided
      if (_externalController != null) {
        _externalController!.removeListener(_onExternalControllerChanged);
        _externalController!.text = content;
        _externalController!.addListener(_onExternalControllerChanged);
      }
    }
  }

  /// Get the current content as Delta JSON (for advanced use cases)
  Future<String> getDeltaContent() async {
    return _quillController.document.toDelta().toJson().toString();
  }

  /// Set content from Delta JSON
  Future<void> setDeltaContent(String deltaJson) async {
    try {
      // Parse the JSON string to get the delta operations
      final deltaOps = deltaJson as List;
      final document = Document.fromJson(deltaOps);
      _quillController.removeListener(_onContentChanged);
      _quillController.document = document;
      _quillController.addListener(_onContentChanged);
      _currentContent = _convertQuillToHtml();

      // Update external controller if provided
      if (_externalController != null) {
        _externalController!.removeListener(_onExternalControllerChanged);
        _externalController!.text = _currentContent;
        _externalController!.addListener(_onExternalControllerChanged);
      }
    } catch (e) {
      // If parsing fails, treat as plain text
      await setHtmlContent(deltaJson);
    }
  }

  /// Clear the editor content
  Future<void> clear() async {
    _quillController.clear();
    _currentContent = '';

    // Clear external controller if provided
    if (_externalController != null) {
      _externalController!.removeListener(_onExternalControllerChanged);
      _externalController!.clear();
      _externalController!.addListener(_onExternalControllerChanged);
    }
  }

  /// Check if the editor has content
  Future<bool> hasContent() async {
    return _quillController.document.toPlainText().trim().isNotEmpty;
  }
}
