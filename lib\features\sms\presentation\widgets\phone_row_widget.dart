import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/features/sms/controllers/sms_controller.dart';
import 'package:onechurch/features/sms/presentation/widgets/phone_pasting.dart';
import 'package:onechurch/features/group/controllers/group_controller.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
class PhoneRowWidget extends StatelessWidget {
  const PhoneRowWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SmsController>();
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Paste button
        Container(
          margin: EdgeInsets.symmetric(
            horizontal: context.width * 0.01,
            vertical: 4,
          ),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(4),
          ),
          child: IconButton(
            onPressed: () {
              PhoneNumberProcessor.show(
                context,
                onNumbersProcessed: (List<String> phoneNumbers) {
                  for (var phone in phoneNumbers) {
                    controller.addRecipient(phone);
                  }
                },
              );
            },
            icon: const Icon(Icons.paste, color: Colors.blue),
            tooltip: 'Paste multiple numbers',
          ),
        ),
        // Import from file button with enhanced UI
        Container(
          margin: EdgeInsets.symmetric(
            horizontal: context.width * 0.01,
            vertical: 4,
          ),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(4),
          ),
          child: PopupMenuButton<String>(
            icon: const Icon(Icons.upload_file, color: Colors.green),
            tooltip: 'Import contacts from CSV or Excel file',
            offset: Offset(0, 40),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(color: Colors.grey.shade300),
            ),
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'download',
                    child: Row(
                      children: [
                        Icon(Icons.download, color: Colors.blue),
                        SizedBox(width: 8),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Download Template',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              'Get a properly formatted CSV template',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'import',
                    child: Row(
                      children: [
                        Icon(Icons.upload_file, color: Colors.green),
                        SizedBox(width: 8),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Import Contacts',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              'Upload CSV or Excel file with contacts',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'help',
                    child: Row(
                      children: [
                        Icon(Icons.help_outline, color: Colors.orange),
                        SizedBox(width: 8),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Import Help',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              'Learn how to format your contact file',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
            onSelected: (value) async {
              if (value == 'download') {
                await controller.downloadSampleCsvTemplate(context);
              } else if (value == 'import') {
                await controller.pickFileAndImportContacts(context);
              } else if (value == 'help') {
                showDialog(
                  context: context,
                  builder:
                      (context) => AlertDialog(
                        title: Row(
                          children: [
                            Icon(
                              Icons.help_outline,
                              color: Theme.of(context).primaryColor,
                            ),
                            SizedBox(width: 8),
                            Text('CSV/Excel Import Guide'),
                          ],
                        ),
                        content: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Step-by-step instructions
                              Text(
                                'How to import contacts:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              SizedBox(height: 12),
                              ImportStep(
                                stepNumber: 1,
                                text: 'Download the template file',
                                icon: Icons.download,
                              ),
                              ImportStep(
                                stepNumber: 2,
                                text: 'Fill in your contacts information',
                                icon: Icons.edit,
                              ),
                              ImportStep(
                                stepNumber: 3,
                                text: 'Save the file as CSV or XLSX',
                                icon: Icons.save,
                              ),
                              ImportStep(
                                stepNumber: 4,
                                text: 'Import the file using the upload button',
                                icon: Icons.upload_file,
                              ),

                              SizedBox(height: 20),
                              // File format requirements
                              Text(
                                'File Format Requirements:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              SizedBox(height: 12),
                              Container(
                                padding: EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('• First row must be a header row'),
                                    Text(
                                      '• Phone numbers must be in the 3rd column',
                                    ),
                                    Text(
                                      '• Include country code with phone numbers',
                                    ),
                                    Text('• Example: +254712345678'),
                                    SizedBox(height: 8),
                                    Text(
                                      'Supported file formats:',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    Text('• CSV (Comma Separated Values)'),
                                    Text('• XLSX (Excel Spreadsheet)'),
                                  ],
                                ),
                              ),

                              SizedBox(height: 20),
                              // Example of CSV format
                              Text(
                                'Example CSV Format:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              SizedBox(height: 12),
                              Container(
                                padding: EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withOpacity(0.2),
                                      spreadRadius: 1,
                                      blurRadius: 2,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'First Name,Last Name,Phone Number,Email,Address',
                                      style: TextStyle(
                                        fontFamily: 'monospace',
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Divider(),
                                    Text(
                                      'John,Doe,+254712345678,<EMAIL>,123 Main St',
                                      style: TextStyle(fontFamily: 'monospace'),
                                    ),
                                    Text(
                                      'Jane,Smith,+254723456789,<EMAIL>,456 Oak Ave',
                                      style: TextStyle(fontFamily: 'monospace'),
                                    ),
                                  ],
                                ),
                              ),

                              SizedBox(height: 20),
                              // Tips section
                              Container(
                                padding: EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.blue.shade200,
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.lightbulb,
                                          color: Colors.amber,
                                        ),
                                        SizedBox(width: 8),
                                        Text(
                                          'Tips:',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      '• You can export contacts from most email clients',
                                    ),
                                    Text(
                                      '• Make sure phone numbers are in a consistent format',
                                    ),
                                    Text(
                                      '• Remove any special characters from phone numbers',
                                    ),
                                    Text(
                                      '• The app will automatically skip invalid numbers',
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: Text('Close'),
                          ),
                          CustomButton(
                            icon: Icon(Icons.download),
                            label: Text('Download Template'),
                            onPressed: () async {
                              Navigator.pop(context);
                              await controller.downloadSampleCsvTemplate(
                                context,
                              );
                            },
                          ),
                        ],
                      ),
                );
              }
            },
          ),
        ),
        // Group selection button
        Container(
          margin: EdgeInsets.symmetric(
            horizontal: context.width * 0.01,
            vertical: 4,
          ),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Obx(
            () => Stack(
              children: [
                IconButton(
                  onPressed: () {
                    // Show groups dialog
                    showDialog(
                      context: context,
                      builder:
                          (context) => AlertDialog(
                            title: Row(
                              children: [
                                Icon(
                                  Icons.group,
                                  color: Theme.of(context).primaryColor,
                                ),
                                SizedBox(width: 8),
                                Text('Select Groups'),
                              ],
                            ),
                            content: SizedBox(
                              width: double.maxFinite,
                              child: GetBuilder<GroupController>(
                                init: Get.find<GroupController>(),
                                builder: (groupController) {
                                  return Obx(
                                    () =>
                                        groupController.isLoading.value
                                            ? Center(
                                              child: CircleLoadingAnimation(),
                                            )
                                            : groupController.groups.isEmpty
                                            ? Center(
                                              child: Text(
                                                'No groups available',
                                              ),
                                            )
                                            : Obx(
                                              () => ListView.builder(
                                                shrinkWrap: true,
                                                itemCount:
                                                    groupController
                                                        .groups
                                                        .length,
                                                itemBuilder: (context, index) {
                                                  final group =
                                                      groupController
                                                          .groups[index];
                                                  return Obx(
                                                    () => CheckboxListTile(
                                                      title: Text(
                                                        group.title ??
                                                            'Unknown Group',
                                                      ),
                                                      subtitle: Text(
                                                        '${group.members?.length ?? 0} members',
                                                      ),
                                                      value: controller
                                                          .selectedGroups
                                                          .contains(group.id),
                                                      onChanged: (selected) {
                                                        if (selected == true) {
                                                          controller.addGroup(
                                                            group.id!,
                                                          );
                                                        } else {
                                                          controller
                                                              .removeGroup(
                                                                group.id!,
                                                              );
                                                        }
                                                        // Force UI update
                                                        controller
                                                            .selectedGroups
                                                            .value = List.from(
                                                          controller
                                                              .selectedGroups,
                                                        );
                                                      },
                                                    ),
                                                  );
                                                },
                                              ),
                                            ),
                                  );
                                },
                              ),
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: Text('Close'),
                              ),
                              Obx(
                                () => TextButton.icon(
                                  icon: Icon(Icons.check_circle_outline),
                                  label: Text(
                                    '${controller.selectedGroups.length} Groups Selected',
                                  ),
                                  onPressed: null,
                                ),
                              ),
                            ],
                          ),
                    );
                  },
                  icon: const Icon(Icons.group, color: Colors.purple),
                  tooltip: 'Select groups to add contacts from',
                ),
                if (controller.selectedGroups.isNotEmpty)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: Text(
                        '${controller.selectedGroups.length}',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                if (controller.selectedGroups.isNotEmpty)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: Text(
                        '${controller.selectedGroups.length}',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class ImportStep extends StatelessWidget {
  final IconData icon;
  final String text;
  final int stepNumber;
  const ImportStep({
    super.key,
    required this.icon,
    required this.text,
    required this.stepNumber,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '$stepNumber',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(icon, size: 16, color: Colors.grey.shade700),
                    SizedBox(width: 8),
                    Text(text, style: TextStyle(fontSize: 15)),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build import step with icon and number
}
