import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../controllers/announcement_controller.dart';
import '../../../../data/models/announcement_model.dart';
import '../../../../core/app/constants/routes.dart';
import '../../../../core/app/utils/show_toast.dart';
import 'package:go_router/go_router.dart';
import 'package:simple_html_css/simple_html_css.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
class ViewAnnouncementScreen extends StatefulWidget {
  final String announcementId;
  final AnnouncementModel? initialAnnouncement;

  const ViewAnnouncementScreen({
    super.key,
    required this.announcementId,
    this.initialAnnouncement,
  });

  @override
  State<ViewAnnouncementScreen> createState() => _ViewAnnouncementScreenState();
}

class _ViewAnnouncementScreenState extends State<ViewAnnouncementScreen> {
  final controller = Get.find<AnnouncementController>();
  AnnouncementModel? announcement;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    // Display the passed announcement immediately if available
    if (widget.initialAnnouncement != null) {
      announcement = widget.initialAnnouncement;
      isLoading = false;
      // Optionally fetch fresh data in the background
      _refreshAnnouncement();
    } else {
      // If no initial announcement, fetch it
      _loadAnnouncement();
    }
  }

  Future<void> _loadAnnouncement() async {
    setState(() {
      isLoading = true;
    });

    try {
      final result = await controller.getAnnouncementById(
        widget.announcementId,
      );
      if (result != null) {
        setState(() {
          announcement = result;
        });
      } else {
        ToastUtils.showErrorToast('Failed to load announcement', null);
        if (mounted) {
          context.go(Routes.ANNOUNCEMENTS);
        }
      }
    } catch (e) {
      ToastUtils.showErrorToast('Error: ${e.toString()}', null);
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _refreshAnnouncement() async {
    try {
      final result = await controller.getAnnouncementById(
        widget.announcementId,
      );
      if (result != null && mounted) {
        setState(() {
          announcement = result;
        });
      }
    } catch (e) {
      // Silently fail for background refresh
      debugPrint('Background refresh failed: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('View Announcement'),
        actions: [
          IconButton(
            icon: const Icon(IconlyLight.edit),
            onPressed: () {
              context.go(
                '${Routes.ANNOUNCEMENTS}/${widget.announcementId}/edit',
                extra: announcement,
              );
            },
            tooltip: 'Edit',
          ),
          IconButton(
            icon: const Icon(IconlyLight.delete, color: Colors.red),
            onPressed: () {
              _showDeleteConfirmationDialog();
            },
            tooltip: 'Delete',
          ),
        ],
      ),
      body:
          isLoading
              ? const Center(child: CircleLoadingAnimation())
              : announcement == null
              ? const Center(child: Text('Announcement not found'))
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        announcement!.title ?? 'No Title',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Gap(8.h),
                                      _buildStatusChip(
                                        announcement!.status ?? 'active',
                                      ),
                                    ],
                                  ),
                                ),
                                if (announcement!.media != null &&
                                    announcement!.media!.isNotEmpty)
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Image.network(
                                      announcement!.media!.first.mediaUrl ?? '',
                                      width: 100,
                                      height: 100,
                                      fit: BoxFit.cover,
                                      errorBuilder: (
                                        context,
                                        error,
                                        stackTrace,
                                      ) {
                                        return Container(
                                          width: 100,
                                          height: 100,
                                          color: Colors.grey[300],
                                          child: const Icon(Icons.error),
                                        );
                                      },
                                    ),
                                  ),
                              ],
                            ),
                            Gap(16.h),
                            const Divider(),
                            Gap(16.h),
                            // Display HTML content
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.grey.shade50,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey.shade200),
                              ),
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    announcement!.description ??
                                        'No description',
                                    style: TextStyle(height: 1.5),
                                  ),
                                ],
                              ),
                            ),

                            // Display media items if available
                            if (announcement!.media != null &&
                                announcement!.media!.isNotEmpty) ...[
                              Gap(16.h),
                              Text(
                                'Media',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              Gap(8.h),
                              GridView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 3,
                                      crossAxisSpacing: 8,
                                      mainAxisSpacing: 8,
                                      childAspectRatio: 1,
                                    ),
                                itemCount: announcement!.media!.length,
                                itemBuilder: (context, index) {
                                  final media = announcement!.media![index];
                                  return Card(
                                    clipBehavior: Clip.antiAlias,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Stack(
                                      fit: StackFit.expand,
                                      children: [
                                        Image.network(
                                          media.mediaUrl ?? '',
                                          fit: BoxFit.cover,
                                          errorBuilder: (
                                            context,
                                            error,
                                            stackTrace,
                                          ) {
                                            return Container(
                                              color: Colors.grey[300],
                                              child: const Icon(Icons.error),
                                            );
                                          },
                                        ),
                                        Positioned(
                                          bottom: 0,
                                          left: 0,
                                          right: 0,
                                          child: Container(
                                            color: Colors.black.withOpacity(
                                              0.5,
                                            ),
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 4,
                                            ),
                                            child: Text(
                                              media.title ?? 'Image',
                                              style: const TextStyle(
                                                color: Colors.white,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ],

                            // Display items if available
                            if (announcement!.items != null &&
                                announcement!.items!.isNotEmpty) ...[
                              Gap(16.h),
                              Text(
                                'Items',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              Gap(8.h),
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: announcement!.items!.length,
                                itemBuilder: (context, index) {
                                  final item = announcement!.items![index];
                                  return Card(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    child: ListTile(
                                      leading: CircleAvatar(
                                        backgroundColor: Theme.of(
                                          context,
                                        ).primaryColor.withOpacity(0.1),
                                        child: Text(
                                          '${index + 1}',
                                          style: TextStyle(
                                            color:
                                                Theme.of(context).primaryColor,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      title: Text(
                                        item.title ?? 'Untitled Item',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      subtitle: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          if (item.description != null &&
                                              item.description!.isNotEmpty)
                                            RichText(
                                              text: HTML.toTextSpan(
                                                context,
                                                item.description ?? '',
                                              ),
                                            ),
                                          // Text(
                                          //   item.description!.length > 100
                                          //       ? '${!.substring(0, 100)}...'
                                          //       : item.description!,
                                          //   style: const TextStyle(
                                          //     fontSize: 12,
                                          //   ),
                                          // ),
                                          if (item.category != null &&
                                              item.category!.isNotEmpty)
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                top: 4,
                                              ),
                                              child: Chip(
                                                label: Text(
                                                  item.category!,
                                                  style: const TextStyle(
                                                    fontSize: 10,
                                                  ),
                                                ),
                                                materialTapTargetSize:
                                                    MaterialTapTargetSize
                                                        .shrinkWrap,
                                                visualDensity:
                                                    VisualDensity.compact,
                                              ),
                                            ),
                                        ],
                                      ),
                                      trailing:
                                          item.locationName != null &&
                                                  item.locationName!.isNotEmpty
                                              ? Icon(
                                                IconlyLight.location,
                                                size: 16,
                                                color: Colors.grey[600],
                                              )
                                              : null,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                    Gap(24.h),
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Announcement Details',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Gap(16.h),
                            _buildDetailRow(
                              'Created At',
                              announcement!.createdAt != null
                                  ? DateFormat(
                                    'dd MMM yyyy, HH:mm',
                                  ).format(announcement!.createdAt!)
                                  : 'N/A',
                              IconlyLight.timeCircle,
                            ),
                            _buildDetailRow(
                              'Last Updated',
                              announcement!.updatedAt != null
                                  ? DateFormat(
                                    'dd MMM yyyy, HH:mm',
                                  ).format(announcement!.updatedAt!)
                                  : 'N/A',
                              IconlyLight.edit,
                            ),

                            _buildDetailRow(
                              'Category',
                              announcement!.category ?? 'N/A',
                              IconlyLight.category,
                            ),
                            _buildDetailRow(
                              'Type',
                              announcement!.type ?? 'N/A',
                              IconlyLight.document,
                            ),
                            _buildDetailRow(
                              'Items Count',
                              announcement!.items != null
                                  ? '${announcement!.items!.length}'
                                  : '0',
                              IconlyLight.folder,
                            ),
                            _buildDetailRow(
                              'Created By',
                              announcement!.createdByUser?.firstName != null &&
                                      announcement!.createdByUser?.secondName !=
                                          null
                                  ? '${announcement!.createdByUser!.firstName} ${announcement!.createdByUser!.secondName}'
                                  : announcement!.createdByUser?.firstName ??
                                      announcement!.createdByUser?.username ??
                                      announcement!.createdByUser?.email ??
                                      'Unknown',
                              IconlyLight.profile,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.go(Routes.ANNOUNCEMENTS);
        },
        child: const Icon(Icons.arrow_back),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color backgroundColor;
    Color textColor;

    switch (status.toLowerCase()) {
      case 'active':
        backgroundColor = Colors.green.shade100.withOpacity(0.2);
        textColor = Colors.green;
        break;
      case 'inactive':
        backgroundColor = Colors.grey.shade100.withOpacity(0.2);
        textColor = Colors.grey;
        break;
      case 'draft':
        backgroundColor = Colors.orange.shade100.withOpacity(0.2);
        textColor = Colors.orange;
        break;
      default:
        backgroundColor = Colors.blue.shade100.withOpacity(0.2);
        textColor = Colors.blue;
    }

    return Chip(
      backgroundColor: backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(color: textColor, width: 0.6),
      ),
      label: Text(
        status.toUpperCase(),
        style: TextStyle(color: textColor, fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          Gap(12.w),
          SizedBox(
            width: 120.w,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmationDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirm Delete'),
            content: const Text(
              'Are you sure you want to delete this announcement? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  final success = await controller.deleteAnnouncement(
                    widget.announcementId,
                  );
                  if (success && mounted) {
                    context.go(Routes.ANNOUNCEMENTS);
                  }
                },
                child: const Text(
                  'Delete',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }
}
