                        -HC:\Users\<USER>\Documents\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-<PERSON><PERSON><PERSON>OID_PLATFORM=android-23
-D<PERSON>DROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=C:\Users\<USER>\Documents\sdks\android\ndk\28.0.13004108
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\Documents\sdks\android\ndk\28.0.13004108
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\Documents\sdks\android\ndk\28.0.13004108\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\Documents\sdks\android\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\onechurch v3\john316-flutter-front\build\app\intermediates\cxx\RelWithDebInfo\i6r1s2g2\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\onechurch v3\john316-flutter-front\build\app\intermediates\cxx\RelWithDebInfo\i6r1s2g2\obj\x86_64
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BC:\Users\<USER>\Desktop\onechurch v3\john316-flutter-front\android\app\.cxx\RelWithDebInfo\i6r1s2g2\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2