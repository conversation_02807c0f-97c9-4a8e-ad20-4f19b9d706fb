import 'package:get/get.dart';
import '../controllers/organization_settings_controller.dart';
import '../services/organization_settings_service.dart';

class OrganizationSettingsBinding extends Bindings {
  @override
  void dependencies() {
    // Register the organization settings service if not already registered
    if (!Get.isRegistered<OrganizationSettingsService>()) {
      Get.lazyPut<OrganizationSettingsService>(() => OrganizationSettingsService());
    }

    // Register the organization settings controller
    Get.lazyPut<OrganizationSettingsController>(() => OrganizationSettingsController());
  }
}
