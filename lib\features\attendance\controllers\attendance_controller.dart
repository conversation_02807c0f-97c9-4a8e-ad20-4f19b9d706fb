import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/services/location_service.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';

import '../../../core/app/utils/show_toast.dart';
import '../services/attendance_service.dart';
import '../models/attendance_model.dart';

class AttendanceController extends GetxController {
  final AttendanceService _attendanceService = Get.find<AttendanceService>();
  final logger = Get.find<Logger>();

  // Observables for attendance list
  final RxList<AttendanceModel> attendanceList = <AttendanceModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt pageSize = 10.obs;
  final RxInt totalItems = 0.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;

  // Filter controllers
  final TextEditingController searchController = TextEditingController();
  final TextEditingController locationController = TextEditingController();

  // Filter observables
  final RxString searchFilter = ''.obs;
  final RxString locationFilter = ''.obs;
  final Rx<DateTime?> startDateFilter = Rx<DateTime?>(null);
  final Rx<DateTime?> endDateFilter = Rx<DateTime?>(null);
  final RxString eventIdFilter = ''.obs;

  // Form controllers for marking attendance
  final TextEditingController titleController = TextEditingController();
  final RxString description = ''.obs;
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController attendeeNameFormController =
      TextEditingController();
  final TextEditingController attendeeEmailController = TextEditingController();
  final TextEditingController locationNameController = TextEditingController();
  final RxString phoneNumber = ''.obs;
  final attendeePhoneController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    fetchAttendance();
  }

  @override
  void onClose() {
    // Dispose of controllers
    searchController.dispose();
    locationController.dispose();
    titleController.dispose();
    descriptionController.dispose();
    attendeeNameFormController.dispose();
    attendeeEmailController.dispose();
    locationNameController.dispose();
    super.onClose();
  }

  // Fetch attendance records with pagination and filters
  Future<void> fetchAttendance() async {
    isLoading.value = true;
    try {
      final response = await _attendanceService.fetchAttendance(
        page: currentPage.value,
        size: pageSize.value,
        attendeeName: searchFilter.value.isEmpty ? null : searchFilter.value,
        attendeePhone: searchFilter.value.isEmpty ? null : searchFilter.value,
        timeIn: startDateFilter.value,
        timeOut: endDateFilter.value,
        eventId: eventIdFilter.value.isEmpty ? null : eventIdFilter.value,
      );

      if (response['status'] == true) {
        final data = response['data'];
        final List<dynamic> items = data['items'] ?? [];

        attendanceList.value = _attendanceService.parseAttendance(items);

        // Update pagination info
        currentPage.value = data['page'] ?? 0;
        totalPages.value = data['total_pages'] ?? 0;
        totalItems.value = data['total'] ?? 0;
        isLastPage.value = data['last'] ?? true;
        isFirstPage.value = data['first'] ?? true;
      } else {
        ToastUtils.showErrorToast(
          response['message'] ?? 'Failed to fetch attendance',
          null,
        );
      }
    } catch (e) {
      logger.e('Error fetching attendance: $e');
      ToastUtils.showErrorToast('Error fetching attendance: $e', null);
    } finally {
      isLoading.value = false;
    }
  }

  // Mark attendance
  Future<bool> markAttendance() async {
    isLoading.value = true;
    try {
      // Parse latitude and longitude from LocationService using GetX
      final locationService = Get.find<LocationService>();
      double? lat = locationService.latitude;
      double? lng = locationService.longitude;

      // Create attendance model
      final attendance = AttendanceModel(
        organisationId: Get.find<AuthController>().currentOrg.value?.id,
        title: titleController.text,
        createdByUserId: Get.find<AuthController>().user.value?.id,
        description: description.value,
        timeIn: DateTime.now().toUtc().toIso8601String(),
        timeOut: null,
        locationName: locationNameController.text,
        latitude: lat,
        longitude: lng,
        eventId: eventIdFilter.value.isEmpty ? null : eventIdFilter.value,
        attendeeName: attendeeNameFormController.text,
        attendeeEmail: attendeeEmailController.text,
        attendeePhone: phoneNumber.value,
      );

      logger.d('Marking attendance with event_id: ${attendance.eventId}');
      final response = await _attendanceService.markAttendance(attendance);

      if (response['status'] == true) {
        ToastUtils.showSuccessToast(
          response['message'] ?? 'Attendance marked successfully',
          null,
        );
        clearFormFields();
        await fetchAttendance(); // Refresh the list
        return true;
      } else {
        ToastUtils.showErrorToast(
          response['message'] ?? 'Failed to mark attendance',
          null,
        );
        return false;
      }
    } catch (e) {
      logger.e('Error marking attendance: $e');
      ToastUtils.showErrorToast('Error marking attendance: $e', null);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Mark timeout for attendance
  Future<bool> markTimeout(AttendanceModel attendance) async {
    isLoading.value = true;
    try {
      logger.d('Marking timeout');
      final response = await _attendanceService.markTimeout(attendance);

      if (response['status'] == true) {
        ToastUtils.showSuccessToast(
          response['message'] ?? 'Timeout marked successfully',
          null,
        );
        await fetchAttendance(); // Refresh the list
        return true;
      } else {
        ToastUtils.showErrorToast(
          response['message'] ?? 'Failed to mark timeout',
          null,
        );
        return false;
      }
    } catch (e) {
      logger.e('Error marking timeout: $e');
      ToastUtils.showErrorToast('Error marking timeout: $e', null);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Navigate to next page
  void nextPage() {
    if (!isLastPage.value) {
      currentPage.value++;
      fetchAttendance();
    }
  }

  // Navigate to previous page
  void previousPage() {
    if (!isFirstPage.value) {
      currentPage.value--;
      fetchAttendance();
    }
  }

  // Go to specific page
  void goToPage(int page) {
    if (page >= 0 && page < totalPages.value) {
      currentPage.value = page;
      fetchAttendance();
    }
  }

  // Apply filters
  void applyFilters() {
    currentPage.value = 0; // Reset to first page
    fetchAttendance();
  }

  // Set event filter for filtering attendance by event
  void setEventFilter(String? eventId) {
    eventIdFilter.value = eventId ?? '';
    logger.d('Setting event filter to: ${eventIdFilter.value}');
    currentPage.value = 0;
    fetchAttendance();
  }

  // Clear all filters
  void clearFilters() {
    searchController.clear();
    locationController.clear();

    searchFilter.value = '';
    locationFilter.value = '';
    startDateFilter.value = null;
    endDateFilter.value = null;
    eventIdFilter.value = '';

    currentPage.value = 0; // Reset to first page
    fetchAttendance();
  }

  // Clear form fields
  void clearFormFields() {
    titleController.clear();
    descriptionController.clear();
    attendeeNameFormController.clear();
    attendeeEmailController.clear();
    locationNameController.clear();
  }

  // Format date for display
  String formatDate(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) {
      return 'N/A';
    }
    try {
      final date = DateTime.parse(dateStr);
      return DateFormat('dd MMM yyyy').format(date);
    } catch (e) {
      return dateStr; // Return original string if parsing fails
    }
  }

  // Format date and time for display
  String formatDateTime(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) {
      return 'N/A';
    }
    try {
      final date = DateTime.parse(dateStr);
      return DateFormat('dd MMM yyyy HH:mm').format(date);
    } catch (e) {
      return dateStr; // Return original string if parsing fails
    }
  }
}
