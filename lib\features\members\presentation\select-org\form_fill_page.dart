import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/constants/enums.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/core/app/widgets/custom_dropdown.dart';

class FormFill extends StatefulWidget {
  const FormFill({super.key});

  @override
  State<FormFill> createState() => _FormFillState();

  // Static method to show the form sheet from anywhere
  static Future<Map<String, dynamic>?> showFormSheet(BuildContext context) {
    return showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      isScrollControlled: true,
      builder: (context) => const _FormContent(),
    );
  }
}

class _FormFillState extends State<FormFill> {
  @override
  Widget build(BuildContext context) {
    return CustomButton(
      onPressed: () => FormFill.showFormSheet(context),
      label: const Text('Open Form'),
    );
  }
}

class _FormContent extends StatefulWidget {
  const _FormContent();

  @override
  State<_FormContent> createState() => __FormContentState();
}

class FormDateField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String? Function(String?)? validator;
  final Function() onTap;

  const FormDateField({
    super.key,
    required this.controller,
    required this.label,
    this.validator,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextFormField(
      controller: controller,
      labelText: label,
      validator: validator,
      suffixIcon: Icon(Icons.calendar_today),
      suffixIconAction: onTap,
      onTap: onTap,
      readOnly: true,
    );
  }
}

class AssetOwnershipField extends StatelessWidget {
  final Function(AssetOwnership?) onChanged;
  final AssetOwnership? value;

  const AssetOwnershipField({super.key, required this.onChanged, this.value});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return CustomDropdown<AssetOwnership>(
      labelText: 'Asset Ownership',
      value: value,
      initialValue: AssetOwnership.none,
      prefixIcon: Icon(Icons.home_outlined, color: colorScheme.primary),
      items:
          AssetOwnership.values
              .map(
                (e) => DropdownMenuItem(value: e, child: Text(e.displayName)),
              )
              .toList(),
      onChanged: onChanged,
    );
  }
}

class HouseholdSizeField extends StatelessWidget {
  final Function(String?) onSaved;

  const HouseholdSizeField({super.key, required this.onSaved});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return CustomTextFormField(
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter household size';
        }
        if (int.tryParse(value) == null) {
          return 'Please enter a valid number';
        }
        return null;
      },
      labelText: 'Household Size',
      prefixIcon: Icon(Icons.people_outline, color: colorScheme.primary),

      keyboardType: TextInputType.number,
      onSaved: onSaved,
    );
  }
}

class __FormContentState extends State<_FormContent> {
  final _formKey = GlobalKey<FormState>();
  final Map<String, dynamic> formData = {};
  EmploymentStatus? employmentStatus;
  Disability? disability;
  MaritalStatus? maritalStatus;
  EducationLevel? educationLevel;
  IncomeBracket? incomeBracket;
  AssetOwnership? assetOwnership;
  TextEditingController baptismDateController = TextEditingController();
  TextEditingController joinDateController = TextEditingController();

  final List<String> kenyanEducationLevels = [
    'PRIMARY',
    'SECONDARY',
    'DIPLOMA',
    'DEGREE',
    'POSTGRADUATE',
  ];

  @override
  void dispose() {
    baptismDateController.dispose();
    joinDateController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(
    BuildContext context,
    TextEditingController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        controller.text = DateFormat('yyyy-MM-dd').format(picked);
      });
    }
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      // Add date values to formData
      formData['baptism_date'] = baptismDateController.text;
      formData['join_date'] = joinDateController.text;
      // Add disability value to formData
      formData['disability'] = disability;
      // Add employment status to formData
      formData['employment_status'] = employmentStatus;

      Navigator.of(context).pop(formData);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: BoxDecoration(
          color: theme.scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Column(
          children: [
            // Form Header
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: Center(
                child: Text(
                  'Member Information',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

            // Form Content
            Expanded(
              child: Form(
                key: _formKey,
                child: Stack(
                  children: [
                    SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      padding: const EdgeInsets.fromLTRB(
                        24,
                        24,
                        24,
                        80,
                      ), // Added bottom padding for button
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Date Fields Section
                          Text(
                            'Important Dates',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          FormDateField(
                            controller: baptismDateController,
                            label: 'Baptism Date',
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please select baptism date';
                              }
                              return null;
                            },
                            onTap:
                                () =>
                                    _selectDate(context, baptismDateController),
                          ),
                          const SizedBox(height: 16),
                          FormDateField(
                            controller: joinDateController,
                            label: 'Join Date',
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please select join date';
                              }
                              return null;
                            },
                            onTap:
                                () => _selectDate(context, joinDateController),
                          ),
                          const SizedBox(height: 32),

                          // Personal Information Section
                          Text(
                            'Personal Information',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          CustomTextFormField(
                            labelText: 'Address',
                            prefixIcon: Icon(Icons.location_on_outlined),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter address';
                              }
                              return null;
                            },
                            onSaved: (v) => formData['address'] = v,
                          ),
                          const SizedBox(height: 16),
                          CustomDropdown<MaritalStatus>(
                            labelText: 'Marital Status',
                            value: maritalStatus,
                            initialValue: MaritalStatus.single,
                            prefixIcon: Icon(
                              Icons.favorite_outline,
                              color: colorScheme.primary,
                            ),
                            items:
                                MaritalStatus.values
                                    .map(
                                      (e) => DropdownMenuItem(
                                        value: e,
                                        child: Text(e.displayName),
                                      ),
                                    )
                                    .toList(),
                            validator: (value) {
                              if (value == null) {
                                return 'Please select marital status';
                              }
                              return null;
                            },
                            onChanged: (v) {
                              setState(() {
                                maritalStatus = v;
                                formData['marital_status'] = v?.displayName;
                              });
                            },
                          ),
                          const SizedBox(height: 32),

                          // Education & Employment Section
                          Text(
                            'Education & Employment',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          CustomDropdown<EducationLevel>(
                            labelText: 'Education Level',
                            value: educationLevel,
                            initialValue: EducationLevel.primary,
                            prefixIcon: Icon(
                              Icons.school_outlined,
                              color: colorScheme.primary,
                            ),
                            items:
                                EducationLevel.values
                                    .map(
                                      (e) => DropdownMenuItem(
                                        value: e,
                                        child: Text(e.displayName),
                                      ),
                                    )
                                    .toList(),
                            validator: (value) {
                              if (value == null) {
                                return 'Please select education level';
                              }
                              return null;
                            },
                            onChanged: (v) {
                              setState(() {
                                educationLevel = v;
                                formData['education_level'] = v?.displayName;
                              });
                            },
                          ),
                          const SizedBox(height: 16),
                          CustomDropdown<EmploymentStatus>(
                            labelText: 'Employment Status',
                            value: employmentStatus,
                            initialValue: EmploymentStatus.unemployed,
                            prefixIcon: Icon(
                              Icons.work_outline,
                              color: colorScheme.primary,
                            ),
                            items:
                                EmploymentStatus.values
                                    .map(
                                      (e) => DropdownMenuItem(
                                        value: e,
                                        child: Text(e.displayName),
                                      ),
                                    )
                                    .toList(),
                            validator: (value) {
                              if (value == null) {
                                return 'Please select employment status';
                              }
                              return null;
                            },
                            onChanged: (v) {
                              setState(() {
                                employmentStatus = v;
                                formData['employment_status'] = v?.displayName;
                              });
                            },
                          ),
                          if (employmentStatus ==
                              EmploymentStatus.employed) ...[
                            const SizedBox(height: 16),
                            CustomDropdown<IncomeBracket>(
                              labelText: 'Income Bracket',
                              value: incomeBracket,
                              initialValue: IncomeBracket.lessThan10k,
                              prefixIcon: Icon(
                                Icons.attach_money,
                                color: colorScheme.primary,
                              ),
                              items:
                                  IncomeBracket.values
                                      .map(
                                        (e) => DropdownMenuItem(
                                          value: e,
                                          child: Text(e.displayName),
                                        ),
                                      )
                                      .toList(),
                              validator: (value) {
                                if (employmentStatus ==
                                        EmploymentStatus.employed &&
                                    value == null) {
                                  return 'Please select income bracket';
                                }
                                return null;
                              },
                              onChanged: (v) {
                                setState(() {
                                  incomeBracket = v;
                                  formData['income_bracket'] = v?.displayName;
                                });
                              },
                            ),
                          ],
                          const SizedBox(height: 32),

                          // Additional Information Section
                          Text(
                            'Additional Information',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          CustomDropdown<Disability>(
                            labelText: 'Disability',
                            value: disability,
                            initialValue: Disability.none,
                            prefixIcon: Icon(
                              Icons.accessible_outlined,
                              color: colorScheme.primary,
                            ),
                            items:
                                Disability.values
                                    .map(
                                      (e) => DropdownMenuItem(
                                        value: e,
                                        child: Text(e.displayName),
                                      ),
                                    )
                                    .toList(),
                            validator: (value) {
                              if (value == null) {
                                return 'Please select disability status';
                              }
                              return null;
                            },
                            onChanged: (v) {
                              setState(() {
                                disability = v;
                                formData['disability'] = v?.displayName;
                              });
                            },
                          ),
                          if (disability == Disability.other) ...[
                            const SizedBox(height: 16),
                            CustomTextFormField(
                              validator: (value) {
                                if (disability == Disability.other &&
                                    (value == null || value.isEmpty)) {
                                  return 'Please describe the disability';
                                }
                                return null;
                              },
                              labelText: 'Describe Disability',
                              prefixIcon: Icon(
                                Icons.description_outlined,
                                color: colorScheme.primary,
                              ),

                              onSaved:
                                  (v) => formData['disability_description'] = v,
                            ),
                          ],
                          const SizedBox(height: 16),
                          HouseholdSizeField(
                            onSaved:
                                (v) =>
                                    formData['household_size'] = int.tryParse(
                                      v ?? '0',
                                    ),
                          ),
                          const SizedBox(height: 16),
                          AssetOwnershipField(
                            value: assetOwnership,
                            onChanged: (v) {
                              setState(() {
                                assetOwnership = v;
                                formData['asset_ownership'] = v?.displayName;
                              });
                            },
                          ),
                          const SizedBox(height: 100),
                        ],
                      ),
                    ),

                    // Persistent Submit Button
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 0,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 16,
                        ),
                        decoration: BoxDecoration(
                          color: theme.scaffoldBackgroundColor,
                          boxShadow: [
                            BoxShadow(
                              color: colorScheme.shadow.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, -2),
                            ),
                          ],
                        ),
                        child: SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: CustomButton(
                            onPressed: _submitForm,
                           label: const Text(
                              'SUBMIT',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
