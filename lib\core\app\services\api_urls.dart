/// HTTP Method enum to indicate the type of request
enum HttpMethod { get, post, put, delete }

class ApiUrls {
  static const String baseUrl = 'https://churchdev.salticon.com/v1/';

  /// [Group]
  static const String groups = 'group/groups/';
  static const String groupById = 'group/group/';
  static const String createGroup = 'group/group/';
  static const String updateGroup = 'group/group/';
  static const String deleteGroup = 'group/group/';
  static const String groupMembers = 'group/group-members/';
  static const String groupMembersById = 'group/group-members/';

  /// [Events]
  static const String events = 'event/events/';
  static const String eventById = 'event/event/';
  static const String createEvent = 'event/event/';
  static const String updateEvent = 'event/event/';
  static const String deleteEvent = 'event/event/';
  static const String eventMembers = 'event/event-members/';

  /// [Auth]
  static const String register = 'auth/register/admin';
  static const String confirmOtp = 'auth/confirm-otp/';
  static const String login = 'auth/login/';
  static const String setPin = 'auth/set-password/';
  static const String forgotPassword = 'auth/forgot-password-request/';
  static const String forgotPasswordConfirm = 'auth/forgot-password-confirm/';
  static const String resetPassword = 'auth/reset-password/';
  static const String verifyOtp = 'auth/verify-otp/';

  /// [Member]
  ///
  static const String memberCategories = 'member/category/';
  static const String createCustomerCategory = 'member/category/';
  static const String onboardCustomer = 'member/members/';
  static const String getCustomerCategory = 'member/category/';
  static const String updateCustomerCategory = 'member/category/';
  static const String deleteCustomerCategory = 'member/category/';
  static const String getMembers = 'member/members/';
  static const String getSingleMember = 'member/member/';
  static const String createMemberAccount = 'member/account/';

  /// append this json param {"title": "Eldery member","description": "Eldery Members are the above 70 years","category": "FAMILY","organisation_id": null,"is_general": true}
  static const createMemberRelationshipType = 'member/relationship-types/';

  static const getMemberRelationshipType = 'member/relationship-types/';

  /// append relationshiptype to update. eg. member/relationship-type/
  static const updateMemberRelationshipType = 'member/relationship-types/';

  /// append id to delete. eg. member/relationship-type/123
  static const deleteMemberRelationshipType = 'member/relationship-types/';

  /// append these json to params {"from_member_id": "123e4567-e89b-12d3-a456-************", //uuid "to_member_id": "123e4567-e89b-12d3-a456-************", "relationship_type_id": "123e4567-e89b-12d3-a456-************" }
  static const createMemberRelationship = 'member/relationship/';

  /// append these get. eg. member/relationship/?relationship_type=123&from_member=123&to_member=123
  static const getMemberRelationship = 'member/relationship/';

  /// append memberRelationshipObject to update
  static const updateMemberRelationship = 'member/relationship/';

  /// append id to delete. eg. member/relationship/123
  static const deleteMemberRelationship = 'member/relationship/';

  /// [staffs]
  /// requires jsonparams phone_number, first_name, second_name , code(StaffNumber/IdNumber), email, id_number, assignedLocations([]), role(get from enums), is_maker(true), is_checker(false), is_signatory(true/false)
  static const String onBoardStaff = 'organisation/staff/';

  /// filter by identifier(phone_number, email), role, id_number, code.
  static const String getStaff = 'organisation/staff/';

  /// requires jsonparams phone_number, first_name, second_name , code(StaffNumber/IdNumber), email, id_number, assignedLocations([]), role(get from enums), is_maker(true), is_checker(false), is_signatory(true/false)
  static const String updateStaff = 'organisation/staff/';

  /// append id. eg organisation/staff/123
  static const String deleteStaff = 'organisation/staff/';

  /// append id. eg organisation/organisation-staff/123
  static const String getStaffById = 'organisation/organisation-staff/';

  /// can append ?phone_number=123&email=<EMAIL>&id_number=123&code=123
  static const String getOrgWhereStaff = 'organisation/organisation-staff/';

  /// [staffs_rolls_and_permissions]
  /// Append the following data to the body: { "name": "TREASURER", "description": "This is a treasurer role", "organisation_id": "d5c0b3c4-15d2-4b0d-a3a4-7c9e3cfeebc9",  "permissions": [{ "id": "4e5e5f5f-6f6f-7f7f-8f8f-9f9f0f0f0f0f", "code": "VIEW_FINANCES", "name": "View Finances", "description": "This permission allows the user to view the organisation finances" }, { "id": "1e2e3e4e-5e5e-6e6e-7e7e-8e8e9e9e9e9e", "code": "EDIT_FINANCES", "name": "Edit Finances", "description": "This permission allows the user to edit the organisation finances" } ]}
  static const String createStaffRole = 'organisation/role/';

  /// Append the following data to the body: { "name": "TREASURER", "description": "This is a treasurer role", "organisation_id": "d5c0b3c4-15d2-4b0d-a3a4-7c9e3cfeebc9",  "permissions": [{ "id": "4e5e5f5f-6f6f-7f7f-8f8f-9f9f0f0f0f0f", "code": "VIEW_FINANCES", "name": "View Finances", "description": "This permission allows the user to view the organisation finances" }, { "id": "1e2e3e4e-5e5e-6e6e-7e7e-8e8e9e9e9e9e", "code": "EDIT_FINANCES", "name": "Edit Finances", "description": "This permission allows the user to edit the organisation finances" } ]}
  static const String updateStaffRole = 'organisation/roles/';

  /// Append id. eg organization/role/123
  static const String deleteStaffRole = 'organisation/roles/';

  /// Append id. eg organization/role/123
  static const String getStaffRoles = 'organisation/roles/';

  /// Append the following data to the body: { "role_id": "123", "permission_id": "456" }
  static const String addPermissionToRole = 'organisation/roles/';

  /// post request. Append the following data to the body: { "role_id": "123", "permission_id": "456" }
  static const String removePermissionToRole = 'organisation/role-permission/';

  /// post request. Append the following data to the body: { "role_id": "123", "staff_id": "456" , assigned_at:  "2022-01-01T00:00:00Z", //(can be now or later), "description": "Assignment of role",  "expires_at": "2023-01-01T00:00:00Z" // nullable}
  static const String addStaffToRole = 'organisation/role-permission/';

  /// Append id. eg organization/staff-role-assignement/?staff_id={uuid}
  static const String getStaffRoleAssignment =
      'organisation/staff-role-permission/';

  /// Append follwing data { "id": "123e4567-e89b-12d3-a456-************", "staff_id": "123e4567-e89b-12d3-a456-************", "role_id": "123e4567-e89b-12d3-a456-************", "assigned_at": "2022-01-01T00:00:00Z", //can be  assigned the role now or later "description": "Assignment of role", "is_active": false, "expires_at": "2023-01-01T00:00:00Z" // nullable }
  static const String updateStaffToRole = 'organisation/staff-role-permission/';

  /// nothing to append
  static const String getAllPermissions = 'organisation/permissions/';

  /// [General]
  static const String getEnums = 'auth/enums/';

  /// [Locations]
  static const String createLocations = 'organizations/organization/';
  static const String getLocations = 'organization/location/';
  static const String updateLocations = 'organization/location/';
  static const String createOrganizationLocations =
      'organization/org-location/';
  static const String getOrganizationLocations = 'organization/org-location/';
  static const String deleteOrganizationLocations =
      'organization/org-location/';

  /// [Media]
  static const String uploadFile = 'media/upload/';
  static const String deleteMedia = 'media/delete/';
  static const String getMedia = 'media/media/';

  /// [Notification]
  static const String sendPushNotification =
      'notification/send-general-notification/';
  static const String sendSmsNotification = 'notification/sms-preview/';
  static const String sendSmsNotificationActual = 'notification/sms-actual/';
  static const String getOrganisationSms = 'notification/sms/';
  static const String getOrganisationSmsRequests = 'notification/sms/';
  static const String getGeneralNotification =
      'notification/general-notifications/';
  static const String getNotificationById = 'notification/';
  static const String readNotificationUpdate =
      'notification/read-notification/';
  //   // Notification endpoints
  static const String notifications = '$baseUrl/notifications';

  /// [User]
  static const String createAddress = 'user/address/';
  static const String deleteAddress = 'user/address/';
  static const String updateAddress = 'user/address/';
  static const String getAddress = 'user/address/';
  static const String getUsersAdmin = 'user/admin/';
  static const String getUserById = 'user/';

  /// [Sermons]
  static const String sermons = 'sermon/sermons/';
  static const String createSermon = 'sermon/sermon/';
  static const String sermonById = 'sermon/sermon/';

  /// [Hymns]
  static const getHymns = 'sermon/hymns/';

  /// include organisation_id, search, page, size,start_date, end_date, created_by
  static const getHymnbyID = 'sermon/hymn/';

  /// include id eg. sermon/hymn/12
  static const createHymn = 'sermon/hymn/';

  /// include organisation_id
  static const updateHymn = 'sermon/hymn/';

  /// include id and organisation_id
  static const deleteHymn = 'sermon/hymn/';

  /// include id
  /// [Hymns-Categories]
  static const getHymnsCategory = 'sermon/hymn-categories/';
  static const createHymnCategory = 'sermon/hymn-category/';
  static const updateHymnCategory = 'sermon/hymn-category/';
  static const deleteHymnCategory = 'sermon/hymn-category/';
  static const createHymnCategoryRelationship =
      'sermon/hymn-category-relationship/';
  static const removeHymnCategoryRelationship =
      'sermon/hymn-category-relationship/';

  /// [Announcements]
  static const String announcements = 'announcement/announcements/';
  static const String announcementById = 'announcement/announcement/';
  static const String createAnnouncement = 'announcement/announcements/';
  static const String updateAnnouncement = 'announcement/announcements/';
  static const String deleteAnnouncement = 'announcement/announcements/';

  /// [Super Organisation]
  static const String createSuperOrg = 'super-organisation/super-organisation';
  static const String createPackages = 'super-organisation/package/';

  /// [Payments]
  static const String subAccounts = 'payment/sub-accounts/';
  static const String subAcct = 'payment/sub-account/';
  static const String subAccountsCategories = "payment/sub-account-categories/";
  static const String createSubAccountCategory =
      "payment/sub-account-category/";

  /// [Transactions]
  static const String transactions = 'payment/transactions/';

  /// [ATTENDANCE]
  /// append these in params {"organisation_id": "","member_id": "f250bf53-51c9-404b-b337-ad42c42bef0e","event_id": "e389ddcf-df71-44f1-aad4-5c8b2dab0346","time_in": "2020-01-01T00:00:00Z","time_out": "2020-01-01T00:00:00Z","title": "Sample Event","description": "Sample Event Description","attendee_phone": "************","attendee_name": "Jane Doe","attendee_email": "<EMAIL>","location_name": "Nairobi, chapel","latitude":null,"longitude":2.21,}
  static const markAttendance = 'member/attendance/';

  /// get all attendace heres full url: member/attendance/?page=0&size=10&member_id=f250bf53-51c9-404b-b337-ad42c42bef0e&event_id=e389ddcf-df71-44f1-aad4-5c8b2dab0346&time_in=2020-01-01&time_out=2020-01-01
  static const getAttendance = 'member/attendance/';

  /// [INVENTORY]
  /// append this example [ { "title": "Plastic Chair", "category": "Furniture", "unit_of_measure_id": "e7d5b0c5-8c4f-4b47-8f1d-8f3d5b0c5", "barcode": "1234567890", "description": "A plastic chair", "organisation_id": "9b5d5b0c-8c4f-4b47-8f1d-8f3d5b0c5", "media": [ { "id": "5b0c-8c4f-4b47-8f1d-8f3d5b0c5", "url": "https://example.com/media/1234567890.jpg", "type": "image" } ], } ]
  static const String createInventoryItem = 'inventory/item/';

  /// can be filtered by search,start_date, end_date, category, id. use it as inventory/item/?search=Plastic&start_date=`toUtc`&end_date=`.toUtc`&category=Furniture&id=`for getting specific item`
  static const String getInventoryItems = 'inventory/items/';

  /// append id. eg. inventory/item/123
  static const String getInventoryItemById = 'inventory/item/';

  /// should look something like this:  { "ID":1, "title": "Group Title", "description": "Group Detscription", "start_date":"2024-05-12T13:00", "end_date":"2024-05-12T13:00", "status": "active", "organisation_id": 1  }
  static const String updateInventoryItem = 'inventory/item/';

  /// append id. eg. inventory/item/123
  static const String deleteInventoryItem = 'inventory/invetory/';

  /// can be filtered by : member_id, id, item_id, inventory_type eg. inventory/inventories/?member_id=123&id=123&item_id=123&inventory_type=IN
  static const String getInventories = 'inventory/inventories/';

  /// Here's an example:  [ {  "member_id": "uuid-value", "organisation_id": "uuid-value", "inventory_item_id": "uuid-value", "quantity": 100, "condition": "New", "inventory_type": "Donation", "received_at": "2023-10-01T10:00:00Z", "expiry_date": "2024-10-01T10:00:00Z", "batch_no": "Batch123", "estimated_value": 500.00, "purchase_date": "2023-09-01T10:00:00Z", "cost": 300.00, "warranty_expiry": "2025-09-01T10:00:00Z", "notes": "Sample note", "media": [ {   "url": "https://example.com/media/123.jpg", "type": "image" } ], "is_anonymous": false, "full_names": "John Doe",   "email": "<EMAIL>//( not a must if member is found), "phone_number": "1234567890//( not a must if member is found), "county": "Sample County", "city": "Sample City", "address": "123 Sample Street", } ]
  static const String recordInventory = 'inventory/inventory/';

  /// Here's whats to be sent:  [ {  "member_id": "uuid-value", "organisation_id": "uuid-value", "inventory_item_id": "uuid-value", "quantity": 100, "condition": "New", "inventory_type": "Donation", "received_at": "2023-10-01T10:00:00Z", "expiry_date": "2024-10-01T10:00:00Z", "batch_no": "Batch123", "estimated_value": 500.00, "purchase_date": "2023-09-01T10:00:00Z", "cost": 300.00, "warranty_expiry": "2025-09-01T10:00:00Z", "notes": "Sample note", "media": [ {   "url": "https://example.com/media/123.jpg", "type": "image" } ], "is_anonymous": false, "full_names": "John Doe",   "email": "<EMAIL>//( not a must if member is found), "phone_number": "1234567890//( not a must if member is found), "county": "Sample County", "city": "Sample City", "address": "123 Sample Street", } ]
  static const String updateInventory = 'inventory/inventory/';

  /// append id. eg. inventory/inventory/123
  static const String deleteInventory = 'inventory/inventory/';

  /// append id. eg. inventory/inventory/123
  static const String getInventoryById = 'inventory/inventory/';

  /// [INVENTORY-CATEGORIES]
  /// Get all inventory item categories with pagination and search
  static const String inventoryItemCategories = 'inventory/item-categories/';

  /// Create a new inventory item category
  static const String createInventoryItemCategory =
      'inventory/item-categories/';

  /// Update an existing inventory item category
  static const String updateInventoryItemCategory =
      'inventory/item-categories/';

  /// Delete an inventory item category
  static const String deleteInventoryItemCategory =
      'inventory/item-categories/';


  /// [Organisation] 
  static const getOrganisation = 'organisation/organisation/';  
  static const updateOrganisation = 'organisation/org-accounts/';
  static const getOrganisationAccounts = 'organisation/org-accounts/';  

  
}
