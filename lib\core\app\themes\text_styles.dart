import 'package:flutter/material.dart';
import 'app_theme.dart';

class AppTextStyles {
  // Gets the appropriate font size based on screen width
  static double _getResponsiveSize(double mobileSize, {double? tabletSize, double? desktopSize}) {
    // Use MediaQuery to check screen width
    final width = MediaQueryData.fromView(WidgetsBinding.instance.window).size.width;
    final isTablet = width >= 768 && width < 1024;
    final isDesktop = width >= 1024;
    
    if (isDesktop && desktopSize != null) {
      return desktopSize;
    } else if (isTablet && tabletSize != null) {
      return tabletSize;
    } else {
      return mobileSize;
    }
  }

  // Light Theme Text Styles
  static TextStyle get displayLargeLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(57, tabletSize: 60, desktopSize: 64),
        fontWeight: FontWeight.w400,
        color: AppTheme.secondaryColor,
        letterSpacing: -0.25,
      );

  static TextStyle get displayMediumLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(45, tabletSize: 48, desktopSize: 52),
        fontWeight: FontWeight.w400,
        color: AppTheme.secondaryColor,
      );

  static TextStyle get displaySmallLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(36, tabletSize: 38, desktopSize: 40),
        fontWeight: FontWeight.w400,
        color: AppTheme.secondaryColor,
      );

  static TextStyle get headlineLargeLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(28, tabletSize: 30, desktopSize: 32),
        fontWeight: FontWeight.w600,
        color: AppTheme.secondaryColor,
      );

  static TextStyle get headlineMediumLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(20, tabletSize: 24, desktopSize: 26),
        fontWeight: FontWeight.w600,
        color: AppTheme.secondaryColor,
      );

  static TextStyle get headlineSmallLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(18, tabletSize: 20, desktopSize: 22),
        fontWeight: FontWeight.w600,
        color: AppTheme.secondaryColor,
      );

  static TextStyle get titleLargeLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(16, tabletSize: 18, desktopSize: 20),
        fontWeight: FontWeight.w600,
        color: AppTheme.secondaryColor,
      );

  static TextStyle get titleMediumLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(14, tabletSize: 15, desktopSize: 16),
        fontWeight: FontWeight.w600,
        color: AppTheme.secondaryColor,
        letterSpacing: 0.15,
      );

  static TextStyle get titleSmallLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(12, tabletSize: 13, desktopSize: 14),
        fontWeight: FontWeight.w500,
        color: AppTheme.secondaryColor,
        letterSpacing: 0.1,
      );

  static TextStyle get bodyLargeLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(14, tabletSize: 15, desktopSize: 16),
        fontWeight: FontWeight.w400,
        color: AppTheme.secondaryColor,
        letterSpacing: 0.5,
      );

  static TextStyle get bodyMediumLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(12, tabletSize: 13, desktopSize: 14),
        fontWeight: FontWeight.w400,
        color: AppTheme.secondaryColor,
        letterSpacing: 0.25,
      );

  static TextStyle get bodySmallLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(10, tabletSize: 11, desktopSize: 12),
        fontWeight: FontWeight.w400,
        color: AppTheme.secondaryColor,
        letterSpacing: 0.4,
      );

  static TextStyle get labelLargeLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(12, tabletSize: 13, desktopSize: 14),
        fontWeight: FontWeight.w500,
        color: AppTheme.secondaryColor,
        letterSpacing: 0.1,
      );

  static TextStyle get labelMediumLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(10, tabletSize: 11, desktopSize: 12),
        fontWeight: FontWeight.w500,
        color: AppTheme.secondaryColor,
        letterSpacing: 0.5,
      );

  static TextStyle get labelSmallLight => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(8, tabletSize: 9, desktopSize: 10),
        fontWeight: FontWeight.w500,
        color: AppTheme.secondaryColor,
        letterSpacing: 0.5,
      );

  // Dark Theme Text Styles
  static TextStyle get displayLargeDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(57, tabletSize: 60, desktopSize: 64),
        fontWeight: FontWeight.w400,
        color: Colors.white,
        letterSpacing: -0.25,
      );

  static TextStyle get displayMediumDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(45, tabletSize: 48, desktopSize: 52),
        fontWeight: FontWeight.w400,
        color: Colors.white,
      );

  static TextStyle get displaySmallDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(36, tabletSize: 38, desktopSize: 40),
        fontWeight: FontWeight.w400,
        color: Colors.white,
      );

  static TextStyle get headlineLargeDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(28, tabletSize: 30, desktopSize: 32),
        fontWeight: FontWeight.w600,
        color: Colors.white,
      );

  static TextStyle get headlineMediumDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(20, tabletSize: 24, desktopSize: 26),
        fontWeight: FontWeight.w600,
        color: Colors.white,
      );

  static TextStyle get headlineSmallDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(18, tabletSize: 20, desktopSize: 22),
        fontWeight: FontWeight.w600,
        color: Colors.white,
      );

  static TextStyle get titleLargeDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(16, tabletSize: 18, desktopSize: 20),
        fontWeight: FontWeight.w600,
        color: AppTheme.accentColorDark,
      );

  static TextStyle get titleMediumDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(14, tabletSize: 15, desktopSize: 16),
        fontWeight: FontWeight.w600,
        color: AppTheme.accentColorDark,
        letterSpacing: 0.15,
      );

  static TextStyle get titleSmallDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(12, tabletSize: 13, desktopSize: 14),
        fontWeight: FontWeight.w500,
        color: AppTheme.accentColorDark,
        letterSpacing: 0.1,
      );

  static TextStyle get bodyLargeDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(14, tabletSize: 15, desktopSize: 16),
        fontWeight: FontWeight.w400,
        color: Colors.white,
        letterSpacing: 0.5,
      );

  static TextStyle get bodyMediumDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(12, tabletSize: 13, desktopSize: 14),
        fontWeight: FontWeight.w400,
        color: Colors.white,
        letterSpacing: 0.25,
      );

  static TextStyle get bodySmallDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(10, tabletSize: 11, desktopSize: 12),
        fontWeight: FontWeight.w400,
        color: Colors.white,
        letterSpacing: 0.4,
      );

  static TextStyle get labelLargeDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(12, tabletSize: 13, desktopSize: 14),
        fontWeight: FontWeight.w500,
        color: Colors.white,
        letterSpacing: 0.1,
      );

  static TextStyle get labelMediumDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(10, tabletSize: 11, desktopSize: 12),
        fontWeight: FontWeight.w500,
        color: Colors.white,
        letterSpacing: 0.5,
      );

  static TextStyle get labelSmallDark => TextStyle(
        fontFamily: 'Alata',
        fontSize: _getResponsiveSize(8, tabletSize: 9, desktopSize: 10),
        fontWeight: FontWeight.w500,
        color: Colors.white,
        letterSpacing: 0.5,
      );

  // Helper method to get text theme for light theme
  static TextTheme getLightTextTheme() {
    return TextTheme(
      displayLarge: displayLargeLight,
      displayMedium: displayMediumLight,
      displaySmall: displaySmallLight,
      headlineLarge: headlineLargeLight,
      headlineMedium: headlineMediumLight,
      headlineSmall: headlineSmallLight,
      titleLarge: titleLargeLight,
      titleMedium: titleMediumLight,
      titleSmall: titleSmallLight,
      bodyLarge: bodyLargeLight,
      bodyMedium: bodyMediumLight,
      bodySmall: bodySmallLight,
      labelLarge: labelLargeLight,
      labelMedium: labelMediumLight,
      labelSmall: labelSmallLight,
    );
  }

  // Helper method to get text theme for dark theme
  static TextTheme getDarkTextTheme() {
    return TextTheme(
      displayLarge: displayLargeDark,
      displayMedium: displayMediumDark,
      displaySmall: displaySmallDark,
      headlineLarge: headlineLargeDark,
      headlineMedium: headlineMediumDark,
      headlineSmall: headlineSmallDark,
      titleLarge: titleLargeDark,
      titleMedium: titleMediumDark,
      titleSmall: titleSmallDark,
      bodyLarge: bodyLargeDark,
      bodyMedium: bodyMediumDark,
      bodySmall: bodySmallDark,
      labelLarge: labelLargeDark,
      labelMedium: labelMediumDark,
      labelSmall: labelSmallDark,
    );
  }
}
