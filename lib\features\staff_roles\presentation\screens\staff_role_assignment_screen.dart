import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/features/staff_roles/models/staff_role_model.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:intl/intl.dart';
import '../../controllers/staff_role_assignment_controller.dart';
import '../../controllers/staff_roles_controller.dart';
import '../../../staff/models/staff_model.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';

class StaffRoleAssignmentScreen extends StatefulWidget {
  final StaffModel staff;

  const StaffRoleAssignmentScreen({super.key, required this.staff});

  @override
  State<StaffRoleAssignmentScreen> createState() =>
      _StaffRoleAssignmentScreenState();
}

class _StaffRoleAssignmentScreenState extends State<StaffRoleAssignmentScreen> {
  final StaffRoleAssignmentController controller =
      Get.find<StaffRoleAssignmentController>();
  final StaffRolesController rolesController = Get.find<StaffRolesController>();
  late PlutoGridStateManager stateManager;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    controller.setSelectedStaff(widget.staff);
    rolesController.fetchRoles();
  }

  List<PlutoColumn> columns() {
    return [
      PlutoColumn(
        title: 'Role',
        field: 'role_id',
        type: PlutoColumnType.text(),
        width: 150,
        renderer: (rendererContext) {
          final roleId = rendererContext.cell.value as String;
          final role = rolesController.roles.firstWhere(
            (role) => role.id == roleId,
            orElse: () => StaffRoleModel(),
          );
          return Text(role.name ?? 'Unknown Role');
        },
      ),
      PlutoColumn(
        title: 'Assigned At',
        field: 'assigned_at',
        type: PlutoColumnType.date(),
        width: 150,
        renderer: (rendererContext) {
          final date = rendererContext.cell.value as DateTime?;
          return Text(
            date != null ? DateFormat('yyyy-MM-dd').format(date) : 'N/A',
          );
        },
      ),
      PlutoColumn(
        title: 'Expires At',
        field: 'expires_at',
        type: PlutoColumnType.date(),
        width: 150,
        renderer: (rendererContext) {
          final date = rendererContext.cell.value as DateTime?;
          return Text(
            date != null ? DateFormat('yyyy-MM-dd').format(date) : 'Never',
          );
        },
      ),
      PlutoColumn(
        title: 'Status',
        field: 'is_active',
        type: PlutoColumnType.text(),
        width: 100,
        renderer: (rendererContext) {
          final isActive = rendererContext.cell.value as bool;
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isActive ? Colors.green.shade100 : Colors.red.shade100,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              isActive ? 'Active' : 'Inactive',
              style: TextStyle(
                color: isActive ? Colors.green.shade800 : Colors.red.shade800,
                fontWeight: FontWeight.bold,
              ),
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        width: 200,
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 150,
        renderer: (rendererContext) {
          final assignmentId = rendererContext.cell.value as String;
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: const Icon(Icons.edit, color: Colors.blue),
                onPressed: () {
                  final assignment = controller.assignments.firstWhere(
                    (a) => a.id == assignmentId,
                  );
                  controller.setSelectedAssignment(assignment);
                  _showAssignmentDialog(context, true);
                },
              ),
              IconButton(
                icon: const Icon(Icons.toggle_off, color: Colors.orange),
                onPressed: () {
                  final assignment = controller.assignments.firstWhere(
                    (a) => a.id == assignmentId,
                  );
                  controller.setSelectedAssignment(assignment);
                  controller.isActive.value = !controller.isActive.value;
                  controller.updateStaffRoleAssignment();
                },
              ),
            ],
          );
        },
      ),
    ];
  }

  List<PlutoRow> rows() {
    return controller.assignments.map((assignment) {
      return PlutoRow(
        cells: {
          'role_id': PlutoCell(value: assignment.roleId),
          'assigned_at': PlutoCell(value: assignment.assignedAt),
          'expires_at': PlutoCell(value: assignment.expiresAt),
          'is_active': PlutoCell(value: assignment.isActive ?? false),
          'description': PlutoCell(value: assignment.description),
          'actions': PlutoCell(value: assignment.id),
        },
      );
    }).toList();
  }

  void _showAssignmentDialog(BuildContext context, bool isEdit) {
    if (!isEdit) {
      controller.resetForm();
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isEdit ? 'Edit Role Assignment' : 'Assign New Role'),
          content: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (!isEdit)
                    Obx(
                      () => DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Select Role',
                          border: OutlineInputBorder(),
                        ),
                        value: controller.selectedRole.value?.id,
                        items:
                            rolesController.roles
                                .map(
                                  (role) => DropdownMenuItem<String>(
                                    value: role.id,
                                    child: Text(role.name ?? 'Unknown'),
                                  ),
                                )
                                .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            final role = rolesController.roles.firstWhere(
                              (r) => r.id == value,
                            );
                            controller.setSelectedRole(role);
                          }
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a role';
                          }
                          return null;
                        },
                      ),
                    ),
                  const SizedBox(height: 16),
                  CustomTextFormField(
                    controller: controller.descriptionController,
                    labelText: 'Description',
                    maxLines: 2,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a description';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  Obx(
                    () => InkWell(
                      onTap: () async {
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: controller.assignedAtDate.value,
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) {
                          controller.assignedAtDate.value = picked;
                        }
                      },
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'Assigned At',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          DateFormat(
                            'yyyy-MM-dd',
                          ).format(controller.assignedAtDate.value),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Obx(
                    () => InkWell(
                      onTap: () async {
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate:
                              controller.expiresAtDate.value ??
                              DateTime.now().add(const Duration(days: 365)),
                          firstDate: DateTime.now(),
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) {
                          controller.expiresAtDate.value = picked;
                        }
                      },
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: 'Expires At (Optional)',
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              controller.expiresAtDate.value = null;
                            },
                          ),
                        ),
                        child: Text(
                          controller.expiresAtDate.value != null
                              ? DateFormat(
                                'yyyy-MM-dd',
                              ).format(controller.expiresAtDate.value!)
                              : 'Never',
                        ),
                      ),
                    ),
                  ),
                  if (isEdit)
                    Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Obx(
                        () => SwitchListTile(
                          title: const Text('Active'),
                          value: controller.isActive.value,
                          onChanged: (value) {
                            controller.isActive.value = value;
                          },
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            Obx(
              ()=> CustomButton(
                onPressed: () async {
                  if (_formKey.currentState!.validate()) {
                    bool success;
                    if (isEdit) {
                      success = await controller.updateStaffRoleAssignment();
                    } else {
                      success = await controller.assignRoleToStaff();
                    }
              
                    if (success) {
                      Navigator.of(context).pop();
                      ToastUtils.showSuccessToast(
                        'Success',
                        controller.successMessage.value,
                      );
                    } else {
                      ToastUtils.showErrorToast(
                        'Error',
                        controller.errorMessage.value,
                      );
                    }
                  }
                },
                  isLoading: controller.isAssigning.value || controller.isUpdating.value,
                label:  Text(isEdit ? 'Update' : 'Assign'),
                
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Role Assignments - ${widget.staff.firstName} ${widget.staff.secondName}',
        ),
      ),
      body: Obx(
        () =>
            controller.isLoading.value
                ? const Center(child: CircleLoadingAnimation())
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text(
                                    'Staff Role Assignments',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  CustomButton(
                                    onPressed: () {
                                      _showAssignmentDialog(context, false);
                                    },
                                    icon: const Icon(Icons.add),
                                    label: const Text('Assign New Role'),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              SizedBox(
                                height:
                                    MediaQuery.of(context).size.height * 0.6,
                                child: Obx(
                                  () =>
                                      controller.assignments.isEmpty
                                          ? const Center(
                                            child: Text(
                                              'No role assignments found for this staff member.',
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontStyle: FontStyle.italic,
                                              ),
                                            ),
                                          )
                                          : PlutoGrid(
                                            columns: columns(),
                                            rows: rows(),
                                            onLoaded: (
                                              PlutoGridOnLoadedEvent event,
                                            ) {
                                              stateManager = event.stateManager;
                                            },
                                            configuration: PlutoGridConfiguration(
                                              columnFilter:
                                                  PlutoGridColumnFilterConfig(
                                                    filters: [
                                                      ...FilterHelper
                                                          .defaultFilters,
                                                    ],
                                                    resolveDefaultColumnFilter: (
                                                      column,
                                                      resolver,
                                                    ) {
                                                      return resolver<
                                                            PlutoFilterTypeContains
                                                          >()
                                                          as PlutoFilterType;
                                                    },
                                                  ),
                                            ),
                                          ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
      ),
    );
  }
}
