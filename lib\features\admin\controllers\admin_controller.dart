import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/app/constants/routes.dart';
import '../../dashboard/presentation/screens/dashboard_screen.dart';
import '../../hymns/presentation/screens/hymns_screen.dart';
import '../../members/presentation/screens/members_screen/members_screen.dart';
import '../../events/presentation/screens/events_screen.dart';
import '../../events/presentation/admin/create_event.dart';
import '../../members/presentation/screens/member_cateories/member_categories_screen.dart';
import '../../announcements/presentation/screens/announcements_screen.dart';
import '../../sermons/presentation/screens/sermon_screens.dart';
import '../../sermons/presentation/screens/create_sermon_screen.dart';
import '../../group/presentation/view_groups.dart';
import '../../group/presentation/screens/create_group_screen.dart';
import '../../finances/presentation/admin/main_finance.dart';
import '../../finances/presentation/screens/account_categories_screen.dart';
import '../../inventory/presentation/screens/inventory_dashboard_screen.dart';
import '../../inventory/presentation/screens/inventory_items_screen.dart';
import '../../inventory/presentation/screens/inventory_records_screen.dart';
import '../../inventory/presentation/screens/inventory_categories/inventory_categories_screen.dart';
import '../../communications/presentation/screens/communications_screen.dart';
import '../../staff/presentation/screens/staff_screen.dart';
import '../../staff/presentation/screens/create_staff_screen.dart';
import '../../staff_roles/presentation/screens/staff_roles_screen.dart';
import '../../attendance/presentation/screens/attendance_screen.dart';
import '../../profile/presentation/screens/profile_screen.dart';
import '../../organization_settings/presentation/screens/organization_settings_screen.dart';

class AdminController extends GetxController {
  RxString pagtitle = 'Home'.obs;
  Rx<Widget> selectedPage = const DashboardScreen().obs;
  RxString selectedRoute = Routes.DASHBOARD.obs;

  @override
  void onInit() {
    super.onInit();
    selectedPage.value = const DashboardScreen();
  }

  void updatePage(String route, String title) {
    pagtitle.value = title;
    selectedRoute.value = route;

    // Update the selected page based on the route
    switch (route) {
      case Routes.DASHBOARD:
        selectedPage.value = const DashboardScreen();
        break;
      case Routes.HYMNS:
        selectedPage.value = const HymnsScreen();
        break;
      case Routes.MEMBERS:
        selectedPage.value = const MemberViewScreen();
        break;
      case Routes.EVENTS:
        selectedPage.value = const EventsScreen();
        break;
      case Routes.EVENT_CREATE:
        selectedPage.value = const CreateEventScreen();
        break;
      case Routes.MEMBER_CATEGORIES:
        selectedPage.value = const MemberCategoriesScreen();
        break;
      case Routes.ANNOUNCEMENTS:
        selectedPage.value = const AnnouncementsScreen();
        break;
      case Routes.SERMONS:
        selectedPage.value = const ViewSermonsGrid();
        break;
      case Routes.SERMON_CREATE:
        selectedPage.value = const CreateSermonScreen();
        break;
      case Routes.GROUPS:
        selectedPage.value = const ViewGroupsGrid();
        break;
      case Routes.CREATE_GROUP:
        selectedPage.value = const CreateGroupScreen();
        break;
      case Routes.FINANCE_ADMIN:
        selectedPage.value = const SubAccountsView();
        break;
      case Routes.ACCOUNT_CATEGORIES:
        selectedPage.value = const AccountCategoriesScreen();
        break;
      case Routes.INVENTORY:
        selectedPage.value = const InventoryDashboardScreen();
        break;
      case Routes.INVENTORY_ITEMS:
        selectedPage.value = const InventoryItemsScreen();
        break;
      case Routes.INVENTORY_RECORDS:
        selectedPage.value = const InventoryRecordsScreen();
        break;
      case Routes.INVENTORY_CATEGORIES:
        selectedPage.value = const InventoryCategoriesScreen();
        break;
      case Routes.COMMUNICATIONS:
        selectedPage.value = const CommunicationsScreen();
        break;
      case Routes.STAFF:
        selectedPage.value = const StaffScreen();
        break;
      case Routes.CREATE_STAFF:
        selectedPage.value = const CreateStaffScreen();
        break;
      case Routes.STAFF_ROLES:
        selectedPage.value = const StaffRolesScreen();
        break;
      case Routes.ATTENDANCE:
        selectedPage.value = const AttendanceScreen();
        break;
      case Routes.PROFILE:
        selectedPage.value = const ProfileScreen();
        break;
      case Routes.SETTINGS:
        selectedPage.value =
            const ProfileScreen(); // Using ProfileScreen for settings for now
        break;
      case Routes.ORGANIZATION_SETTINGS:
        selectedPage.value = const OrganizationSettingsScreen();
        break;
      default:
        selectedPage.value = const DashboardScreen();
    }
  }
}
