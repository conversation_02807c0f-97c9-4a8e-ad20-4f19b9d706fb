import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../auth/controllers/auth_controller.dart';

class GivingService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();
  final authController = Get.find<AuthController>();

  // Initialize the service
  GivingService() {
    _httpService.initializeDio();
  }

  // Fetch giving records with pagination
  Future<Map<String, dynamic>> fetchGivingRecords({
    required int page,
    required int size,
    String? givingType,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Build URL with query parameters
      String url = "${ApiUrls.baseUrl}/giving?";

      url += "page=$page";
      url += "&size=$size";

      // Add organisation_id parameter
      final organisationId = authController.currentOrg.value?.id;
      if (organisationId != null) {
        url += "&organisation_id=$organisationId";
      }

      // Add optional filters to URL
      if (givingType != null && givingType.isNotEmpty) {
        url += "&giving_type=${Uri.encodeComponent(givingType)}";
      }

      if (startDate != null) {
        final formattedStart = startDate.toUtc().toIso8601String();
        url += "&start_date=${Uri.encodeComponent(formattedStart)}";
      }

      if (endDate != null) {
        final formattedEnd = endDate.toUtc().toIso8601String();
        url += "&end_date=${Uri.encodeComponent(formattedEnd)}";
      }

      logger.i('Fetching giving records with URL: $url');
      final response = await _httpService.request(url: url, method: Method.GET);

      return response.data;
    } catch (e) {
      logger.e('Error fetching giving records: $e');
      return {
        'status': false,
        'message': 'Failed to fetch giving records: $e',
        'data': null,
      };
    }
  }

  // Submit a new giving record
  Future<Map<String, dynamic>> submitGiving({
    required double amount,
    required String givingType,
    String? notes,
  }) async {
    try {
      final Map<String, dynamic> givingData = {
        'amount': amount,
        'giving_type': givingType,
        'organisation_id': authController.currentOrg.value?.id,
        'member_id': authController.user.value?.id,
      };

      if (notes != null && notes.isNotEmpty) {
        givingData['notes'] = notes;
      }

      logger.d('Submitting giving with payload: $givingData');

      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/giving',
        method: Method.POST,
        params: givingData,
      );

      return response.data;
    } catch (e) {
      logger.e('Error submitting giving: $e');
      return {
        'status': false,
        'message': 'Failed to submit giving: $e',
        'data': null,
      };
    }
  }

  // Get giving statistics
  Future<Map<String, dynamic>> getGivingStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      String url = "${ApiUrls.baseUrl}/giving/statistics?";

      // Add organisation_id parameter
      final organisationId = authController.currentOrg.value?.id;
      if (organisationId != null) {
        url += "organisation_id=$organisationId";
      }

      if (startDate != null) {
        final formattedStart = startDate.toUtc().toIso8601String();
        url += "&start_date=${Uri.encodeComponent(formattedStart)}";
      }

      if (endDate != null) {
        final formattedEnd = endDate.toUtc().toIso8601String();
        url += "&end_date=${Uri.encodeComponent(formattedEnd)}";
      }

      final response = await _httpService.request(url: url, method: Method.GET);

      return response.data;
    } catch (e) {
      logger.e('Error fetching giving statistics: $e');
      return {
        'status': false,
        'message': 'Failed to fetch statistics: $e',
        'data': null,
      };
    }
  }
}
