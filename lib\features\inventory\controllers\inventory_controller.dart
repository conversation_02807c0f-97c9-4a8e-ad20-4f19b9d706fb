import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../auth/controllers/auth_controller.dart';
import '../models/inventory_record_model.dart';
import '../services/inventory_service.dart';

class InventoryController extends GetxController {
  final InventoryService _inventoryService = InventoryService();
  final Logger logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<InventoryRecordModel> inventoryRecords =
      <InventoryRecordModel>[].obs;
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalItems = 0.obs;
  final RxInt pageSize = 10.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;

  // Filter variables
  final RxString searchQuery = ''.obs;
  final RxString memberIdFilter = ''.obs;
  final RxString itemIdFilter = ''.obs;
  final RxString inventoryTypeFilter = ''.obs;
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  // Controllers
  final TextEditingController searchController = TextEditingController();
  final TextEditingController memberIdController = TextEditingController();
  final TextEditingController itemIdController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    fetchInventoryRecords();
  }

  @override
  void onClose() {
    searchController.dispose();
    memberIdController.dispose();
    itemIdController.dispose();
    super.onClose();
  }

  // Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    currentPage.value = 0;
  }

  // Set member filter
  void setMemberFilter(String memberId) {
    memberIdFilter.value = memberId;
    memberIdController.text = memberId;
    currentPage.value = 0;
  }

  // Set item filter
  void setItemFilter(String itemId) {
    itemIdFilter.value = itemId;
    itemIdController.text = itemId;
    currentPage.value = 0;
  }

  // Set inventory type filter
  void setInventoryTypeFilter(String type) {
    inventoryTypeFilter.value = type;
    currentPage.value = 0;
  }

  // Set date range
  void setDateRange(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0;
  }

  // Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    memberIdFilter.value = '';
    itemIdFilter.value = '';
    inventoryTypeFilter.value = '';
    startDate.value = null;
    endDate.value = null;
    searchController.clear();
    memberIdController.clear();
    itemIdController.clear();
    currentPage.value = 0;
    fetchInventoryRecords();
  }

  // Fetch inventory records with pagination and filters
  Future<void> fetchInventoryRecords() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _inventoryService.fetchInventoryRecords(
        page: currentPage.value,
        size: pageSize.value,
        searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
        memberId: memberIdFilter.value.isEmpty ? null : memberIdFilter.value,
        itemId: itemIdFilter.value.isEmpty ? null : itemIdFilter.value,
        inventoryType:
            inventoryTypeFilter.value.isEmpty
                ? null
                : inventoryTypeFilter.value,
        startDate: startDate.value,
        endDate: endDate.value,
        organisationId: Get.find<AuthController>().currentOrg.value?.id,
      );

      if (response["status"] ?? false) {
        final data = response["data"];

        // Handle nested data structure
        final innerData = data["data"] ?? data;

        if (innerData is List) {
          inventoryRecords.value =
              innerData
                  .map((item) => InventoryRecordModel.fromJson(item))
                  .toList();
        } else if (innerData is Map && innerData.containsKey("data")) {
          final recordsList = innerData["data"] as List;
          inventoryRecords.value =
              recordsList
                  .map((item) => InventoryRecordModel.fromJson(item))
                  .toList();

          // Update pagination info
          totalPages.value = innerData["total_pages"] ?? 0;
          totalItems.value = innerData["total_items"] ?? 0;
          isLastPage.value = currentPage.value >= (totalPages.value - 1);
          isFirstPage.value = currentPage.value == 0;
        }
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to fetch inventory records';
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to fetch inventory records: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
  }

  // Get inventory record by ID
  Future<InventoryRecordModel?> getInventoryRecordById(String id) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _inventoryService.getInventoryRecordById(id);

      if (response["status"] ?? false) {
        final data = response["data"];
        return InventoryRecordModel.fromJson(data);
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to fetch inventory record';
        return null;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to fetch inventory record: ${e.toString()}';
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // Record new inventory
  Future<bool> recordInventory({
    required String memberId,
    required String inventoryItemId,
    required int quantity,
    required String condition,
    required String inventoryType,
    required DateTime receivedAt,
    DateTime? expiryDate,
    String? batchNo,
    double? estimatedValue,
    DateTime? purchaseDate,
    double? cost,
    DateTime? warrantyExpiry,
    String? notes,
    List<Map<String, dynamic>>? media,
    bool isAnonymous = false,
    String? fullNames,
    String? email,
    String? phoneNumber,
    String? county,
    String? city,
    String? address,
  }) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _inventoryService.recordInventory(
        memberId: memberId,
        organisationId: Get.find<AuthController>().currentOrg.value?.id ?? '',
        inventoryItemId: inventoryItemId,
        quantity: quantity,
        condition: condition,
        inventoryType: inventoryType,
        receivedAt: receivedAt,
        expiryDate: expiryDate,
        batchNo: batchNo,
        estimatedValue: estimatedValue,
        purchaseDate: purchaseDate,
        cost: cost,
        warrantyExpiry: warrantyExpiry,
        notes: notes,
        media: media,
        isAnonymous: isAnonymous,
        fullNames: fullNames,
        email: email,
        phoneNumber: phoneNumber,
        county: county,
        city: city,
        address: address,
      );

      if (response["status"] ?? false) {
        await fetchInventoryRecords(); // Refresh the list
        return true;
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to record inventory';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to record inventory: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Update inventory record
  Future<bool> updateInventoryRecord({
    required String id,
    required String memberId,
    required String inventoryItemId,
    required int quantity,
    required String condition,
    required String inventoryType,
    required DateTime receivedAt,
    DateTime? expiryDate,
    String? batchNo,
    double? estimatedValue,
    DateTime? purchaseDate,
    double? cost,
    DateTime? warrantyExpiry,
    String? notes,
    List<Map<String, dynamic>>? media,
    bool isAnonymous = false,
    String? fullNames,
    String? email,
    String? phoneNumber,
    String? county,
    String? city,
    String? address,
  }) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _inventoryService.updateInventoryRecord(
        id: id,
        memberId: memberId,
        organisationId: Get.find<AuthController>().currentOrg.value?.id ?? '',
        inventoryItemId: inventoryItemId,
        quantity: quantity,
        condition: condition,
        inventoryType: inventoryType,
        receivedAt: receivedAt,
        expiryDate: expiryDate,
        batchNo: batchNo,
        estimatedValue: estimatedValue,
        purchaseDate: purchaseDate,
        cost: cost,
        warrantyExpiry: warrantyExpiry,
        notes: notes,
        media: media,
        isAnonymous: isAnonymous,
        fullNames: fullNames,
        email: email,
        phoneNumber: phoneNumber,
        county: county,
        city: city,
        address: address,
      );

      if (response["status"] ?? false) {
        await fetchInventoryRecords(); // Refresh the list
        return true;
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to update inventory record';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to update inventory record: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Delete inventory record
  Future<bool> deleteInventoryRecord(String id) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _inventoryService.deleteInventoryRecord(id);

      if (response["status"] ?? false) {
        await fetchInventoryRecords(); // Refresh the list
        return true;
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to delete inventory record';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to delete inventory record: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Pagination methods
  void nextPage() {
    if (!isLastPage.value) {
      currentPage.value++;
      fetchInventoryRecords();
    }
  }

  void previousPage() {
    if (!isFirstPage.value) {
      currentPage.value--;
      fetchInventoryRecords();
    }
  }

  void goToPage(int page) {
    if (page >= 0 && page < totalPages.value) {
      currentPage.value = page;
      fetchInventoryRecords();
    }
  }
}
