import 'package:currency_formatter/currency_formatter.dart';

class FormattedCurrency {
  static getFormattedCurrency(dynamic balance, {String? currency}) {
    {
      CurrencyFormat kenyanSettings = CurrencyFormat(
        symbol: currency ?? "KES",
        symbolSide: SymbolSide.left,
        thousandSeparator: ',',
        decimalSeparator: '.',
      );
      String formatted = CurrencyFormatter.format(balance ?? 0, kenyanSettings);
      return formatted;
    }
  }
}
