import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../../../core/app/constants/routes.dart';
import '../../../core/app/utils/show_toast.dart';
import '../../media_upload/models/media_model.dart';
import '../models/unit_of_measure_model.dart';
import '../models/inventory_category_model.dart';
import 'inventory_item_controller.dart';

class CreateInventoryItemController extends GetxController {
  // Dependencies
  final InventoryItemController _inventoryItemController =
      Get.find<InventoryItemController>();

  // Form controllers
  late final TextEditingController titleController;
  late final TextEditingController barcodeController;
  late final TextEditingController descriptionController;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // Reactive state variables
  final RxString selectedCategory = RxString('');
  final RxString selectedUnitOfMeasure = RxString('');
  final RxBool isLoading = false.obs;
  final RxList<MediaModel> mediaItems = <MediaModel>[].obs;
  final RxList<UnitOfMeasureModel> unitsOfMeasure = <UnitOfMeasureModel>[].obs;

  // Categories - get from inventory item controller
  List<InventoryCategory> get categories => _inventoryItemController.categories;

  @override
  void onInit() {
    super.onInit();
    _initializeControllers();
    _loadUnitsOfMeasure();
  }

  @override
  void onClose() {
    titleController.dispose();
    barcodeController.dispose();
    descriptionController.dispose();
    super.onClose();
  }

  void _initializeControllers() {
    titleController = TextEditingController();
    barcodeController = TextEditingController();
    descriptionController = TextEditingController();
  }

  void _loadUnitsOfMeasure() {
    unitsOfMeasure.value = CommonUnitsOfMeasure.getUnitsFromEnums();
  }

  void setSelectedCategory(String? category) {
    selectedCategory.value = category ?? '';
  }

  void setSelectedUnitOfMeasure(String? unitOfMeasure) {
    selectedUnitOfMeasure.value = unitOfMeasure ?? '';
  }

  void setMediaItems(List<MediaModel> media) {
    mediaItems.value = media;
  }

  void removeMediaItem(int index) {
    if (index >= 0 && index < mediaItems.length) {
      mediaItems.removeAt(index);
    }
  }

  void clearForm() {
    titleController.clear();
    barcodeController.clear();
    descriptionController.clear();
    selectedCategory.value = '';
    selectedUnitOfMeasure.value = '';
    mediaItems.clear();
  }

  Future<void> submitForm(BuildContext context) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (selectedCategory.value.isEmpty) {
      ToastUtils.showErrorToast('Please select a category', null);
      return;
    }

    if (selectedUnitOfMeasure.value.isEmpty) {
      ToastUtils.showErrorToast('Please select a unit of measure', null);
      return;
    }

    isLoading.value = true;

    try {
      final success = await _inventoryItemController.createInventoryItem(
        title: titleController.text.trim(),
        categoryId: selectedCategory.value, // Now sending category ID
        unitOfMeasureId: selectedUnitOfMeasure.value,
        barcode:
            barcodeController.text.trim().isEmpty
                ? null
                : barcodeController.text.trim(),
        description:
            descriptionController.text.trim().isEmpty
                ? null
                : descriptionController.text.trim(),
        media:
            mediaItems.isNotEmpty
                ? mediaItems.map((media) => media.toJson()).toList()
                : null,
      );

      if (success) {
        ToastUtils.showSuccessToast(
          'Inventory item created successfully',
          null,
        );
        context.go(Routes.INVENTORY);
      } else {
        ToastUtils.showErrorToast(
          _inventoryItemController.errorMessage.value,
          null,
        );
      }
    } catch (e) {
      ToastUtils.showErrorToast('Failed to create inventory item: $e', null);
    } finally {
      isLoading.value = false;
    }
  }

  // Validation helpers
  String? validateTitle(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Please enter an item title';
    }
    return null;
  }

  String? validateCategory(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please select a category';
    }
    return null;
  }

  String? validateUnitOfMeasure(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please select a unit of measure';
    }
    return null;
  }
}
