import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';

import 'package:toastification/toastification.dart';

enum ToastType { error, success, information, normal }

class ToastUtils {
  static void showToast(
    String message, {
    String? description,
    ToastType toastType = ToastType.normal,
  }) {
    if (toastType == ToastType.error) {
      showErrorToast(message, description);
    } else if (toastType == ToastType.success) {
      showSuccessToast(message, description);
    } else {
      showInfoToast(message, description);
    }
    // Fluttertoast.showToast(
    //   msg: message,
    //   toastLength: Toast.LENGTH_LONG,
    //   gravity: ToastGravity.TOP,
    //   timeInSecForIosWeb: 5,
    //   backgroundColor: _getToastBgColor(toastType),
    //   textColor: Colors.white,
    //   fontSize: 14.0,
    // );
  }

  static void showSuccessToast(String message, String? desc) async {
    toastification.show(
      title: Text(message),
      description: desc != null ? Text(desc) : null,
      type: ToastificationType.success,
      autoCloseDuration: const Duration(seconds: 3),
      applyBlurEffect: true,
      showProgressBar: true,
      style: ToastificationStyle.fillColored,
      showIcon: true,
      pauseOnHover: true,
      icon: const Icon(Icons.check_circle_outline),
    );
    // await Fluttertoast.showToast(
    //   msg: message,
    //   toastLength: Toast.LENGTH_LONG,
    //   gravity: ToastGravity.TOP,
    //   timeInSecForIosWeb: 5,
    //   backgroundColor: _getToastBgColor(ToastType.success),
    //   textColor: Colors.white,
    //   fontSize: 14.0,
    // );
    // CherryToast.success(
    //   title: Text(message),
    //   // backgroundColor: _getToastBgColor(ToastType.success),
    //   autoDismiss: true,
    //   toastPosition: toastPosition ?? Position.top,
    //   description: Text(desc ?? ''),
    // ).show(context);
  }

  /* display error */
  static void showErrorToast(
    String message,
    String? desc, {
    bool autoDismiss = true,
  }) {
    toastification.show(
      title: Text(message),
      description: desc != null ? Text(desc) : null,
      type: ToastificationType.error,
      autoCloseDuration: const Duration(seconds: 3),
      applyBlurEffect: true,
      showProgressBar: true,
      style: ToastificationStyle.fillColored,
      showIcon: true,
      pauseOnHover: true,
      icon: Icon(Icons.error_outline_rounded),
    );
    // showToast("$message : ${desc ?? ""}", toastType: ToastType.error);
    // CherryToast.error(
    //   title: Text(message),
    //   autoDismiss: autoDismiss,
    //   description: Text(desc ?? ''),
    // ).show(context);
  }

  static void showInfoToast(String message, String? desc) {
    toastification.show(
      title: Text(message),
      description: desc != null ? Text(desc) : null,
      type: ToastificationType.info,
      autoCloseDuration: const Duration(seconds: 3),
      applyBlurEffect: true,
      showProgressBar: true,
      style: ToastificationStyle.fillColored,
      showIcon: true,
      pauseOnHover: true,
      icon: Icon(IconlyLight.paper),
    );

    // CherryToast.info(
    //   title: Text(message),
    //   autoDismiss: true,
    //   description: Text(desc ?? ''),
    // ).show(context);
  }
}
