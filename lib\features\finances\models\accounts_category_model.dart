// To parse this JSON data, do
//
//     final accountsCategory = accountsCategoryFromJson(jsonString);

import 'dart:convert';

import 'package:onechurch/data/models/user_model.dart';

List<AccountsCategory> accountsCategoryFromJson(String str) =>
    List<AccountsCategory>.from(
      json.decode(str).map((x) => AccountsCategory.fromJson(x)),
    );

String accountsCategoryToJson(List<AccountsCategory> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AccountsCategory {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final String? title;
  final String? description;
  final String? username;
  final bool? isPinned;
  final int? orderNumber;
  final Category? category;
  final String? categoryId;
  final String? account;
  final String? organisationId;
  final dynamic organisation;
  final String? status;
  final int? createdByUserId;
  final UserModel? createdByUser;

  AccountsCategory({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.description,
    this.username,
    this.isPinned,
    this.orderNumber,
    this.category,
    this.categoryId,
    this.account,
    this.organisationId,
    this.organisation,
    this.status,
    this.createdByUserId,
    this.createdByUser,
  });

  AccountsCategory copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    String? title,
    String? description,
    String? username,
    bool? isPinned,
    int? orderNumber,
    Category? category,
    String? categoryId,
    String? account,
    String? organisationId,
    dynamic organisation,
    String? status,
    int? createdByUserId,
    UserModel? createdByUser,
  }) => AccountsCategory(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    title: title ?? this.title,
    description: description ?? this.description,
    username: username ?? this.username,
    isPinned: isPinned ?? this.isPinned,
    orderNumber: orderNumber ?? this.orderNumber,
    category: category ?? this.category,
    categoryId: categoryId ?? this.categoryId,
    account: account ?? this.account,
    organisationId: organisationId ?? this.organisationId,
    organisation: organisation ?? this.organisation,
    status: status ?? this.status,
    createdByUserId: createdByUserId ?? this.createdByUserId,
    createdByUser: createdByUser ?? this.createdByUser,
  );

  factory AccountsCategory.fromJson(
    Map<String, dynamic> json,
  ) => AccountsCategory(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    title: json["title"],
    description: json["description"],
    username: json["username"],
    isPinned: json["is_pinned"],
    orderNumber: json["order_number"],
    category:
        json["category"] == null ? null : Category.fromJson(json["category"]),
    categoryId: json["category_id"],
    account: json["account"],
    organisationId: json["organisation_id"],
    organisation: json["organisation"],
    status: json["status"],
    createdByUserId: json["created_by_user_id"],
    createdByUser:
        json["created_by_user"] == null
            ? null
            : UserModel.fromJson(json["created_by_user"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "title": title,
    "description": description,
    "username": username,
    "is_pinned": isPinned,
    "order_number": orderNumber,
    "category": category?.toJson(),
    "category_id": categoryId,
    "account": account,
    "organisation_id": organisationId,
    "organisation": organisation,
    "status": status,
    "created_by_user_id": createdByUserId,
    "created_by_user": createdByUser?.toJson(),
  };
}

class Category {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final String? description;
  final String? organisationId;
  final dynamic organisation;
  final bool? isGeneral;

  Category({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.description,
    this.organisationId,
    this.organisation,
    this.isGeneral,
  });

  Category copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? title,
    String? description,
    dynamic organisationId,
    dynamic organisation,
    bool? isGeneral,
  }) => Category(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    title: title ?? this.title,
    description: description ?? this.description,
    organisationId: organisationId ?? this.organisationId,
    organisation: organisation ?? this.organisation,
    isGeneral: isGeneral ?? this.isGeneral,
  );

  factory Category.fromJson(Map<String, dynamic> json) => Category(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    title: json["title"],
    description: json["description"],
    organisationId: json["organisation_id"],
    organisation: json["organisation"],
    isGeneral: json["is_general"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "title": title,
    "description": description,
    "organisation_id": organisationId,
    "organisation": organisation,
    "is_general": isGeneral,
  };
}
