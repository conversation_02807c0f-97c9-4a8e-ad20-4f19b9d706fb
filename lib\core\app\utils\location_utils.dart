import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart' as geo;
import '../constants/enums.dart' as enums;

/// Utility class for location-related operations
class LocationUtils {
  /// Convert Geolocator LocationPermission to our custom enum
  static enums.LocationPermissionStatus convertPermissionStatus(
    geo.LocationPermission permission,
  ) {
    switch (permission) {
      case geo.LocationPermission.always:
        return enums.LocationPermissionStatus.always;
      case geo.LocationPermission.whileInUse:
        return enums.LocationPermissionStatus.whileInUse;
      case geo.LocationPermission.denied:
        return enums.LocationPermissionStatus.denied;
      case geo.LocationPermission.deniedForever:
        return enums.LocationPermissionStatus.deniedForever;
      case geo.LocationPermission.unableToDetermine:
        return enums.LocationPermissionStatus.unableToDetermine;
    }
  }

  /// Convert our custom LocationAccuracy enum to Geolocator LocationAccuracy
  static geo.LocationAccuracy convertToGeolocatorAccuracy(
    enums.LocationAccuracy accuracy,
  ) {
    switch (accuracy) {
      case enums.LocationAccuracy.lowest:
        return geo.LocationAccuracy.lowest;
      case enums.LocationAccuracy.low:
        return geo.LocationAccuracy.low;
      case enums.LocationAccuracy.medium:
        return geo.LocationAccuracy.medium;
      case enums.LocationAccuracy.high:
        return geo.LocationAccuracy.high;
      case enums.LocationAccuracy.best:
        return geo.LocationAccuracy.best;
      case enums.LocationAccuracy.bestForNavigation:
        return geo.LocationAccuracy.bestForNavigation;
    }
  }

  /// Get platform-specific location settings
  static geo.LocationSettings getPlatformLocationSettings({
    enums.LocationAccuracy accuracy = enums.LocationAccuracy.high,
    int distanceFilter = 10,
    Duration? timeLimit,
  }) {
    if (kIsWeb) {
      return geo.LocationSettings(
        accuracy: convertToGeolocatorAccuracy(accuracy),
        distanceFilter: distanceFilter,
        timeLimit: timeLimit,
      );
    } else if (Platform.isAndroid) {
      return geo.AndroidSettings(
        accuracy: convertToGeolocatorAccuracy(accuracy),
        distanceFilter: distanceFilter,
        forceLocationManager: false,
        intervalDuration: const Duration(seconds: 10),
        foregroundNotificationConfig: const geo.ForegroundNotificationConfig(
          notificationText: "OneChurch is tracking your location",
          notificationTitle: "Location Tracking",
          enableWakeLock: true,
        ),
      );
    } else if (Platform.isIOS) {
      return geo.AppleSettings(
        accuracy: convertToGeolocatorAccuracy(accuracy),
        activityType: geo.ActivityType.other,
        distanceFilter: distanceFilter,
        pauseLocationUpdatesAutomatically: true,
        showBackgroundLocationIndicator: false,
      );
    } else {
      return geo.LocationSettings(
        accuracy: convertToGeolocatorAccuracy(accuracy),
        distanceFilter: distanceFilter,
        timeLimit: timeLimit,
      );
    }
  }

  /// Calculate distance between two positions in meters
  static double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return geo.Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Calculate bearing between two positions in degrees
  static double calculateBearing(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return geo.Geolocator.bearingBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Check if location services are enabled
  static Future<bool> isLocationServiceEnabled() async {
    return await geo.Geolocator.isLocationServiceEnabled();
  }

  /// Check current location permission status
  static Future<enums.LocationPermissionStatus> checkPermission() async {
    final permission = await geo.Geolocator.checkPermission();
    return convertPermissionStatus(permission);
  }

  /// Request location permission
  static Future<enums.LocationPermissionStatus> requestPermission() async {
    final permission = await geo.Geolocator.requestPermission();
    return convertPermissionStatus(permission);
  }

  /// Open location settings
  static Future<bool> openLocationSettings() async {
    return await geo.Geolocator.openLocationSettings();
  }

  /// Open app settings
  static Future<bool> openAppSettings() async {
    return await geo.Geolocator.openAppSettings();
  }

  /// Get current position with error handling
  static Future<geo.Position?> getCurrentPosition({
    enums.LocationAccuracy accuracy = enums.LocationAccuracy.high,
    Duration? timeLimit,
  }) async {
    try {
      return await geo.Geolocator.getCurrentPosition();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting current position: $e');
      }
      return null;
    }
  }

  /// Get position stream with error handling
  static Stream<geo.Position> getPositionStream({
    enums.LocationAccuracy accuracy = enums.LocationAccuracy.high,
    int distanceFilter = 10,
  }) {
    return geo.Geolocator.getPositionStream();
  }

  /// Format coordinates for display
  static String formatCoordinates(double latitude, double longitude) {
    return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
  }

  /// Validate coordinates
  static bool isValidCoordinates(double? latitude, double? longitude) {
    if (latitude == null || longitude == null) return false;
    return latitude >= -90 &&
        latitude <= 90 &&
        longitude >= -180 &&
        longitude <= 180;
  }

  /// Get location accuracy description
  static String getAccuracyDescription(enums.LocationAccuracy accuracy) {
    switch (accuracy) {
      case enums.LocationAccuracy.lowest:
        return 'Lowest accuracy (~3000m)';
      case enums.LocationAccuracy.low:
        return 'Low accuracy (~1000m)';
      case enums.LocationAccuracy.medium:
        return 'Medium accuracy (~100m)';
      case enums.LocationAccuracy.high:
        return 'High accuracy (~10m)';
      case enums.LocationAccuracy.best:
        return 'Best accuracy (~3m)';
      case enums.LocationAccuracy.bestForNavigation:
        return 'Best for navigation (~1m)';
    }
  }

  /// Check if permission is sufficient for location tracking
  static bool isPermissionSufficient(enums.LocationPermissionStatus status) {
    return status == enums.LocationPermissionStatus.always ||
        status == enums.LocationPermissionStatus.whileInUse ||
        status == enums.LocationPermissionStatus.granted;
  }

  /// Get permission status description
  static String getPermissionDescription(
    enums.LocationPermissionStatus status,
  ) {
    switch (status) {
      case enums.LocationPermissionStatus.granted:
        return 'Location permission granted';
      case enums.LocationPermissionStatus.denied:
        return 'Location permission denied';
      case enums.LocationPermissionStatus.deniedForever:
        return 'Location permission permanently denied';
      case enums.LocationPermissionStatus.whileInUse:
        return 'Location permission granted while app is in use';
      case enums.LocationPermissionStatus.always:
        return 'Location permission granted always';
      case enums.LocationPermissionStatus.unableToDetermine:
        return 'Unable to determine location permission status';
    }
  }
}
