import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../models/inventory_category_model.dart';
import '../../auth/controllers/auth_controller.dart';

class InventoryCategoryService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();
  final authController = Get.find<AuthController>();

  // Initialize the service
  InventoryCategoryService() {
    _httpService.initializeDio();
  }

  // Fetch inventory categories from the inventory items API
  Future<Map<String, dynamic>> fetchInventoryCategories({
    String? search,
  }) async {
    try {
      // Use the inventory items API to get categories
      String url = "${ApiUrls.getInventoryItems}?page=0&size=1";

      // Add organisation_id parameter
      final organisationId = authController.currentOrg.value?.id;
      if (organisationId != null) {
        url += "&organisation_id=$organisationId";
      }

      logger.i('Fetching inventory categories from items API with URL: $url');
      final response = await _httpService.request(url: url, method: Method.GET);

      final responseData = response.data;
      if (responseData != null && responseData['status'] == true) {
        final data = responseData['data'];
        if (data != null && data['item_categories'] != null) {
          final categories = data['item_categories'] as List;

          // Filter categories by search if provided
          List<dynamic> filteredCategories = categories;
          if (search != null && search.isNotEmpty) {
            filteredCategories =
                categories.where((category) {
                  final title =
                      category['title']?.toString().toLowerCase() ?? '';
                  return title.contains(search.toLowerCase());
                }).toList();
          }

          return {
            'status': true,
            'message': 'success',
            'data': filteredCategories,
            'errors': null,
          };
        }
      }

      return {'status': true, 'message': 'success', 'data': [], 'errors': null};
    } catch (e) {
      logger.e('Error fetching inventory categories: $e');
      return {
        'status': false,
        'message': 'Failed to load categories: $e',
        'data': [],
        'errors': e.toString(),
      };
    }
  }

  // Create a new inventory category
  Future<Map<String, dynamic>> createInventoryCategory({
    required String title,
    required String description,
    required String code,
    required bool isGeneral,
  }) async {
    try {
      // Get organisation ID from auth controller
      final organisationId = authController.currentOrg.value?.id.toString();

      final Map<String, dynamic> data = {
        'title': title,
        'description': description,
        'code': code,
        'organisation_id': organisationId,
        'is_general': isGeneral,
      };

      logger.i('Creating inventory category with data: $data');

      final response = await _httpService.request(
        url: ApiUrls.createInventoryItemCategory,
        method: Method.POST,
        params: data,
      );

      return response.data;
    } catch (e) {
      logger.e('Error creating inventory category: $e');
      return {
        'status': false,
        'message': 'Failed to create category: $e',
        'data': null,
      };
    }
  }

  // Update an existing inventory category
  Future<Map<String, dynamic>> updateInventoryCategory({
    required String id,
    required String title,
    required String description,
    required String code,
  }) async {
    try {
      // Get organisation ID from auth controller
      final organisationId = authController.currentOrg.value?.id.toString();

      final Map<String, dynamic> data = {
        'title': title,
        'description': description,
        'code': code,
        'organisation_id': organisationId,
      };

      logger.i('Updating inventory category $id with data: $data');

      final response = await _httpService.request(
        url: "${ApiUrls.updateInventoryItemCategory}$id/",
        method: Method.PUT,
        params: data,
      );

      return response.data;
    } catch (e) {
      logger.e('Error updating inventory category: $e');
      return {
        'status': false,
        'message': 'Failed to update category: $e',
        'data': null,
      };
    }
  }

  // Delete an inventory category
  Future<Map<String, dynamic>> deleteInventoryCategory(String id) async {
    try {
      logger.i('Deleting inventory category: $id');

      final response = await _httpService.request(
        url: "${ApiUrls.deleteInventoryItemCategory}$id/",
        method: Method.DELETE,
      );

      return response.data;
    } catch (e) {
      logger.e('Error deleting inventory category: $e');
      return {
        'status': false,
        'message': 'Failed to delete category: $e',
        'data': null,
      };
    }
  }

  // Parse category items from API response
  List<InventoryCategory> parseCategories(List<dynamic> items) {
    return items.map((item) => InventoryCategory.fromJson(item)).toList();
  }
}
