import 'package:flutter/material.dart';

/// Button style enum for different button appearances
enum CustomButtonStyle { filled, outlined, text, elevated, claymorphism }

class CustomButton extends StatelessWidget {
  final String? text;
  final VoidCallback? onPressed;
  final Function()? onClick;
  final bool isLoading;
  final bool isEnabled;
  final Color? backgroundColor; // Deprecated: use color instead
  final Color? textColor;
  final Widget? icon;
  final double? width;
  final double height;
  final Widget? label;
  final Color? color; // Button background color
  final CustomButtonStyle style;
  final double borderRadius;

  const CustomButton({
    super.key,
    this.text,
    this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.backgroundColor, // Deprecated
    this.textColor,
    this.icon,
    this.onClick,
    this.width,
    this.height = 50,
    this.label,
    this.color,
    this.style = CustomButtonStyle.claymorphism,
    this.borderRadius = 7.0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Determine the effective background color
    final effectiveBackgroundColor =
        color ?? backgroundColor ?? colorScheme.primary;

    // Determine the effective text color
    final effectiveTextColor =
        textColor ??
        (style == CustomButtonStyle.outlined || style == CustomButtonStyle.text
            ? effectiveBackgroundColor
            : colorScheme.onPrimary);

    // Determine the display content (label overrides text)
    final displayContent = label ?? (text != null ? Text(text!) : null);

    // Handle button press
    final effectiveOnPressed =
        isEnabled && !isLoading
            ? () {
              if (onPressed != null) {
                onPressed!();
              } else if (onClick != null) {
                onClick!();
              }
            }
            : null;

    switch (style) {
      case CustomButtonStyle.filled:
        return _buildFilledButton(
          context,
          effectiveBackgroundColor,
          effectiveTextColor,
          effectiveOnPressed,
          displayContent,
        );
      case CustomButtonStyle.outlined:
        return _buildOutlinedButton(
          context,
          effectiveBackgroundColor,
          effectiveTextColor,
          effectiveOnPressed,
          displayContent,
        );
      case CustomButtonStyle.text:
        return _buildTextButton(
          context,
          effectiveBackgroundColor,
          effectiveTextColor,
          effectiveOnPressed,
          displayContent,
        );
      case CustomButtonStyle.elevated:
        return _buildCustomButton(
          context,
          effectiveBackgroundColor,
          effectiveTextColor,
          effectiveOnPressed,
          displayContent,
        );
      case CustomButtonStyle.claymorphism:
        return _buildClaymorphismButton(
          context,
          effectiveBackgroundColor,
          effectiveTextColor,
          effectiveOnPressed,
          displayContent,
        );
    }
  }

  Widget _buildFilledButton(
    BuildContext context,
    Color backgroundColor,
    Color textColor,
    VoidCallback? onPressed,
    Widget? content,
  ) {
    return FilledButton(
      style: FilledButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: textColor,
        minimumSize: Size(width ?? double.infinity, height),
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 24),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        disabledBackgroundColor: backgroundColor.withOpacity(0.4),
        disabledForegroundColor: textColor.withOpacity(0.4),
      ),
      onPressed: onPressed,
      child: _buildButtonChild(content, context),
    );
  }

  Widget _buildOutlinedButton(
    BuildContext context,
    Color borderColor,
    Color textColor,
    VoidCallback? onPressed,
    Widget? content,
  ) {
    return OutlinedButton(
      style: OutlinedButton.styleFrom(
        foregroundColor: textColor,
        minimumSize: Size(width ?? double.infinity, height),
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 24),
        side: BorderSide(
          color: isEnabled ? borderColor : borderColor.withOpacity(0.4),
          width: 1.5,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        disabledForegroundColor: textColor.withOpacity(0.4),
      ),
      onPressed: onPressed,
      child: _buildButtonChild(content, context),
    );
  }

  Widget _buildTextButton(
    BuildContext context,
    Color backgroundColor,
    Color textColor,
    VoidCallback? onPressed,
    Widget? content,
  ) {
    return TextButton(
      style: TextButton.styleFrom(
        foregroundColor: textColor,
        minimumSize: Size(width ?? double.infinity, height),
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 24),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        disabledForegroundColor: textColor.withOpacity(0.4),
      ),
      onPressed: onPressed,
      child: _buildButtonChild(content, context),
    );
  }

  Widget _buildCustomButton(
    BuildContext context,
    Color backgroundColor,
    Color textColor,
    VoidCallback? onPressed,
    Widget? content,
  ) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: textColor,
        minimumSize: Size(width ?? double.infinity, height),
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 24),
        elevation: isEnabled ? 4.0 : 1.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        disabledBackgroundColor: backgroundColor.withOpacity(0.4),
        disabledForegroundColor: textColor.withOpacity(0.4),
      ),
      onPressed: onPressed,
      child: _buildButtonChild(content, context),
    );
  }

  Widget _buildClaymorphismButton(
    BuildContext context,
    Color backgroundColor,
    Color textColor,
    VoidCallback? onPressed,
    Widget? content,
  ) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final baseColor =
        isDark ? const Color(0xFF2A2A2A) : const Color(0xFFF0F0F0);

    return Container(
      width: width ?? double.infinity,
      height: height,
      decoration: BoxDecoration(
        color: isEnabled ? baseColor : baseColor.withOpacity(0.5),
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow:
            isEnabled
                ? [
                  // Light shadow (top-left)
                  BoxShadow(
                    color:
                        isDark ? Colors.white.withOpacity(0.1) : Colors.white,
                    offset: const Offset(-4, -4),
                    blurRadius: 8,
                    spreadRadius: 0,
                  ),
                  // Dark shadow (bottom-right)
                  BoxShadow(
                    color:
                        isDark
                            ? Colors.black.withOpacity(0.5)
                            : Colors.grey.withOpacity(0.3),
                    offset: const Offset(4, 4),
                    blurRadius: 8,
                    spreadRadius: 0,
                  ),
                ]
                : [],
        gradient:
            isEnabled
                ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    baseColor.withOpacity(0.9),
                    baseColor,
                    baseColor.withOpacity(0.8),
                  ],
                )
                : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: onPressed,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 24),
            child: Center(
              child: DefaultTextStyle(
                style: TextStyle(
                  color: isEnabled ? textColor : textColor.withOpacity(0.4),
                  fontWeight: FontWeight.w600,
                ),
                child: _buildButtonChild(content, context),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildButtonChild(Widget? content, [BuildContext? context]) {
    if (isLoading) {
      return SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            style == CustomButtonStyle.outlined ||
                    style == CustomButtonStyle.text
                ? (color ??
                    (context != null
                        ? Theme.of(context).colorScheme.primary
                        : Colors.blue))
                : (textColor ??
                    (context != null
                        ? Theme.of(context).colorScheme.onPrimary
                        : Colors.white)),
          ),
        ),
      );
    }

    if (icon != null && content != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [icon!, const SizedBox(width: 8), content],
      );
    }

    return content ?? const SizedBox.shrink();
  }
}
