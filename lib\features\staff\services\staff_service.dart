import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';

import '../../../core/app/services/api_urls.dart';
import '../../../core/app/services/http_service.dart';
import '../models/staff_model.dart';

class StaffService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();

  // Initialize the service
  StaffService() {
    _httpService.initializeDio();
  }

  // Fetch all staff members with pagination and filters
  Future<Map<String, dynamic>> fetchStaffs({
    required int page,
    required int size,
    String? identifier,
    String? role,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Build query string
      String queryString = "?page=$page&size=$size";

      // Add optional filters if provided
      if (identifier != null && identifier.isNotEmpty) {
        queryString += "&identifier=$identifier";
      }
      if (role != null && role.isNotEmpty) {
        queryString += "&role=$role";
      }
      if (startDate != null) {
        queryString += "&created_at_after=${startDate.toIso8601String()}";
      }
      if (endDate != null) {
        queryString += "&created_at_before=${endDate.toIso8601String()}";
      }

      final response = await _httpService.request(
        url: ApiUrls.getStaff + queryString,
        method: Method.GET,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching staffs: $e');
      return {
        'status': false,
        'message': 'Failed to fetch staff members: ${e.toString()}',
        'data': null,
      };
    }
  }

  // Get a staff member by ID
  Future<Map<String, dynamic>> getStaffById(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.getStaffById}$id",
        method: Method.GET,
      );
      return response.data;
    } catch (e) {
      logger.e('Error fetching staff by ID: $e');
      return {
        'status': false,
        'message': 'Failed to fetch staff member: ${e.toString()}',
        'data': null,
      };
    }
  }

  // Create a new staff member
  Future<Map<String, dynamic>> createStaff({
    required String phoneNumber,
    required String firstName,
    required String secondName,
    required String code,
    required String email,
    required String idNumber,
    required List<dynamic> assignedLocations,
    required String role,
    required bool isMaker,
    required bool isChecker,
    required bool isSignatory,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'phone_number': phoneNumber,
        'first_name': firstName,
        'second_name': secondName,
        'code': code,
        'email': email,
        'id_number': idNumber,
        'assigned_locations': assignedLocations,
        'organisation_id': Get.find<AuthController>().currentOrg.value?.id,
        'role': role,
        'is_maker': isMaker,
        'is_checker': isChecker,
        'is_signatory': isSignatory,
      };

      final response = await _httpService.request(
        url: ApiUrls.onBoardStaff,
        method: Method.POST,
        params: data,
      );
      if (response.data['status'] == false) {
        ToastUtils.showErrorToast('error!', response.data['message']);
      }

      return response.data;
    } catch (e) {
      logger.e('Error creating staff: $e');
      return {
        'status': false,
        'message': 'Failed to create staff member: ${e.toString()}',
        'data': null,
      };
    }
  }

  // Update an existing staff member
  Future<Map<String, dynamic>> updateStaff({
    required String id,
    required String phoneNumber,
    required String firstName,
    required String secondName,
    required String code,
    required String email,
    required String idNumber,
    required List<dynamic> assignedLocations,
    required String role,
    required bool isMaker,
    required bool isChecker,
    required bool isSignatory,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'phone_number': phoneNumber,
        'first_name': firstName,
        'second_name': secondName,
        'code': code,
        'email': email,
        'id_number': idNumber,
        'assigned_locations': assignedLocations,
        'organisation_id': Get.find<AuthController>().currentOrg.value?.id,
        'role': role,
        'is_maker': isMaker,
        'is_checker': isChecker,
        'is_signatory': isSignatory,
      };

      final response = await _httpService.request(
        url: "${ApiUrls.updateStaff}$id/",
        method: Method.PUT,
        params: data,
      );

      return response.data;
    } catch (e) {
      logger.e('Error updating staff: $e');
      return {
        'status': false,
        'message': 'Failed to update staff member: ${e.toString()}',
        'data': null,
      };
    }
  }

  // Delete a staff member
  Future<Map<String, dynamic>> deleteStaff(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.deleteStaff}$id/",
        method: Method.DELETE,
      );
      return response.data;
    } catch (e) {
      logger.e('Error deleting staff: $e');
      return {
        'status': false,
        'message': 'Failed to delete staff member: ${e.toString()}',
        'data': null,
      };
    }
  }

  // Check if a phone number belongs to a member
  Future<Map<String, dynamic>> checkMemberByPhone(String phoneNumber) async {
    try {
      final response = await _httpService.request(
        url:
            "${ApiUrls.getSingleMember}?identifier=${phoneNumber.trim().replaceAll('+', '')}",
        method: Method.GET,
      );
      return response.data;
    } catch (e) {
      logger.e('Error checking member by phone: $e');
      return {
        'status': false,
        'message': 'Failed to check member: ${e.toString()}',
        'data': null,
      };
    }
  }

  // Parse staff list from API response
  List<StaffModel> parseStaffs(List<dynamic> items) {
    return items.map((item) => StaffModel.fromJson(item)).toList();
  }
}
