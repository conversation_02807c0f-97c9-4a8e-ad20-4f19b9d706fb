import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/app/constants/routes.dart';
import '../../controllers/hymn_controller.dart';
import '../../../../data/models/hymn_model.dart';

class DownloadedHymnsScreen extends GetView<HymnController> {
  const DownloadedHymnsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: colorScheme.surface,
        appBar: AppBar(
          title: Text(
            'My Library',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          bottom: const TabBar(
            tabs: [
              Tab(icon: Icon(IconlyLight.download), text: 'Downloads'),
              Tab(icon: Icon(IconlyLight.bookmark), text: 'Bookmarks'),
            ],
          ),
        ),
        body: TabBarView(
          children: [_buildDownloadedHymns(), _buildBookmarkedHymns()],
        ),
      ),
    );
  }

  Widget _buildDownloadedHymns() {
    return Obx(
      () => _buildHymnsList(
        controller.downloadedHymns,
        'No downloaded hymns',
        'Your downloaded hymns will appear here',
      ),
    );
  }

  Widget _buildBookmarkedHymns() {
    return Obx(
      () => _buildHymnsList(
        controller.bookmarkedHymns,
        'No bookmarked hymns',
        'Your bookmarked hymns will appear here',
      ),
    );
  }

  Widget _buildHymnsList(List<HymnModel> hymns, String title, String subtitle) {
    if (hymns.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(IconlyLight.paper, size: 64),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: TextStyle(
                color: Get.theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: hymns.length,
      itemBuilder: (context, index) {
        final hymn = hymns[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 0,
          color: Get.theme.colorScheme.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap:
                () =>
                    context.go('${Routes.HYMN_DETAIL}/${hymn.id}', extra: hymn),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                children: [
                  Container(
                    height: 80,
                    width: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Get.theme.colorScheme.primaryContainer,
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.music_note,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          hymn.title ?? 'Untitled Hymn',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${hymn.artist} • ${hymn.album}',
                          style: TextStyle(
                            color: Get.theme.colorScheme.onSurface.withOpacity(
                              0.6,
                            ),
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        if (hymn.hasCategories)
                          Row(
                            children: [
                              Icon(
                                IconlyLight.category,
                                size: 14,
                                color: Get.theme.colorScheme.primary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                hymn.categories.first.name ?? 'Uncategorized',
                                style: TextStyle(
                                  color: Get.theme.colorScheme.primary,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
