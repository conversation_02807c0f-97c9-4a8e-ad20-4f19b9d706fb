import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../data/models/event_model.dart';

class EventService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();

  // Initialize the service
  EventService() {
    _httpService.initializeDio();
  }

  // Fetch events with pagination and filters
  Future<Map<String, dynamic>> fetchEvents({
    required int page,
    required int size,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? organisationId,
  }) async {
    try {
      // Prepare request parameters
      final Map<String, dynamic> params = {
        'organisation_id': Get.find<AuthController>().currentOrg.value?.id,
        'page': page,
        'size': size,
      };

      // Add search query if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        params['search'] = searchQuery;
      }

      // Add date filters if provided
      if (startDate != null) {
        params['start_date'] = startDate.toIso8601String();
      }

      if (endDate != null) {
        params['end_date'] = endDate.toIso8601String();
      }

      logger.d('Fetching events with params: $params');

      // The API endpoint for events
      final response = await _httpService.request(
        url: "${ApiUrls.events}?page=$page&size=$size",
        method: Method.GET,
        params: params,
      );

      logger.d('Raw response from API: ${response.data}');

      // Ensure we're returning a properly structured response
      if (response.data is Map<String, dynamic>) {
        return response.data;
      } else {
        logger.e('API response is not a Map: ${response.data}');
        // Convert to a proper format if needed
        return {'status': true, 'message': 'success', 'data': response.data};
      }
    } catch (e, stackTrace) {
      logger.e('Error fetching events: $e');
      logger.e('Stack trace: $stackTrace');
      return {
        'status': false,
        'message': 'Failed to fetch events: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  // Get event by ID
  Future<Map<String, dynamic>> getEventById(String eventId) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.eventById}$eventId",
        method: Method.GET,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching event details: $e');
      return {
        'status': false,
        'message': 'Failed to fetch event details',
        'error': e.toString(),
      };
    }
  }

  // Create a new event from EventModel
  Future<Map<String, dynamic>> createEvent(EventModel event) async {
    try {
      final response = await _httpService.request(
        url: ApiUrls.createEvent,
        method: Method.POST,
        params: event.toJson(),
      );

      return response.data;
    } catch (e) {
      logger.e('Error creating event: $e');
      return {
        'status': false,
        'message': 'Failed to create event',
        'error': e.toString(),
      };
    }
  }

  // Create a new event with custom data (including media)
  Future<Map<String, dynamic>> createEventWithData(
    Map<String, dynamic> eventData,
  ) async {
    try {
      logger.d('Creating event with data: $eventData');

      final response = await _httpService.request(
        url: ApiUrls.createEvent,
        method: Method.POST,
        params: eventData,
      );

      logger.d('Event creation response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error creating event with media: $e');
      return {
        'status': false,
        'message': 'Failed to create event with media',
        'error': e.toString(),
      };
    }
  }

  // Update an existing event using EventModel
  Future<Map<String, dynamic>> updateEvent(
    String eventId,
    EventModel event,
  ) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.eventById}$eventId",
        method: Method.PUT,
        params: event.toJson(),
      );

      return response.data;
    } catch (e) {
      logger.e('Error updating event: $e');
      return {
        'status': false,
        'message': 'Failed to update event',
        'error': e.toString(),
      };
    }
  }

  // Update an existing event with custom data (including media)
  Future<Map<String, dynamic>> updateEventWithData(
    String eventId,
    Map<String, dynamic> eventData,
  ) async {
    try {
      logger.d('Updating event with data: $eventData');

      final response = await _httpService.request(
        url: "${ApiUrls.eventById}$eventId",
        method: Method.PUT,
        params: eventData,
      );

      logger.d('Event update response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error updating event with media: $e');
      return {
        'status': false,
        'message': 'Failed to update event with media',
        'error': e.toString(),
      };
    }
  }

  // Delete an event
  Future<Map<String, dynamic>> deleteEvent(String eventId) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.eventById}$eventId",
        method: Method.DELETE,
      );

      return response.data;
    } catch (e) {
      logger.e('Error deleting event: $e');
      return {
        'status': false,
        'message': 'Failed to delete event',
        'error': e.toString(),
      };
    }
  }
}
