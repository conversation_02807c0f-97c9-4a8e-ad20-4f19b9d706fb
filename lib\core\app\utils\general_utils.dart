import 'package:flutter/material.dart';

class GeneralUtils {
  static showTooltip(String message, BuildContext context) {
    final tooltipKey = GlobalKey<TooltipState>();
    final tooltip = Tooltip(
      key: tooltipKey,
      message: message,
      child: Container(),
    );
    final overlay = Overlay.of(context);
    final entry = OverlayEntry(builder: (context) => tooltip);
    overlay.insert(entry);
    tooltipKey.currentState?.ensureTooltipVisible();
    Future.delayed(const Duration(seconds: 2), () {
      entry.remove();
    });
  }
}
