import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../data/models/sms_model.dart';

class SmsRequestsService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();

  // Initialize the service
  SmsRequestsService() {
    _httpService.initializeDio();
  }

  // Fetch SMS requests with pagination and filters
  Future<Map<String, dynamic>> fetchSmsRequests({
    required int page,
    required int size,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? organisationId,
    String? phoneNumber,
    String? message,
  }) async {
    try {
      // Build URL with query parameters
      String url = "${ApiUrls.getOrganisationSmsRequests}?";
      url += "page=$page";
      url += "&size=$size";

      // Add optional filters to URL
      if (searchQuery != null && searchQuery.isNotEmpty) {
        url += "&search=${Uri.encodeComponent(searchQuery)}";
      }

      if (startDate != null) {
        url +=
            "&start_date=${Uri.encodeComponent(startDate.toUtc().toIso8601String())}";
      }

      if (endDate != null) {
        url +=
            "&end_date=${Uri.encodeComponent(endDate.toUtc().toIso8601String())}";
      }

      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        url += "&phone_number=${Uri.encodeComponent(phoneNumber)}";
      }

      if (message != null && message.isNotEmpty) {
        url += "&message=${Uri.encodeComponent(message)}";
      }

      final response = await _httpService.request(url: url, method: Method.GET);

      return response.data;
    } catch (e) {
      logger.e('Error fetching SMS requests: $e');
      rethrow;
    }
  }

  // Parse SMS items from API response
  List<SmsModel> parseMessages(List<dynamic> items) {
    return items.map((item) => SmsModel.fromJson(item)).toList();
  }
}
