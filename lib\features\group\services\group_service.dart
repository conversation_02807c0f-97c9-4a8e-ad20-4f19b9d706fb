import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../data/models/group_model.dart';

class GroupService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();

  // Initialize the service
  GroupService() {
    _httpService.initializeDio();
  }

  // Fetch groups with pagination and filters
  Future<Map<String, dynamic>> fetchGroups({
    required int page,
    required int size,
    String? searchQuery,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
    String? memberId,
  }) async {
    try {
      // Prepare request parameters
      final Map<String, dynamic> params = {'page': page, 'size': size};

      // Add search query if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        params['search'] = searchQuery;
      }

      // Add status filter if provided
      if (status != null && status.isNotEmpty) {
        params['status'] = status;
      }

      // Add date filters if provided
      if (startDate != null) {
        params['start_date'] = startDate.toIso8601String();
      }

      if (endDate != null) {
        params['end_date'] = endDate.toIso8601String();
      }

      final response = await _httpService.request(
        url: "${ApiUrls.groups}?page=$page&size=$size&member_id=$memberId",
        method: Method.GET,
        params: params,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching groups: $e');
      rethrow;
    }
  }

  // Get group by ID
  Future<Map<String, dynamic>> getGroupById(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.groupById}$id",
        method: Method.GET,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching group by ID: $e');
      rethrow;
    }
  }

  // Parse group items from API response
  List<GroupModel> parseGroups(List<dynamic> items) {
    return items.map((item) => GroupModel.fromJson(item)).toList();
  }

  // Create a new group
  Future<Map<String, dynamic>> createGroup({
    required String title,
    required String description,
    String status = 'active',
    List<Map<String, dynamic>>? members,
    List<Map<String, dynamic>>? media,
  }) async {
    try {
      final Map<String, dynamic> groupData = {
        'title': title,
        'description': description,
        'status': status,
        'organisation_id': Get.find<AuthController>().currentOrg.value?.id,
      };

      if (members != null && members.isNotEmpty) {
        groupData['members'] = members;
      }

      if (media != null && media.isNotEmpty) {
        groupData['media'] = media;
      }

      logger.d('Creating group with payload: $groupData');

      final response = await _httpService.request(
        url: ApiUrls.createGroup,
        method: Method.POST,
        params: groupData,
      );

      logger.d('Group creation response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error creating group: $e');
      rethrow;
    }
  }

  // Update an existing group
  Future<Map<String, dynamic>> updateGroup({
    required String id,
    String? title,
    String? description,
    String? status,
    List<Map<String, dynamic>>? members,
    List<Map<String, dynamic>>? media,
  }) async {
    try {
      final Map<String, dynamic> groupData = {'id': id};

      if (title != null) groupData['title'] = title;
      if (description != null) groupData['description'] = description;
      if (status != null) groupData['status'] = status;
      if (members != null) groupData['members'] = members;
      if (media != null) groupData['media'] = media;

      logger.d('Updating group with payload: $groupData');

      final response = await _httpService.request(
        url: "${ApiUrls.updateGroup}$id",
        method: Method.PUT,
        params: groupData,
      );

      logger.d('Group update response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error updating group: $e');
      rethrow;
    }
  }

  // Delete a group
  Future<Map<String, dynamic>> deleteGroup(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.deleteGroup}$id",
        method: Method.DELETE,
      );

      logger.d('Group deletion response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error deleting group: $e');
      rethrow;
    }
  }
}
