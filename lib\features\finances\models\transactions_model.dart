// To parse this JSON data, do
//
//     final transactionsModel = transactionsModelFromJson(jsonString);

import 'dart:convert';

import 'package:onechurch/data/models/sub_account_model.dart';

TransactionsModel transactionsModelFromJson(String str) =>
    TransactionsModel.fromJson(json.decode(str));

String transactionsModelToJson(TransactionsModel data) =>
    json.encode(data.toJson());

class TransactionsModel {
  String? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? accountNumber;
  String? accountNumberRef;
  String? paymentRef;
  String? description;
  String? payerEmail;
  String? firstName;
  String? secondName;
  String? accountName;
  String? transactionCode;
  String? transactionCodeOther;
  String? apiTransactionRef;
  String? internalId;
  String? phoneNumber;
  int? kittyId;
  dynamic userId;
  String? currencyCode;
  String? checkoutRequestId;
  String? channelCode;
  String? channelName;
  num? amount;
  num? totalCharges;
  num? thirdPartyCharges;
  num? orgCharges;
  String? typeInOut;
  String? status;
  String? transactionType;
  String? transactionCategory;
  dynamic paymentRequestId;
  dynamic invoice;
  dynamic invoiceId;
  Member? member;
  String? memberId;
  SubAccount? subAccount;
  String? subAccountId;
  dynamic orgAccount;
  String? orgAccountId;
  dynamic organisation;
  int? organisationId;
  bool? isReversed;
  dynamic merchantAccount;
  dynamic merchantAccountId;

  TransactionsModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.accountNumber,
    this.accountNumberRef,
    this.paymentRef,
    this.description,
    this.payerEmail,
    this.firstName,
    this.secondName,
    this.accountName,
    this.transactionCode,
    this.transactionCodeOther,
    this.apiTransactionRef,
    this.internalId,
    this.phoneNumber,
    this.kittyId,
    this.userId,
    this.currencyCode,
    this.checkoutRequestId,
    this.channelCode,
    this.channelName,
    this.amount,
    this.totalCharges,
    this.thirdPartyCharges,
    this.orgCharges,
    this.typeInOut,
    this.status,
    this.transactionType,
    this.transactionCategory,
    this.paymentRequestId,
    this.invoice,
    this.invoiceId,
    this.member,
    this.memberId,
    this.subAccount,
    this.subAccountId,
    this.orgAccount,
    this.orgAccountId,
    this.organisation,
    this.organisationId,
    this.isReversed,
    this.merchantAccount,
    this.merchantAccountId,
  });

  TransactionsModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? accountNumber,
    String? accountNumberRef,
    String? paymentRef,
    String? description,
    String? payerEmail,
    String? firstName,
    String? secondName,
    String? accountName,
    String? transactionCode,
    String? transactionCodeOther,
    String? apiTransactionRef,
    String? internalId,
    String? phoneNumber,
    int? kittyId,
    int? userId,
    String? currencyCode,
    String? checkoutRequestId,
    String? channelCode,
    String? channelName,
    num? amount,
    num? totalCharges,
    num? thirdPartyCharges,
    num? orgCharges,
    String? typeInOut,
    String? status,
    String? transactionType,
    String? transactionCategory,
    String? paymentRequestId,
    dynamic invoice,
    dynamic invoiceId,
    Member? member,
    String? memberId,
    SubAccount? subAccount,
    String? subAccountId,
    dynamic orgAccount,
    String? orgAccountId,
    dynamic organisation,
    int? organisationId,
    bool? isReversed,
    dynamic merchantAccount,
    dynamic merchantAccountId,
  }) => TransactionsModel(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    accountNumber: accountNumber ?? this.accountNumber,
    accountNumberRef: accountNumberRef ?? this.accountNumberRef,
    paymentRef: paymentRef ?? this.paymentRef,
    description: description ?? this.description,
    payerEmail: payerEmail ?? this.payerEmail,
    firstName: firstName ?? this.firstName,
    secondName: secondName ?? this.secondName,
    accountName: accountName ?? this.accountName,
    transactionCode: transactionCode ?? this.transactionCode,
    transactionCodeOther: transactionCodeOther ?? this.transactionCodeOther,
    apiTransactionRef: apiTransactionRef ?? this.apiTransactionRef,
    internalId: internalId ?? this.internalId,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    kittyId: kittyId ?? this.kittyId,
    userId: userId ?? this.userId,
    currencyCode: currencyCode ?? this.currencyCode,
    checkoutRequestId: checkoutRequestId ?? this.checkoutRequestId,
    channelCode: channelCode ?? this.channelCode,
    channelName: channelName ?? this.channelName,
    amount: amount ?? this.amount,
    totalCharges: totalCharges ?? this.totalCharges,
    thirdPartyCharges: thirdPartyCharges ?? this.thirdPartyCharges,
    orgCharges: orgCharges ?? this.orgCharges,
    typeInOut: typeInOut ?? this.typeInOut,
    status: status ?? this.status,
    transactionType: transactionType ?? this.transactionType,
    transactionCategory: transactionCategory ?? this.transactionCategory,
    paymentRequestId: paymentRequestId ?? this.paymentRequestId,
    invoice: invoice ?? this.invoice,
    invoiceId: invoiceId ?? this.invoiceId,
    member: member ?? this.member,
    memberId: memberId ?? this.memberId,
    subAccount: subAccount ?? this.subAccount,
    subAccountId: subAccountId ?? this.subAccountId,
    orgAccount: orgAccount ?? this.orgAccount,
    orgAccountId: orgAccountId ?? this.orgAccountId,
    organisation: organisation ?? this.organisation,
    organisationId: organisationId ?? this.organisationId,
    isReversed: isReversed ?? this.isReversed,
    merchantAccount: merchantAccount ?? this.merchantAccount,
    merchantAccountId: merchantAccountId ?? this.merchantAccountId,
  );

  factory TransactionsModel.fromJson(
    Map<String, dynamic> json,
  ) => TransactionsModel(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    accountNumber: json["account_number"],
    accountNumberRef: json["account_number_ref"],
    paymentRef: json["payment_ref"],
    description: json["description"],
    payerEmail: json["payer_email"],
    firstName: json["first_name"],
    secondName: json["second_name"],
    accountName: json["account_name"],
    transactionCode: json["transaction_code"],
    transactionCodeOther: json["transaction_code_other"],
    apiTransactionRef: json["api_transaction_ref"],
    internalId: json["internal_id"],
    phoneNumber: json["phone_number"],
    kittyId: json["kitty_id"],
    userId: json["user_id"],
    currencyCode: json["currency_code"],
    checkoutRequestId: json["checkout_request_id"],
    channelCode: json["channel_code"],
    channelName: json["channel_name"],
    amount: json["amount"],
    totalCharges: json["total_charges"],
    thirdPartyCharges: json["third_party_charges"],
    orgCharges: json["org_charges"],
    typeInOut: json["type_in_out"],
    status: json["status"],
    transactionType: json["transaction_type"],
    transactionCategory: json["transaction_category"],
    paymentRequestId: json["payment_request_id"],
    invoice: json["invoice"],
    invoiceId: json["invoice_id"],
    member: json["member"] == null ? null : Member.fromJson(json["member"]),
    memberId: json["member_id"],
    subAccount:
        json["sub_account"] == null
            ? null
            : SubAccount.fromJson(json["sub_account"]),
    subAccountId: json["sub_account_id"],
    orgAccount: json["org_account"],
    orgAccountId: json["org_account_id"],
    organisation: json["organisation"],
    organisationId: json["organisation_id"],
    isReversed: json["is_reversed"],
    merchantAccount: json["merchant_account"],
    merchantAccountId: json["merchant_account_id"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "account_number": accountNumber,
    "account_number_ref": accountNumberRef,
    "payment_ref": paymentRef,
    "description": description,
    "payer_email": payerEmail,
    "first_name": firstName,
    "second_name": secondName,
    "account_name": accountName,
    "transaction_code": transactionCode,
    "transaction_code_other": transactionCodeOther,
    "api_transaction_ref": apiTransactionRef,
    "internal_id": internalId,
    "phone_number": phoneNumber,
    "kitty_id": kittyId,
    "user_id": userId,
    "currency_code": currencyCode,
    "checkout_request_id": checkoutRequestId,
    "channel_code": channelCode,
    "channel_name": channelName,
    "amount": amount,
    "total_charges": totalCharges,
    "third_party_charges": thirdPartyCharges,
    "org_charges": orgCharges,
    "type_in_out": typeInOut,
    "status": status,
    "transaction_type": transactionType,
    "transaction_category": transactionCategory,
    "payment_request_id": paymentRequestId,
    "invoice": invoice,
    "invoice_id": invoiceId,
    "member": member?.toJson(),
    "member_id": memberId,
    "sub_account": subAccount?.toJson(),
    "sub_account_id": subAccountId,
    "org_account": orgAccount,
    "org_account_id": orgAccountId,
    "organisation": organisation,
    "organisation_id": organisationId,
    "is_reversed": isReversed,
    "merchant_account": merchantAccount,
    "merchant_account_id": merchantAccountId,
  };
}

class Member {
  String? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? phoneNumber;
  String? secondaryNumber;
  dynamic joinDate;
  dynamic dob;
  String? profileUrl;
  String? firstName;
  String? secondName;
  String? email;
  String? address;
  dynamic userId;
  String? idNumber;
  int? balance;
  String? accountNumber;
  int? organisationId;
  String? verificationStatus;
  String? status;
  dynamic memberCategoryId;
  dynamic locationId;
  dynamic createdByUserId;
  dynamic createdByUser;

  Member({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.phoneNumber,
    this.secondaryNumber,
    this.joinDate,
    this.dob,
    this.profileUrl,
    this.firstName,
    this.secondName,
    this.email,
    this.address,
    this.userId,
    this.idNumber,
    this.balance,
    this.accountNumber,
    this.organisationId,
    this.verificationStatus,
    this.status,
    this.memberCategoryId,
    this.locationId,
    this.createdByUserId,
    this.createdByUser,
  });

  Member copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? phoneNumber,
    String? secondaryNumber,
    dynamic joinDate,
    dynamic dob,
    String? profileUrl,
    String? firstName,
    String? secondName,
    String? email,
    String? address,
    dynamic userId,
    String? idNumber,
    int? balance,
    String? accountNumber,
    int? organisationId,
    String? verificationStatus,
    String? status,
    dynamic memberCategoryId,
    dynamic locationId,
    dynamic createdByUserId,
    dynamic createdByUser,
  }) => Member(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    secondaryNumber: secondaryNumber ?? this.secondaryNumber,
    joinDate: joinDate ?? this.joinDate,
    dob: dob ?? this.dob,
    profileUrl: profileUrl ?? this.profileUrl,
    firstName: firstName ?? this.firstName,
    secondName: secondName ?? this.secondName,
    email: email ?? this.email,
    address: address ?? this.address,
    userId: userId ?? this.userId,
    idNumber: idNumber ?? this.idNumber,
    balance: balance ?? this.balance,
    accountNumber: accountNumber ?? this.accountNumber,
    organisationId: organisationId ?? this.organisationId,
    verificationStatus: verificationStatus ?? this.verificationStatus,
    status: status ?? this.status,
    memberCategoryId: memberCategoryId ?? this.memberCategoryId,
    locationId: locationId ?? this.locationId,
    createdByUserId: createdByUserId ?? this.createdByUserId,
    createdByUser: createdByUser ?? this.createdByUser,
  );

  factory Member.fromJson(Map<String, dynamic> json) => Member(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    phoneNumber: json["phone_number"],
    secondaryNumber: json["secondary_number"],
    joinDate: json["join_date"],
    dob: json["dob"],
    profileUrl: json["profile_url"],
    firstName: json["first_name"],
    secondName: json["second_name"],
    email: json["email"],
    address: json["address"],
    userId: json["user_id"],
    idNumber: json["id_number"],
    balance: json["balance"],
    accountNumber: json["account_number"],
    organisationId: json["organisation_id"],
    verificationStatus: json["verification_status"],
    status: json["status"],
    memberCategoryId: json["member_category_id"],
    locationId: json["location_id"],
    createdByUserId: json["created_by_user_id"],
    createdByUser: json["created_by_user"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "phone_number": phoneNumber,
    "secondary_number": secondaryNumber,
    "join_date": joinDate,
    "dob": dob,
    "profile_url": profileUrl,
    "first_name": firstName,
    "second_name": secondName,
    "email": email,
    "address": address,
    "user_id": userId,
    "id_number": idNumber,
    "balance": balance,
    "account_number": accountNumber,
    "organisation_id": organisationId,
    "verification_status": verificationStatus,
    "status": status,
    "member_category_id": memberCategoryId,
    "location_id": locationId,
    "created_by_user_id": createdByUserId,
    "created_by_user": createdByUser,
  };
}
