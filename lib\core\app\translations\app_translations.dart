import 'package:flutter/material.dart';
import '../translations/english.dart' show english;
import '../translations/french.dart' show french;
import '../translations/german.dart' show german;
import '../translations/spanish.dart' show spanish;
import '../translations/swahili.dart' show swahili;

class AppLocalizations {
  final Locale locale;
  late Map<String, String> _localizedStrings;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static const List<Locale> supportedLocales = [
    Locale('en', ''), // English
    Locale('fr', ''), // French
    Locale('de', ''), // German
    Locale('es', ''), // Spanish
    Locale('sw', ''), // Swahili
  ];

  Future<bool> load() async {
    switch (locale.languageCode) {
      case 'fr':
        _localizedStrings = french;
        break;
      case 'de':
        _localizedStrings = german;
        break;
      case 'es':
        _localizedStrings = spanish;
        break;
      case 'sw':
        _localizedStrings = swahili;
        break;
      case 'en':
      default:
        _localizedStrings = english;
        break;
    }
    return true;
  }

  String translate(String key) {
    return _localizedStrings[key] ?? key;
  }

  // Convenience method for shorter syntax
  String tr(String key) => translate(key);
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return AppLocalizations.supportedLocales.any(
      (supportedLocale) => supportedLocale.languageCode == locale.languageCode,
    );
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    AppLocalizations localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

// Extension for easy access to translations
extension AppLocalizationsExtension on BuildContext {
  AppLocalizations? get localizations => AppLocalizations.of(this);
  String tr(String key) => AppLocalizations.of(this)?.translate(key) ?? key;
}
