import 'dart:convert';
import 'dart:math';
import 'package:onechurch/data/models/member_model.dart';
import 'package:onechurch/data/models/member_category_model.dart';

class MemberPayloadModel {
  // Required personal information
  final String phoneNumber;
  final String firstName;
  final String secondName;
  final String? email;
  final String? secondaryNumber;
  final String? idNumber;

  // IDs and references
  final String organisationId;
  final String? memberCategoryId;
  final MemberCategory? memberCategory;
  final String? locationId;
  final int? userId;

  // Dates
  final String? joinDate;
  final String? dob;
  final String? baptismDate;

  // Account information
  final String accountNumber;
  final String? internalAccount;
  final String? memberAccount;
  final int balance;

  // Address and location
  final String? address;
  final String? locationCode;

  // Personal attributes
  final String gender;
  final String maritalStatus;
  final String? occupation;
  final String educationLevel;
  final String tribe;
  final String disability;
  final String nationality;

  // Socioeconomic information
  final String? incomeBracket;
  final String? employmentStatus;
  final int householdSize;
  final String housingType;
  final List<String> assetOwnership;

  // Status information
  final String? profileUrl;
  final String status;
  final String verificationStatus;
  final String? categoryCode;

  // Child indicator
  final bool isChild;

  MemberPayloadModel({
    required this.phoneNumber,
    required this.firstName,
    required this.secondName,
    this.email,
    this.secondaryNumber,
    this.idNumber,
    required this.organisationId,
    this.memberCategoryId,
    this.memberCategory,
    this.locationId,
    this.userId = 1,
    this.joinDate,
    this.dob,
    this.baptismDate,
    required this.accountNumber,
    this.internalAccount = 'CUST',
    this.memberAccount = 'CUST',
    this.balance = 0,
    this.address = '',
    this.locationCode = '',
    this.gender = 'MALE',
    this.maritalStatus = 'SINGLE',
    this.occupation,
    this.educationLevel = 'PRIMARY',
    this.tribe = 'example',
    this.disability = 'NONE',
    this.nationality = 'Kenyan',
    this.incomeBracket,
    this.employmentStatus,
    this.householdSize = 2,
    this.housingType = 'PERMANENT',
    this.assetOwnership = const ['LAND', 'LIVESTOCK', 'BUSINESS'],
    this.profileUrl = '',
    this.status = 'ACTIVE',
    this.verificationStatus = 'PENDING',
    this.categoryCode = '',
    this.isChild = false,
  });

  factory MemberPayloadModel.fromMemberModel(
    MemberModel member,
    String organisationId,
  ) {
    return MemberPayloadModel(
      phoneNumber: member.phoneNumber ?? '',
      firstName: member.firstName ?? '',
      secondName: member.secondName ?? '',
      email: member.email,
      secondaryNumber: member.secondaryNumber,
      idNumber: member.idNumber,
      organisationId: organisationId,
      memberCategoryId: member.memberCategoryId,
      memberCategory: member.memberCategory,
      locationId: member.locationId,
      userId: member.userId ?? 1,
      joinDate:
          _formatDateTimeToRFC3339(member.joinDate) ??
          _formatDateTimeToRFC3339(DateTime.now()),
      dob: _formatDateTimeToRFC3339(member.dob),
      baptismDate: _formatDateTimeToRFC3339(member.baptismDate),
      accountNumber: member.accountNumber ?? member.phoneNumber ?? '',
      internalAccount: member.internalAccount ?? 'CUST',
      memberAccount: 'CUST',
      balance: member.balance ?? 0,
      address: member.address ?? '',
      locationCode: '',
      gender: member.gender ?? 'MALE',
      maritalStatus: member.maritalStatus ?? 'SINGLE',
      occupation: member.occupation,
      educationLevel: member.educationLevel ?? 'PRIMARY',
      tribe: member.tribe ?? 'example',
      disability: member.disability ?? 'NONE',
      nationality: member.nationality ?? 'Kenyan',
      incomeBracket: member.incomeBracket,
      employmentStatus: member.employmentStatus,
      householdSize: member.householdSize ?? 2,
      housingType: member.housingType ?? 'PERMANENT',
      assetOwnership:
          member.assetOwnership ?? ['LAND', 'LIVESTOCK', 'BUSINESS'],
      profileUrl: member.profileUrl ?? '',
      status: 'ACTIVE',
      verificationStatus: 'PENDING',
      categoryCode: '',
      isChild: member.isChild ?? false,
    );
  }

  factory MemberPayloadModel.fromMemberModelWithCategory(
    MemberModel member,
    String organisationId,
    MemberCategory? memberCategory,
  ) {
    return MemberPayloadModel(
      phoneNumber: member.phoneNumber ?? '',
      firstName: member.firstName ?? '',
      secondName: member.secondName ?? '',
      email: member.email,
      secondaryNumber: member.secondaryNumber,
      idNumber: member.idNumber,
      organisationId: organisationId,
      memberCategoryId: member.memberCategoryId,
      memberCategory: memberCategory ?? member.memberCategory,
      locationId: member.locationId,
      userId: member.userId ?? 1,
      joinDate:
          _formatDateTimeToRFC3339(member.joinDate) ??
          _formatDateTimeToRFC3339(DateTime.now()),
      dob: _formatDateTimeToRFC3339(member.dob),
      baptismDate: _formatDateTimeToRFC3339(member.baptismDate),
      accountNumber: member.accountNumber ?? member.phoneNumber ?? '',
      internalAccount: member.internalAccount ?? 'CUST',
      memberAccount: 'CUST',
      balance: member.balance ?? 0,
      address: member.address ?? '',
      locationCode: '',
      gender: member.gender ?? 'MALE',
      maritalStatus: member.maritalStatus ?? 'SINGLE',
      occupation: member.occupation,
      educationLevel: member.educationLevel ?? 'PRIMARY',
      tribe: member.tribe ?? 'example',
      disability: member.disability ?? 'NONE',
      nationality: member.nationality ?? 'Kenyan',
      incomeBracket: member.incomeBracket,
      employmentStatus: member.employmentStatus,
      householdSize: member.householdSize ?? 2,
      housingType: member.housingType ?? 'PERMANENT',
      assetOwnership:
          member.assetOwnership ?? ['LAND', 'LIVESTOCK', 'BUSINESS'],
      profileUrl: member.profileUrl ?? '',
      status: 'ACTIVE',
      verificationStatus: 'PENDING',
      categoryCode: '',
      isChild: member.isChild ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
    // Required personal information
    'phone_number':
        isChild ? "$phoneNumber#${Random().nextInt(100)}" : phoneNumber,
    'first_name': firstName,
    'second_name': secondName,
    'email': email,
    'secondary_number': secondaryNumber,
    'id_number': idNumber ?? '',

    // IDs and references
    'organisation_id': organisationId,
    'member_category_id': memberCategoryId ?? memberCategory?.id,
    'location_id': locationId,
    'user_id': userId,

    // Dates - properly formatted for API
    'join_date':
        _formatDateStringToRFC3339(joinDate) ??
        _formatDateTimeToRFC3339(DateTime.now()),
    'dob': _formatDateStringToRFC3339(dob),
    'baptism_date': _formatDateStringToRFC3339(baptismDate),

    // Account information
    'account_number': accountNumber,
    'internal_account': internalAccount,
    'member_account': memberAccount,
    'balance': balance,

    // Address and location
    'address': address,
    'location_code': locationCode,

    // Personal attributes
    'gender': gender,
    'marital_status': maritalStatus,
    'occupation': occupation,
    'education_level': educationLevel,
    'tribe': tribe,
    'disability': disability,
    'nationality': nationality,

    // Socioeconomic information
    'income_bracket': incomeBracket,
    'employment_status': employmentStatus,
    'household_size': householdSize,
    'housing_type': housingType,
    'asset_ownership': assetOwnership,

    // Status information
    'profile_url': profileUrl,
    'status': status,
    'verification_status': verificationStatus,
    'category_code': categoryCode,

    // Child indicator
    'is_child': isChild,
  };

  String toRawJson() => json.encode(toJson());

  static String? _formatDateTimeToRFC3339(DateTime? dateTime) {
    if (dateTime == null) return null;
    return dateTime.toUtc().toIso8601String();
  }

  static String? _formatDateStringToRFC3339(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    try {
      final dateTime = DateTime.parse(dateString);
      return _formatDateTimeToRFC3339(dateTime);
    } catch (e) {
      return null;
    }
  }

  String? get effectiveMemberCategoryId =>
      memberCategoryId ?? memberCategory?.id;

  String? get memberCategoryTitle => memberCategory?.title;

  bool get hasMemberCategory => effectiveMemberCategoryId != null;

  @override
  String toString() {
    return 'MemberPayloadModel(firstName: $firstName, secondName: $secondName, isChild: $isChild, memberCategory: ${memberCategoryTitle ?? memberCategoryId}, joinDate: $joinDate)';
  }
}
