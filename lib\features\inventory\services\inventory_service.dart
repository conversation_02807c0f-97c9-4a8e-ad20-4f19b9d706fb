import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../core/app/services/http_service.dart';

class InventoryService {
  final HttpService _httpService = Get.find<HttpService>();
  final Logger logger = Get.find<Logger>();

  // Fetch inventory records with pagination and filters
  Future<Map<String, dynamic>> fetchInventoryRecords({
    required int page,
    required int size,
    String? searchQuery,
    String? memberId,
    String? itemId,
    String? inventoryType,
    DateTime? startDate,
    DateTime? endDate,
    String? organisationId,
  }) async {
    try {
      // Prepare request parameters
      final Map<String, dynamic> params = {
        'organisation_id': organisationId,
        'page': page,
        'size': size,
      };

      // Add search query if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        params['search'] = searchQuery;
      }

      // Add member filter if provided
      if (memberId != null && memberId.isNotEmpty) {
        params['member_id'] = memberId;
      }

      // Add item filter if provided
      if (itemId != null && itemId.isNotEmpty) {
        params['item_id'] = itemId;
      }

      // Add inventory type filter if provided
      if (inventoryType != null && inventoryType.isNotEmpty) {
        params['inventory_type'] = inventoryType;
      }

      // Add date filters if provided
      if (startDate != null) {
        params['start_date'] = startDate.toUtc().toIso8601String();
      }

      if (endDate != null) {
        params['end_date'] = endDate.toUtc().toIso8601String();
      }

      final response = await _httpService.request(
        url: "${ApiUrls.getInventories}?page=$page&size=$size",
        method: Method.GET,
        params: params,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching inventory records: $e');
      rethrow;
    }
  }

  // Get inventory record by ID
  Future<Map<String, dynamic>> getInventoryRecordById(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.getInventoryById}$id",
        method: Method.GET,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching inventory record by ID: $e');
      rethrow;
    }
  }

  // Record new inventory
  Future<Map<String, dynamic>> recordInventory({
    required String memberId,
    required String organisationId,
    required String inventoryItemId,
    required int quantity,
    required String condition,
    required String inventoryType,
    required DateTime receivedAt,
    DateTime? expiryDate,
    String? batchNo,
    double? estimatedValue,
    DateTime? purchaseDate,
    double? cost,
    DateTime? warrantyExpiry,
    String? notes,
    List<Map<String, dynamic>>? media,
    bool isAnonymous = false,
    String? fullNames,
    String? email,
    String? phoneNumber,
    String? county,
    String? city,
    String? address,
  }) async {
    try {
      final Map<String, dynamic> inventoryData = {
        'member_id': memberId,
        'organisation_id': organisationId,
        'inventory_item_id': inventoryItemId,
        'quantity': quantity,
        'condition': condition,
        'inventory_type': inventoryType,
        'received_at': receivedAt.toUtc().toIso8601String(),
        'is_anonymous': isAnonymous,
      };

      // Add optional fields
      if (expiryDate != null) {
        inventoryData['expiry_date'] = expiryDate.toUtc().toIso8601String();
      }
      if (batchNo != null && batchNo.isNotEmpty) {
        inventoryData['batch_no'] = batchNo;
      }
      if (estimatedValue != null) {
        inventoryData['estimated_value'] = estimatedValue;
      }
      if (purchaseDate != null) {
        inventoryData['purchase_date'] = purchaseDate.toUtc().toIso8601String();
      }
      if (cost != null) {
        inventoryData['cost'] = cost;
      }
      if (warrantyExpiry != null) {
        inventoryData['warranty_expiry'] =
            warrantyExpiry.toUtc().toIso8601String();
      }
      if (notes != null && notes.isNotEmpty) {
        inventoryData['notes'] = notes;
      }
      if (media != null && media.isNotEmpty) {
        inventoryData['media'] = media;
      }

      // Add anonymous member details if applicable
      if (isAnonymous) {
        if (fullNames != null && fullNames.isNotEmpty) {
          inventoryData['full_names'] = fullNames;
        }
        if (email != null && email.isNotEmpty) {
          inventoryData['email'] = email;
        }
        if (phoneNumber != null && phoneNumber.isNotEmpty) {
          inventoryData['phone_number'] = phoneNumber;
        }
        if (county != null && county.isNotEmpty) {
          inventoryData['county'] = county;
        }
        if (city != null && city.isNotEmpty) {
          inventoryData['city'] = city;
        }
        if (address != null && address.isNotEmpty) {
          inventoryData['address'] = address;
        }
      }

      logger.d('Recording inventory with payload: $inventoryData');

      final response = await _httpService.request(
        url: ApiUrls.recordInventory,
        method: Method.POST,
        params: [inventoryData],
      );

      return response.data;
    } catch (e) {
      logger.e('Error recording inventory: $e');
      rethrow;
    }
  }

  // Update inventory record
  Future<Map<String, dynamic>> updateInventoryRecord({
    required String id,
    required String memberId,
    required String organisationId,
    required String inventoryItemId,
    required int quantity,
    required String condition,
    required String inventoryType,
    required DateTime receivedAt,
    DateTime? expiryDate,
    String? batchNo,
    double? estimatedValue,
    DateTime? purchaseDate,
    double? cost,
    DateTime? warrantyExpiry,
    String? notes,
    List<Map<String, dynamic>>? media,
    bool isAnonymous = false,
    String? fullNames,
    String? email,
    String? phoneNumber,
    String? county,
    String? city,
    String? address,
  }) async {
    try {
      final Map<String, dynamic> inventoryData = {
        'id': id,
        'member_id': memberId,
        'organisation_id': organisationId,
        'inventory_item_id': inventoryItemId,
        'quantity': quantity,
        'condition': condition,
        'inventory_type': inventoryType,
        'received_at': receivedAt.toUtc().toIso8601String(),
        'is_anonymous': isAnonymous,
      };

      // Add optional fields (same logic as create)
      if (expiryDate != null) {
        inventoryData['expiry_date'] = expiryDate.toUtc().toIso8601String();
      }
      if (batchNo != null && batchNo.isNotEmpty) {
        inventoryData['batch_no'] = batchNo;
      }
      if (estimatedValue != null) {
        inventoryData['estimated_value'] = estimatedValue;
      }
      if (purchaseDate != null) {
        inventoryData['purchase_date'] = purchaseDate.toUtc().toIso8601String();
      }
      if (cost != null) {
        inventoryData['cost'] = cost;
      }
      if (warrantyExpiry != null) {
        inventoryData['warranty_expiry'] =
            warrantyExpiry.toUtc().toIso8601String();
      }
      if (notes != null && notes.isNotEmpty) {
        inventoryData['notes'] = notes;
      }
      if (media != null && media.isNotEmpty) {
        inventoryData['media'] = media;
      }

      // Add anonymous member details if applicable
      if (isAnonymous) {
        if (fullNames != null && fullNames.isNotEmpty) {
          inventoryData['full_names'] = fullNames;
        }
        if (email != null && email.isNotEmpty) {
          inventoryData['email'] = email;
        }
        if (phoneNumber != null && phoneNumber.isNotEmpty) {
          inventoryData['phone_number'] = phoneNumber;
        }
        if (county != null && county.isNotEmpty) {
          inventoryData['county'] = county;
        }
        if (city != null && city.isNotEmpty) {
          inventoryData['city'] = city;
        }
        if (address != null && address.isNotEmpty) {
          inventoryData['address'] = address;
        }
      }

      logger.d('Updating inventory record with payload: $inventoryData');

      final response = await _httpService.request(
        url: "${ApiUrls.updateInventory}$id",
        method: Method.PUT,
        params: inventoryData,
      );

      return response.data;
    } catch (e) {
      logger.e('Error updating inventory record: $e');
      rethrow;
    }
  }

  // Delete inventory record
  Future<Map<String, dynamic>> deleteInventoryRecord(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.deleteInventory}$id",
        method: Method.DELETE,
      );

      return response.data;
    } catch (e) {
      logger.e('Error deleting inventory record: $e');
      rethrow;
    }
  }
}
