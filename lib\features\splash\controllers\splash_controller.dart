import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/storage_service.dart';
import '../../../core/app/constants/routes.dart';
import '../../auth/controllers/auth_controller.dart';

class SplashController extends GetxController {
  final StorageService _storageService = Get.find();
  final logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = true.obs;
  final RxString loadingMessage = 'Initializing...'.obs;

  @override
  void onInit() {
    super.onInit();
    initializeApp();
  }

  // Initialize the application
  Future<void> initializeApp() async {
    try {
      logger.i('Starting app initialization');

      // Show loading message
      loadingMessage.value = 'Loading...';

      // Wait for a minimum splash duration
      await Future.delayed(const Duration(seconds: 2));

      // Check if user is logged in
      loadingMessage.value = 'Checking authentication...';
      await _checkAuthenticationStatus();
    } catch (e) {
      logger.e('Error during app initialization: $e');
      loadingMessage.value = 'Initialization failed';

      // Navigate to login on error
      await Future.delayed(const Duration(seconds: 1));
      Get.offAllNamed(Routes.LOGIN);
    }
  }

  // Check authentication status
  Future<void> _checkAuthenticationStatus() async {
    try {
      // Check if user token exists
      final token = _storageService.read<String>('auth_token');
      final isFirstTime = _storageService.read<bool>('is_first_time') ?? true;

      if (isFirstTime) {
        // First time user - show onboarding or welcome screen
        await _storageService.write('is_first_time', false);
        Get.offAllNamed(Routes.LOGIN);
      } else if (token != null && token.isNotEmpty) {
        // User has token - try to validate it
        loadingMessage.value = 'Validating session...';

        // Get auth controller and check if user data exists
        final authController = Get.find<AuthController>();
        final userData = authController.user.value;

        if (userData != null) {
          // Valid user data - navigate to main app
          Get.offAllNamed(Routes.HOME);
        } else {
          // Invalid token - navigate to login
          Get.offAllNamed(Routes.LOGIN);
        }
      } else {
        // No token - navigate to login
        Get.offAllNamed(Routes.LOGIN);
      }
    } catch (e) {
      logger.e('Error checking authentication: $e');
      Get.offAllNamed(Routes.LOGIN);
    }
  }

  // Manual navigation to login
  void navigateToLogin() {
    Get.offAllNamed(Routes.LOGIN);
  }

  // Manual navigation to main app
  void navigateToMain() {
    Get.offAllNamed(Routes.HOME);
  }
}
