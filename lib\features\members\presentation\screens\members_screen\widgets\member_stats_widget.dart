import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/widgets/loading_animations.dart';
import 'package:onechurch/features/members/controllers/member_controller.dart';
import 'package:pluto_grid/pluto_grid.dart';

class MemberStatsWidget extends StatelessWidget {
  final bool isSelectMode;
  final VoidCallback onClearSelection;
  final PlutoGridStateManager? stateManager;

  const MemberStatsWidget({
    super.key,
    required this.isSelectMode,
    required this.onClearSelection,
    required this.stateManager,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MemberController>();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          Obx(
            () => Text(
              "Total Members: ${controller.totalItems}",
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const Spacer(),
          if (!isSelectMode)
            Obx(
              () => Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (controller.selectedMembers.isNotEmpty)
                    Text(
                      "Selected: ${controller.selectedMembers.length}",
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  const Gap(8),
                  if (controller.selectedMembers.isNotEmpty)
                    TextButton.icon(
                      onPressed: onClearSelection,
                      icon: const Icon(Icons.clear_all, size: 18),
                      label: const Text(
                        'Clear Selection',
                        style: TextStyle(fontSize: 12),
                      ),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        visualDensity: VisualDensity.compact,
                      ),
                    ),
                ],
              ),
            ),
          const Gap(16),
          Obx(
            () =>
                controller.isLoading.value
                    ? const LoadingAnimations()
                    : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}
