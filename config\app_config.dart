import 'package:get/get.dart';

/// Global configuration service for the SMS application
/// Manages application-wide settings and configuration parameters
class AppConfig extends GetxController {
  static AppConfig get instance => Get.find<AppConfig>();

  // Configuration parameters
  final RxString _baseUrl = ''.obs;
  final RxString _organisationId = ''.obs;
  final RxString _apiKey = ''.obs;
  final RxBool _isInitialized = false.obs;

  // Getters
  String get baseUrl => _baseUrl.value;
  String get organisationId => _organisationId.value;
  String get apiKey => _apiKey.value;
  bool get isInitialized => _isInitialized.value;

  /// Initialize the application configuration
  /// This should be called at app startup with the required parameters
  void initialize({
    required String baseUrl,
    required String organisationId,
    String? apiKey,
  }) {
    _baseUrl.value = baseUrl;
    _organisationId.value = organisationId;
    _apiKey.value = apiKey ?? '';
    _isInitialized.value = true;
    
    Get.log('AppConfig initialized with baseUrl: $baseUrl, organisationId: $organisationId');
  }

  /// Update configuration parameters
  void updateConfig({
    String? baseUrl,
    String? organisationId,
    String? apiKey,
  }) {
    if (baseUrl != null) _baseUrl.value = baseUrl;
    if (organisationId != null) _organisationId.value = organisationId;
    if (apiKey != null) _apiKey.value = apiKey;
  }

  /// Reset configuration
  void reset() {
    _baseUrl.value = '';
    _organisationId.value = '';
    _apiKey.value = '';
    _isInitialized.value = false;
  }

  /// Validate if configuration is properly set
  bool isValidConfig() {
    return _baseUrl.value.isNotEmpty && _organisationId.value.isNotEmpty;
  }
}
