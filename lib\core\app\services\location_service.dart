import 'dart:async';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:geolocator/geolocator.dart' as geo;
import 'package:logger/logger.dart';
import '../constants/enums.dart' as enums;
import '../utils/location_utils.dart';
import 'storage_service.dart';

/// Comprehensive location service using GetX state management
/// Handles location tracking across all platforms with automatic initialization
class LocationService extends GetxService {
  // Dependencies
  final Logger _logger = Get.put<Logger>(Logger());
  late final StorageService _storageService;

  // Location data reactive variables
  final Rx<geo.Position?> _currentPosition = Rx<geo.Position?>(null);
  final RxDouble _latitude = 0.0.obs;
  final RxDouble _longitude = 0.0.obs;
  final RxString _locationName = ''.obs;

  // Status reactive variables
  final Rx<enums.LocationPermissionStatus> _permissionStatus =
      enums.LocationPermissionStatus.unableToDetermine.obs;
  final Rx<enums.LocationServiceStatus> _serviceStatus =
      enums.LocationServiceStatus.disabled.obs;
  final Rx<enums.LocationTrackingStatus> _trackingStatus =
      enums.LocationTrackingStatus.stopped.obs;
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;

  // Configuration
  final Rx<enums.LocationAccuracy> _accuracy = enums.LocationAccuracy.high.obs;
  final RxInt _updateInterval = 10.obs; // seconds
  final RxInt _distanceFilter = 10.obs; // meters
  final RxBool _enableBackgroundTracking = false.obs;

  // Stream subscription
  StreamSubscription<geo.Position>? _positionStreamSubscription;
  Timer? _periodicUpdateTimer;

  // Cache keys
  static const String _cacheKeyPosition = 'cached_position';
  static const String _cacheKeyLocationName = 'cached_location_name';
  static const String _cacheKeyTimestamp = 'cached_timestamp';
  static const String _cacheKeySettings = 'location_settings';

  // Getters for reactive data
  geo.Position? get currentPosition => _currentPosition.value;
  double get latitude => _latitude.value;
  double get longitude => _longitude.value;
  String get locationName => _locationName.value;
  enums.LocationPermissionStatus get permissionStatus =>
      _permissionStatus.value;
  enums.LocationServiceStatus get serviceStatus => _serviceStatus.value;
  enums.LocationTrackingStatus get trackingStatus => _trackingStatus.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  enums.LocationAccuracy get accuracy => _accuracy.value;
  int get updateInterval => _updateInterval.value;
  int get distanceFilter => _distanceFilter.value;
  bool get enableBackgroundTracking => _enableBackgroundTracking.value;

  // Computed getters
  bool get hasValidLocation =>
      LocationUtils.isValidCoordinates(latitude, longitude);
  bool get isPermissionGranted =>
      LocationUtils.isPermissionSufficient(permissionStatus);
  bool get isServiceEnabled =>
      serviceStatus == enums.LocationServiceStatus.enabled;
  bool get isTracking =>
      trackingStatus == enums.LocationTrackingStatus.tracking;
  String get formattedCoordinates =>
      LocationUtils.formatCoordinates(latitude, longitude);

  /// Initialize the location service
  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeService();
  }

  /// Initialize service dependencies and load cached data
  Future<void> _initializeService() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Initialize dependencies
      _storageService = Get.find<StorageService>();

      // Load cached settings
      await _loadCachedSettings();

      // Load cached location data
      await _loadCachedLocation();

      // Check initial permissions and service status
      await _checkInitialStatus();

      // Auto-start tracking if permissions are granted
      if (isPermissionGranted && isServiceEnabled) {
        await startTracking();
      }

      _logger.i('LocationService initialized successfully');
    } catch (e) {
      _errorMessage.value = 'Failed to initialize location service: $e';
      _logger.e('LocationService initialization error: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Check initial permission and service status
  Future<void> _checkInitialStatus() async {
    try {
      // Check if location services are enabled
      final serviceEnabled = await LocationUtils.isLocationServiceEnabled();
      _serviceStatus.value =
          serviceEnabled
              ? enums.LocationServiceStatus.enabled
              : enums.LocationServiceStatus.disabled;

      // Check permission status
      final permission = await LocationUtils.checkPermission();
      _permissionStatus.value = permission;

      _logger.i('Location service status: ${serviceStatus.displayName}');
      _logger.i('Permission status: ${permissionStatus.displayName}');
    } catch (e) {
      _logger.e('Error checking initial status: $e');
      _errorMessage.value = 'Error checking location status: $e';
    }
  }

  /// Load cached location settings
  Future<void> _loadCachedSettings() async {
    try {
      final settingsJson = _storageService.read(_cacheKeySettings);
      if (settingsJson != null) {
        final settings = jsonDecode(settingsJson);
        _accuracy.value =
            enums.LocationAccuracyExtension.fromString(
              settings['accuracy'] ?? 'HIGH',
            ) ??
            enums.LocationAccuracy.high;
        _updateInterval.value = settings['updateInterval'] ?? 10;
        _distanceFilter.value = settings['distanceFilter'] ?? 10;
        _enableBackgroundTracking.value =
            settings['enableBackgroundTracking'] ?? false;
      }
    } catch (e) {
      _logger.w('Error loading cached settings: $e');
    }
  }

  /// Load cached location data
  Future<void> _loadCachedLocation() async {
    try {
      final positionJson = _storageService.read(_cacheKeyPosition);
      final locationName = _storageService.read(_cacheKeyLocationName);
      final timestamp = _storageService.read(_cacheKeyTimestamp);

      if (positionJson != null && timestamp != null) {
        final positionData = jsonDecode(positionJson);
        final cachedTime = DateTime.parse(timestamp);
        final now = DateTime.now();

        // Use cached data if it's less than 1 hour old
        if (now.difference(cachedTime).inHours < 1) {
          _latitude.value = positionData['latitude']?.toDouble() ?? 0.0;
          _longitude.value = positionData['longitude']?.toDouble() ?? 0.0;
          _locationName.value = locationName ?? '';

          _logger.i('Loaded cached location: $formattedCoordinates');
        }
      }
    } catch (e) {
      _logger.w('Error loading cached location: $e');
    }
  }

  /// Save current settings to cache
  Future<void> _saveCachedSettings() async {
    try {
      final settings = {
        'accuracy': accuracy.displayName,
        'updateInterval': updateInterval,
        'distanceFilter': distanceFilter,
        'enableBackgroundTracking': enableBackgroundTracking,
      };
      await _storageService.write(_cacheKeySettings, jsonEncode(settings));
    } catch (e) {
      _logger.w('Error saving settings: $e');
    }
  }

  /// Save current location to cache
  Future<void> _saveCachedLocation() async {
    try {
      if (hasValidLocation) {
        final positionData = {'latitude': latitude, 'longitude': longitude};
        await _storageService.write(
          _cacheKeyPosition,
          jsonEncode(positionData),
        );
        await _storageService.write(_cacheKeyLocationName, locationName);
        await _storageService.write(
          _cacheKeyTimestamp,
          DateTime.now().toIso8601String(),
        );
      }
    } catch (e) {
      _logger.w('Error saving location: $e');
    }
  }

  /// Request location permissions
  Future<bool> requestPermissions() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Check if location services are enabled first
      final serviceEnabled = await LocationUtils.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _serviceStatus.value = enums.LocationServiceStatus.disabled;
        _errorMessage.value = 'Location services are disabled';
        return false;
      }

      _serviceStatus.value = enums.LocationServiceStatus.enabled;

      // Request permission
      final permission = await LocationUtils.requestPermission();
      _permissionStatus.value = permission;

      final isGranted = LocationUtils.isPermissionSufficient(permission);
      if (isGranted) {
        _logger.i('Location permission granted');
        // Auto-start tracking if permission is granted
        await startTracking();
      } else {
        _errorMessage.value = LocationUtils.getPermissionDescription(
          permission,
        );
        _logger.w('Location permission denied: ${permission.displayName}');
      }

      return isGranted;
    } catch (e) {
      _errorMessage.value = 'Error requesting permissions: $e';
      _logger.e('Error requesting permissions: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Start location tracking
  Future<bool> startTracking() async {
    try {
      // Check prerequisites
      if (!isPermissionGranted) {
        _errorMessage.value = 'Location permission not granted';
        return false;
      }

      if (!isServiceEnabled) {
        _errorMessage.value = 'Location services are disabled';
        return false;
      }

      if (isTracking) {
        _logger.w('Location tracking is already active');
        return true;
      }

      _trackingStatus.value = enums.LocationTrackingStatus.starting;
      _errorMessage.value = '';

      // Get initial position
      await _getCurrentLocation();

      // Start position stream
      _startPositionStream();

      // Start periodic updates for reverse geocoding
      _startPeriodicUpdates();

      _trackingStatus.value = enums.LocationTrackingStatus.tracking;
      _logger.i('Location tracking started');
      return true;
    } catch (e) {
      _trackingStatus.value = enums.LocationTrackingStatus.error;
      _errorMessage.value = 'Error starting location tracking: $e';
      _logger.e('Error starting tracking: $e');
      return false;
    }
  }

  /// Stop location tracking
  Future<void> stopTracking() async {
    try {
      _trackingStatus.value = enums.LocationTrackingStatus.stopped;

      // Cancel position stream subscription
      await _positionStreamSubscription?.cancel();
      _positionStreamSubscription = null;

      // Cancel periodic timer
      _periodicUpdateTimer?.cancel();
      _periodicUpdateTimer = null;

      _logger.i('Location tracking stopped');
    } catch (e) {
      _logger.e('Error stopping tracking: $e');
    }
  }

  /// Pause location tracking
  Future<void> pauseTracking() async {
    try {
      if (!isTracking) return;

      _trackingStatus.value = enums.LocationTrackingStatus.paused;

      // Cancel streams but keep cached data
      await _positionStreamSubscription?.cancel();
      _positionStreamSubscription = null;
      _periodicUpdateTimer?.cancel();
      _periodicUpdateTimer = null;

      _logger.i('Location tracking paused');
    } catch (e) {
      _logger.e('Error pausing tracking: $e');
    }
  }

  /// Resume location tracking
  Future<bool> resumeTracking() async {
    try {
      if (trackingStatus != enums.LocationTrackingStatus.paused) {
        return await startTracking();
      }

      _trackingStatus.value = enums.LocationTrackingStatus.starting;

      // Restart streams
      _startPositionStream();
      _startPeriodicUpdates();

      _trackingStatus.value = enums.LocationTrackingStatus.tracking;
      _logger.i('Location tracking resumed');
      return true;
    } catch (e) {
      _trackingStatus.value = enums.LocationTrackingStatus.error;
      _errorMessage.value = 'Error resuming tracking: $e';
      _logger.e('Error resuming tracking: $e');
      return false;
    }
  }

  /// Get current location once
  Future<geo.Position?> getCurrentLocation() async {
    try {
      if (!isPermissionGranted) {
        throw Exception('Location permission not granted');
      }

      _isLoading.value = true;
      return await _getCurrentLocation();
    } catch (e) {
      _errorMessage.value = 'Error getting current location: $e';
      _logger.e('Error getting current location: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Internal method to get current location
  Future<geo.Position?> _getCurrentLocation() async {
    try {
      final position = await LocationUtils.getCurrentPosition(
        accuracy: accuracy,
        timeLimit: const Duration(seconds: 30),
      );

      if (position != null) {
        await _updatePosition(position);
        return position;
      }
      return null;
    } catch (e) {
      _logger.e('Error getting current position: $e');
      rethrow;
    }
  }

  /// Start position stream for continuous tracking
  void _startPositionStream() {
    try {
      final positionStream = LocationUtils.getPositionStream(
        accuracy: accuracy,
        distanceFilter: distanceFilter,
      );

      _positionStreamSubscription = positionStream.listen(
        (geo.Position position) async {
          await _updatePosition(position);
        },
        onError: (error) {
          _logger.e('Position stream error: $error');
          _errorMessage.value = 'Location tracking error: $error';
          _trackingStatus.value = enums.LocationTrackingStatus.error;
        },
      );
    } catch (e) {
      _logger.e('Error starting position stream: $e');
      _trackingStatus.value = enums.LocationTrackingStatus.error;
    }
  }

  /// Start periodic updates for reverse geocoding
  void _startPeriodicUpdates() {
    _periodicUpdateTimer = Timer.periodic(Duration(seconds: updateInterval), (
      timer,
    ) async {
      if (hasValidLocation && locationName.isEmpty) {
        await _reverseGeocode(latitude, longitude);
      }
    });
  }

  /// Update position data
  Future<void> _updatePosition(geo.Position position) async {
    try {
      _currentPosition.value = position;
      _latitude.value = position.latitude;
      _longitude.value = position.longitude;

      // Cache the position
      await _saveCachedLocation();

      // Reverse geocode if location name is empty or coordinates changed significantly
      if (locationName.isEmpty || _shouldUpdateLocationName(position)) {
        await _reverseGeocode(position.latitude, position.longitude);
      }

      _logger.d('Position updated: $formattedCoordinates');
    } catch (e) {
      _logger.e('Error updating position: $e');
    }
  }

  /// Check if location name should be updated
  bool _shouldUpdateLocationName(geo.Position newPosition) {
    if (_currentPosition.value == null) return true;

    final distance = LocationUtils.calculateDistance(
      _currentPosition.value!.latitude,
      _currentPosition.value!.longitude,
      newPosition.latitude,
      newPosition.longitude,
    );

    // Update location name if moved more than 100 meters
    return distance > 100;
  }

  /// Reverse geocode coordinates to get location name
  Future<void> _reverseGeocode(double latitude, double longitude) async {
    try {
      // For now, we'll use a simple placeholder implementation
      // In a real app, you would use a geocoding service like Google Maps API
      // or implement a proper reverse geocoding solution

      // Simple placeholder that creates a location name from coordinates
      final locationName =
          'Location: ${latitude.toStringAsFixed(4)}, ${longitude.toStringAsFixed(4)}';

      _locationName.value = locationName;
      await _saveCachedLocation();

      _logger.d('Reverse geocoded location: $locationName');
    } catch (e) {
      _logger.w('Error reverse geocoding: $e');
      // Don't throw error, just log it as reverse geocoding is not critical
    }
  }

  /// Update location tracking settings
  Future<void> updateSettings({
    enums.LocationAccuracy? accuracy,
    int? updateInterval,
    int? distanceFilter,
    bool? enableBackgroundTracking,
  }) async {
    try {
      bool needsRestart = false;

      if (accuracy != null && accuracy != this.accuracy) {
        _accuracy.value = accuracy;
        needsRestart = true;
      }

      if (updateInterval != null && updateInterval != this.updateInterval) {
        _updateInterval.value = updateInterval;
        needsRestart = true;
      }

      if (distanceFilter != null && distanceFilter != this.distanceFilter) {
        _distanceFilter.value = distanceFilter;
        needsRestart = true;
      }

      if (enableBackgroundTracking != null) {
        _enableBackgroundTracking.value = enableBackgroundTracking;
      }

      // Save settings to cache
      await _saveCachedSettings();

      // Restart tracking if settings changed and tracking is active
      if (needsRestart && isTracking) {
        await stopTracking();
        await startTracking();
      }

      _logger.i('Location settings updated');
    } catch (e) {
      _logger.e('Error updating settings: $e');
      _errorMessage.value = 'Error updating location settings: $e';
    }
  }

  /// Open location settings
  Future<bool> openLocationSettings() async {
    return await LocationUtils.openLocationSettings();
  }

  /// Open app settings
  Future<bool> openAppSettings() async {
    return await LocationUtils.openAppSettings();
  }

  /// Get distance to a specific location
  double getDistanceTo(double targetLatitude, double targetLongitude) {
    if (!hasValidLocation) return 0.0;

    return LocationUtils.calculateDistance(
      latitude,
      longitude,
      targetLatitude,
      targetLongitude,
    );
  }

  /// Get bearing to a specific location
  double getBearingTo(double targetLatitude, double targetLongitude) {
    if (!hasValidLocation) return 0.0;

    return LocationUtils.calculateBearing(
      latitude,
      longitude,
      targetLatitude,
      targetLongitude,
    );
  }

  /// Check if user is within a specific radius of a location
  bool isWithinRadius(
    double targetLatitude,
    double targetLongitude,
    double radiusInMeters,
  ) {
    if (!hasValidLocation) return false;

    final distance = getDistanceTo(targetLatitude, targetLongitude);
    return distance <= radiusInMeters;
  }

  /// Cleanup when service is disposed
  @override
  void onClose() {
    stopTracking();
    super.onClose();
  }
}
