import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/services/http_service.dart';
import 'package:onechurch/core/app/services/storage_service.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import 'package:onechurch/features/members/models/member_category_model.dart';

class MemberService {
  final HttpService _httpService = Get.find();
  final StorageService _storageService = Get.find<StorageService>();
  final logger = Get.find<Logger>();

  // Fetch member categories with pagination
  Future<MemberCategoryResponse> fetchMemberCategories({
    int page = 0,
    int size = 10,
    String? search,
  }) async {
    try {
      final Map<String, dynamic> params = {'page': page, 'size': size};

      // Add search parameter if provided
      if (search != null && search.isNotEmpty) {
        params['search'] = search;
      }

      final response = await _httpService.request(
        url:
            'member/category/?organisation_id=${Get.find<AuthController>().organizationId.value}',
        method: Method.GET,
        params: params,
      );
      return MemberCategoryResponse.fromJson(response.data);
    } catch (e) {
      logger.e('Error fetching member categories: $e');
      return MemberCategoryResponse(
        status: false,
        message: 'Failed to fetch member categories: $e',
        data: null,
      );
    }
  }

  // Register member to an organization
  Future<Map<String, dynamic>> registerMember({
    required String organisationId,
    required String memberCategoryId,
    required String categoryCode,
    required Map<String, dynamic> formData,
    String? phoneNumber,
    String? firstName,
    String? secondName,
    String? email,
    String? joinDate,
    String? dob,
    String? address,
    String? idNumber,
    String? internalAccount,
    String? memberAccount,
    String? locationId,
    String? locationCode,
  }) async {
    try {
      // Get user data from storage
      final userData = _storageService.getUserData();

      // Create member data object
      final Map<String, dynamic> memberData = {
        'phone_number': phoneNumber ?? userData?['phone_number'],
        'first_name': firstName ?? userData?['first_name'],
        'second_name': secondName ?? userData?['second_name'],
        'email': email ?? userData?['email'],
        'join_date': DateTime.now().toUtc().toIso8601String(),
        'dob': dob,
        'address': address ?? '',
        'id_number': idNumber ?? userData?['id_number'] ?? '',
        'organisation_id': organisationId,
        'internal_account': internalAccount ?? 'CUST',
        'member_account': memberAccount ?? 'CUST',
        'location_id': locationId,
        'location_code': locationCode ?? '',
        'member_category_id': memberCategoryId,
        'category_code': categoryCode,
      };
      memberData.addAll(formData);
      // Send request to register member
      final response = await _httpService.request(
        url: 'member/members/',
        method: Method.POST,
        params: [memberData],
      );

      return response.data;
    } catch (e) {
      logger.e('Error registering member: $e');
      return {'status': false, 'message': 'Failed to register member: $e'};
    }
  }
}
