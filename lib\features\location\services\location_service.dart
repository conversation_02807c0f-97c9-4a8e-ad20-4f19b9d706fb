import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../auth/controllers/auth_controller.dart';

class LocationService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();
  final authController = Get.find<AuthController>();

  // Initialize the service
  LocationService() {
    _httpService.initializeDio();
  }

  // Fetch locations
  Future<Map<String, dynamic>> fetchLocations({
    int page = 0,
    int size = 10,
    String? search,
  }) async {
    try {
      String url = "${ApiUrls.baseUrl}/locations?page=$page&size=$size";
      
      // Add organisation_id parameter
      final organisationId = authController.currentOrg.value?.id;
      if (organisationId != null) {
        url += "&organisation_id=$organisationId";
      }

      if (search != null && search.isNotEmpty) {
        url += "&search=${Uri.encodeComponent(search)}";
      }

      logger.i('Fetching locations with URL: $url');
      final response = await _httpService.request(url: url, method: Method.GET);

      return response.data;
    } catch (e) {
      logger.e('Error fetching locations: $e');
      return {
        'status': false,
        'message': 'Failed to fetch locations: $e',
        'data': null,
      };
    }
  }

  // Create a new location
  Future<Map<String, dynamic>> createLocation({
    required String name,
    required String address,
    required double latitude,
    required double longitude,
    String? description,
  }) async {
    try {
      final Map<String, dynamic> locationData = {
        'name': name,
        'address': address,
        'latitude': latitude,
        'longitude': longitude,
        'organisation_id': authController.currentOrg.value?.id,
      };

      if (description != null && description.isNotEmpty) {
        locationData['description'] = description;
      }

      logger.d('Creating location with payload: $locationData');

      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/locations',
        method: Method.POST,
        params: locationData,
      );

      return response.data;
    } catch (e) {
      logger.e('Error creating location: $e');
      return {
        'status': false,
        'message': 'Failed to create location: $e',
        'data': null,
      };
    }
  }

  // Update a location
  Future<Map<String, dynamic>> updateLocation({
    required String id,
    required String name,
    required String address,
    required double latitude,
    required double longitude,
    String? description,
  }) async {
    try {
      final Map<String, dynamic> locationData = {
        'name': name,
        'address': address,
        'latitude': latitude,
        'longitude': longitude,
      };

      if (description != null && description.isNotEmpty) {
        locationData['description'] = description;
      }

      logger.d('Updating location with payload: $locationData');

      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/locations/$id',
        method: Method.PUT,
        params: locationData,
      );

      return response.data;
    } catch (e) {
      logger.e('Error updating location: $e');
      return {
        'status': false,
        'message': 'Failed to update location: $e',
        'data': null,
      };
    }
  }

  // Delete a location
  Future<Map<String, dynamic>> deleteLocation(String id) async {
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/locations/$id',
        method: Method.DELETE,
      );

      return response.data;
    } catch (e) {
      logger.e('Error deleting location: $e');
      return {
        'status': false,
        'message': 'Failed to delete location: $e',
        'data': null,
      };
    }
  }
}
