// To parse this JSON data, do
//
//     final userModel = userModelFromJson(jsonString);

import 'dart:convert';

UserModel userModelFromJson(String str) => UserModel.fromJson(json.decode(str));

String userModelToJson(UserModel data) => json.encode(data.toJson());

class UserModel {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? phoneNumber;
  final String? firstName;
  final String? secondName;
  final String? username;
  final String? email;
  final String? idNumber;
  final int? balance;
  final String? birthDate;
  final String? countryCode;
  final String? county;
  final String? subCounty;
  final String? ward;
  final String? secondaryNumber;
  final String? profileUrl;
  final String? role;
  final String? status;
  final dynamic devices;

  UserModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.phoneNumber,
    this.firstName,
    this.secondName,
    this.username,
    this.email,
    this.idNumber,
    this.balance,
    this.birthDate,
    this.countryCode,
    this.county,
    this.subCounty,
    this.ward,
    this.secondaryNumber,
    this.profileUrl,
    this.role,
    this.status,
    this.devices,
  });

  UserModel copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? phoneNumber,
    String? firstName,
    String? secondName,
    String? username,
    String? email,
    String? idNumber,
    int? balance,
    String? birthDate,
    String? countryCode,
    String? county,
    String? subCounty,
    String? ward,
    String? secondaryNumber,
    String? profileUrl,
    String? role,
    String? status,
    dynamic devices,
  }) => UserModel(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    firstName: firstName ?? this.firstName,
    secondName: secondName ?? this.secondName,
    username: username ?? this.username,
    email: email ?? this.email,
    idNumber: idNumber ?? this.idNumber,
    balance: balance ?? this.balance,
    birthDate: birthDate ?? this.birthDate,
    countryCode: countryCode ?? this.countryCode,
    county: county ?? this.county,
    subCounty: subCounty ?? this.subCounty,
    ward: ward ?? this.ward,
    secondaryNumber: secondaryNumber ?? this.secondaryNumber,
    profileUrl: profileUrl ?? this.profileUrl,
    role: role ?? this.role,
    status: status ?? this.status,
    devices: devices ?? this.devices,
  );

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
    id: json["ID"],
    createdAt:
        json["CreatedAt"] == null ? null : DateTime.parse(json["CreatedAt"]),
    updatedAt:
        json["UpdatedAt"] == null ? null : DateTime.parse(json["UpdatedAt"]),
    deletedAt: json["DeletedAt"],
    phoneNumber: json["phone_number"],
    firstName: json["first_name"],
    secondName: json["second_name"],
    username: json["username"],
    email: json["email"],
    idNumber: json["id_number"],
    balance: json["balance"],
    birthDate: json["birth_date"],
    countryCode: json["country_code"],
    county: json["county"],
    subCounty: json["sub_county"],
    ward: json["ward"],
    secondaryNumber: json["secondary_number"],
    profileUrl: json["profile_url"],
    role: json["role"],
    status: json["status"],
    devices: json["devices"],
  );

  Map<String, dynamic> toJson() => {
    "ID": id,
    "CreatedAt": createdAt?.toIso8601String(),
    "UpdatedAt": updatedAt?.toIso8601String(),
    "DeletedAt": deletedAt,
    "phone_number": phoneNumber,
    "first_name": firstName,
    "second_name": secondName,
    "username": username,
    "email": email,
    "id_number": idNumber,
    "balance": balance,
    "birth_date": birthDate,
    "country_code": countryCode,
    "county": county,
    "sub_county": subCounty,
    "ward": ward,
    "secondary_number": secondaryNumber,
    "profile_url": profileUrl,
    "role": role,
    "status": status,
    "devices": devices,
  };
}
