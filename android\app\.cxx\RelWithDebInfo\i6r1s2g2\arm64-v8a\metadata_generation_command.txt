                        -HC:\Users\<USER>\Documents\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-D<PERSON>DROID_PLATFORM=android-23
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=C:\Users\<USER>\Documents\sdks\android\ndk\28.0.13004108
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\Documents\sdks\android\ndk\28.0.13004108
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\Documents\sdks\android\ndk\28.0.13004108\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\Documents\sdks\android\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\onechurch v3\john316-flutter-front\build\app\intermediates\cxx\RelWithDebInfo\i6r1s2g2\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\onechurch v3\john316-flutter-front\build\app\intermediates\cxx\RelWithDebInfo\i6r1s2g2\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BC:\Users\<USER>\Desktop\onechurch v3\john316-flutter-front\android\app\.cxx\RelWithDebInfo\i6r1s2g2\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2