<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="church management app">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="onechurch">

  <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="shortcut icon" href="/favicon.ico" />
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
  <link rel="manifest" href="/site.webmanifest" />

  <!-- Favicon -->
  <title>onechurch</title>
  <base href="$FLUTTER_BASE_HREF">

  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      overflow: hidden;
    }

    #splash {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      z-index: 9999;
      transition: opacity 0.5s ease;
    }

    #splash.fade-out {
      opacity: 0;
      pointer-events: none;
    }
    .splash-logo {
      width: 180px;
      height: auto;
      animation: pulse 2s infinite;
    }
    @keyframes pulse {
      0% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.1); opacity: 0.8; }
      100% { transform: scale(1); opacity: 1; }
    }
  </style>
</head>
<body>
  <!-- 🔥 Animated Logo Splash Screen -->
  <div id="splash">
    <img src="assets/logo.png" class="splash-logo" alt="OneChurch Logo" />
    <p>Loading OneChurch...</p>
  </div>

  <!-- Flutter App Scripts -->
  <script src="flutter_bootstrap.js" async></script>
  <script src="assets/packages/libphonenumber_plugin/js/libphonenumber.js"></script>
  <script src="assets/packages/libphonenumber_plugin/js/stringbuffer.js"></script>

  <!-- Remove splash after Flutter loads -->
  <script>
    window.addEventListener("load", function () {
      const splash = document.getElementById("splash");
      if (splash) {
        splash.classList.add("fade-out");
        setTimeout(() => splash.remove(), 600);
      }
    });
  </script>
</body>

</html>