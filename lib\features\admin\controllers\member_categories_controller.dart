import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../data/models/member_category_model.dart';

class MemberCategoriesController extends GetxController {
  final titleController = TextEditingController();
  final descriptionController = TextEditingController();
  final codeController = TextEditingController();
  final searchController = TextEditingController();

  final RxList<MemberCategory> categories = <MemberCategory>[].obs;
  final RxList<MemberCategory> filteredCategories = <MemberCategory>[].obs;
  final RxBool isLoading = false.obs;
  final RxString searchQuery = ''.obs;
  final RxString errorMessage = ''.obs;
  final RxBool isGeneral = false.obs;
  String? organisationId = Get.find<AuthController>().currentOrg.value?.id;

  final HttpService _httpService = Get.find();

  @override
  void onInit() {
    super.onInit();
    fetchCategories();
    searchController.addListener(_onSearchChanged);
    _httpService.initializeDio();
  }

  @override
  void onClose() {
    titleController.dispose();
    descriptionController.dispose();
    codeController.dispose();
    searchController.dispose();
    super.onClose();
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    filterCategories();
  }

  void filterCategories() {
    if (searchQuery.isEmpty) {
      filteredCategories.value = categories;
      return;
    }

    filteredCategories.value =
        categories.where((category) {
          return (category.title?.toLowerCase().contains(
                    searchQuery.toLowerCase(),
                  ) ??
                  false) ||
              (category.code?.toLowerCase().contains(
                    searchQuery.toLowerCase(),
                  ) ??
                  false) ||
              (category.description?.toLowerCase().contains(
                    searchQuery.toLowerCase(),
                  ) ??
                  false);
        }).toList();
  }

  Future<void> fetchCategories() async {
    isLoading.value = true;
    try {
      final response = await _httpService.request(
        url: ApiUrls.memberCategories,
        method: Method.GET,
      );

      if (response.data['status'] == true) {
        final List<dynamic> data = response.data['data'];
        categories.value =
            data.map((json) => MemberCategory.fromJson(json)).toList();
        filterCategories();
      } else {
        errorMessage.value =
            response.data['message'] ?? 'Failed to fetch categories';
      }
    } catch (e) {
      errorMessage.value = 'Error fetching categories: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> addCategory() async {
    if (!_validateForm()) return false;

    isLoading.value = true;
    try {
      final response = await _httpService.request(
        url: ApiUrls.createCustomerCategory,
        method: Method.POST,
        params: {
          'title': titleController.text,
          'description': descriptionController.text,
          'code': codeController.text.toUpperCase(),
          'organisation_id': organisationId,
          'is_general': isGeneral.value,
        },
      );

      if (response.data['status'] == true) {
        await fetchCategories();
        _clearForm();
        _showSuccessMessage('Category added successfully');
        return true;
      } else {
        _showErrorMessage(response.data['message'] ?? 'Failed to add category');
        return false;
      }
    } catch (e) {
      _showErrorMessage('Error adding category: ${e.toString()}');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> updateCategory(String? id) async {
    if (!_validateForm()) return false;

    isLoading.value = true;
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.updateCustomerCategory}$id/',
        method: Method.PUT,
        params: {
          'title': titleController.text,
          'description': descriptionController.text,
          'code': codeController.text.toUpperCase(),
          'organisation_id': organisationId,
          'is_general': isGeneral.value,
        },
      );

      if (response.data['status'] == true) {
        await fetchCategories();
        _clearForm();

        _showSuccessMessage('Category updated successfully');
        return true;
      } else {
        _showErrorMessage(
          response.data['message'] ?? 'Failed to update category',
        );
        return false;
      }
    } catch (e) {
      _showErrorMessage('Error updating category: ${e.toString()}');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> toggleCategoryStatus(String? id, bool currentStatus) async {
    isLoading.value = true;
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.updateCustomerCategory}$id/',
        method: Method.PUT,
        params: {'is_active': !currentStatus},
      );

      if (response.data["status"] == true) {
        await fetchCategories();
        _showSuccessMessage('Category status updated successfully');
      } else {
        _showErrorMessage(
          response.data["message"] ?? 'Failed to update category status',
        );
      }
    } catch (e) {
      _showErrorMessage('Error updating category status: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteCategory(String id, BuildContext context) async {
    Get.dialog(
      AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this category?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              isLoading.value = true;

              try {
                final response = await _httpService.request(
                  url: '${ApiUrls.deleteCustomerCategory}$id/',
                  method: Method.DELETE,
                );

                if (response.data["status"] == true) {
                  await fetchCategories();
                  _showSuccessMessage('Category deleted successfully');
                } else {
                  _showErrorMessage(
                    response.data["message"] ?? 'Failed to delete category',
                  );
                }
              } catch (e) {
                _showErrorMessage('Error deleting category: ${e.toString()}');
              } finally {
                isLoading.value = false;
              }
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void prepareForEdit(MemberCategory category) {
    titleController.text = category.title ?? '';
    descriptionController.text = category.description ?? '';
    codeController.text = category.code ?? '';
    organisationId = category.organisationId ?? '';
    isGeneral.value = category.isGeneral ?? false;
  }

  bool _validateForm() {
    if (titleController.text.isEmpty || codeController.text.isEmpty) {
      _showErrorMessage('Please enter category title and code');
      return false;
    }
    return true;
  }

  void _clearForm() {
    titleController.clear();
    descriptionController.clear();
    codeController.clear();
    organisationId = Get.find<AuthController>().currentOrg.value?.id;
    isGeneral.value = false;
  }

  void _showSuccessMessage(String message) {
    ToastUtils.showSuccessToast('Success', message);
  }

  void _showErrorMessage(String message) {
    ToastUtils.showErrorToast('Error', message);
  }
}
