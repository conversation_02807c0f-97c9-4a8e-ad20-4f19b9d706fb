import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gap/gap.dart';
import 'package:onechurch/features/inventory/controllers/inventory_controller.dart';
import 'package:onechurch/features/inventory/controllers/inventory_item_controller.dart';

class EnhancedStatsWidget extends StatelessWidget {
  final bool isItemsView;

  const EnhancedStatsWidget({super.key, required this.isItemsView});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Check if controllers are available
    if (!Get.isRegistered<InventoryItemController>() ||
        !Get.isRegistered<InventoryController>()) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.all(16.r),
      child: Card(
        elevation: 2,
        shadowColor: colorScheme.shadow.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                colorScheme.primary.withOpacity(0.05),
                colorScheme.primary.withOpacity(0.02),
              ],
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(20.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(context),
                Gap(20.h),
                _buildStatsGrid(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(12.r),
          decoration: BoxDecoration(
            color: colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Icon(
            isItemsView ? IconlyLight.category : IconlyLight.document,
            color: colorScheme.primary,
            size: 24,
          ),
        ),
        Gap(12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isItemsView ? 'Inventory Items' : 'Inventory Records',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: colorScheme.onSurface,
                ),
              ),
              Gap(4.h),
              Text(
                isItemsView
                    ? 'Manage your inventory items and categories'
                    : 'Track inventory movements and transactions',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatsGrid(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final isMobile = mediaQuery.size.width < 600;

    if (isMobile) {
      return _buildMobileGrid(context);
    } else {
      return _buildDesktopGrid(context);
    }
  }

  Widget _buildMobileGrid(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                isItemsView ? 'Total Items' : 'Total Records',
                _getTotalValue(),
                IconlyLight.category,
                const Color(0xFF3B82F6), // Blue
              ),
            ),
            Gap(12.w),
            Expanded(
              child: _buildStatCard(
                context,
                isItemsView ? 'Categories' : 'Types',
                _getCategoriesValue(),
                IconlyLight.folder,
                const Color(0xFF10B981), // Green
              ),
            ),
          ],
        ),
        Gap(12.h),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                'Current Page',
                _getCurrentPageValue(),
                IconlyLight.document,
                const Color(0xFFF59E0B), // Orange
              ),
            ),
            Gap(12.w),
            Expanded(
              child: _buildStatCard(
                context,
                'Total Pages',
                _getTotalPagesValue(),
                IconlyLight.paper,
                const Color(0xFF8B5CF6), // Purple
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDesktopGrid(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            isItemsView ? 'Total Items' : 'Total Records',
            _getTotalValue(),
            IconlyLight.category,
            const Color(0xFF3B82F6), // Blue
          ),
        ),
        Gap(16.w),
        Expanded(
          child: _buildStatCard(
            context,
            isItemsView ? 'Categories' : 'Types',
            _getCategoriesValue(),
            IconlyLight.folder,
            const Color(0xFF10B981), // Green
          ),
        ),
        Gap(16.w),
        Expanded(
          child: _buildStatCard(
            context,
            'Current Page',
            _getCurrentPageValue(),
            IconlyLight.document,
            const Color(0xFFF59E0B), // Orange
          ),
        ),
        Gap(16.w),
        Expanded(
          child: _buildStatCard(
            context,
            'Total Pages',
            _getTotalPagesValue(),
            IconlyLight.paper,
            const Color(0xFF8B5CF6), // Purple
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: color.withOpacity(0.08),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withOpacity(0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const Spacer(),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Text(
                  value,
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.w700,
                    fontSize: 11,
                  ),
                ),
              ),
            ],
          ),
          Gap(12.h),
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          Gap(4.h),
          Text(
            value,
            style: theme.textTheme.headlineSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w700,
              fontSize: 24,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // Helper methods to get values from controllers
  String _getTotalValue() {
    try {
      if (isItemsView) {
        if (Get.isRegistered<InventoryItemController>()) {
          final controller = Get.find<InventoryItemController>();
          return controller.totalItems.value.toString();
        }
      } else {
        if (Get.isRegistered<InventoryController>()) {
          final controller = Get.find<InventoryController>();
          return controller.totalItems.value.toString();
        }
      }
    } catch (e) {
      // Return default value if controller not available
    }
    return '0';
  }

  String _getCategoriesValue() {
    try {
      if (Get.isRegistered<InventoryItemController>()) {
        final controller = Get.find<InventoryItemController>();
        return isItemsView
            ? controller.categoryOptions.length.toString()
            : controller.inventoryTypeOptions.length.toString();
      }
    } catch (e) {
      // Return default value if controller not available
    }
    return '0';
  }

  String _getCurrentPageValue() {
    try {
      if (isItemsView) {
        if (Get.isRegistered<InventoryItemController>()) {
          final controller = Get.find<InventoryItemController>();
          return (controller.currentPage.value + 1).toString();
        }
      } else {
        if (Get.isRegistered<InventoryController>()) {
          final controller = Get.find<InventoryController>();
          return (controller.currentPage.value + 1).toString();
        }
      }
    } catch (e) {
      // Return default value if controller not available
    }
    return '1';
  }

  String _getTotalPagesValue() {
    try {
      if (isItemsView) {
        if (Get.isRegistered<InventoryItemController>()) {
          final controller = Get.find<InventoryItemController>();
          return controller.totalPages.value.toString();
        }
      } else {
        if (Get.isRegistered<InventoryController>()) {
          final controller = Get.find<InventoryController>();
          return controller.totalPages.value.toString();
        }
      }
    } catch (e) {
      // Return default value if controller not available
    }
    return '1';
  }
}
