/// Model class representing SMS statistics and analytics
class SmsStatistics {
  final int totalSmsSent;
  final double walletBalance;
  final int totalSuccessful;
  final int totalBlocked;
  final int totalPending;
  final List<SmsGraphData> graphData;
  final String organisationId;
  final DateTime lastUpdated;

  SmsStatistics({
    required this.totalSmsSent,
    required this.walletBalance,
    required this.totalSuccessful,
    required this.totalBlocked,
    required this.totalPending,
    required this.graphData,
    required this.organisationId,
    required this.lastUpdated,
  });

  /// Create SmsStatistics from JSON
  factory SmsStatistics.fromJson(Map<String, dynamic> json) {
    return SmsStatistics(
      totalSmsSent: int.tryParse(json['total_sms_sent']?.toString() ?? json['totalSmsSent']?.toString() ?? '0') ?? 0,
      walletBalance: double.tryParse(json['wallet_balance']?.toString() ?? json['walletBalance']?.toString() ?? '0.0') ?? 0.0,
      totalSuccessful: int.tryParse(json['total_successful']?.toString() ?? json['totalSuccessful']?.toString() ?? '0') ?? 0,
      totalBlocked: int.tryParse(json['total_blocked']?.toString() ?? json['totalBlocked']?.toString() ?? '0') ?? 0,
      totalPending: int.tryParse(json['total_pending']?.toString() ?? json['totalPending']?.toString() ?? '0') ?? 0,
      graphData: (json['graph_data'] as List<dynamic>?)?.map((e) => SmsGraphData.fromJson(e as Map<String, dynamic>)).toList() ?? [],
      organisationId: json['organisation_id']?.toString() ?? json['organisationId']?.toString() ?? '',
      lastUpdated: DateTime.tryParse(json['last_updated']?.toString() ?? json['lastUpdated']?.toString() ?? '') ?? DateTime.now(),
    );
  }

  /// Convert SmsStatistics to JSON
  Map<String, dynamic> toJson() {
    return {
      'total_sms_sent': totalSmsSent,
      'wallet_balance': walletBalance,
      'total_successful': totalSuccessful,
      'total_blocked': totalBlocked,
      'total_pending': totalPending,
      'graph_data': graphData.map((e) => e.toJson()).toList(),
      'organisation_id': organisationId,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  /// Calculate success rate percentage
  double get successRate {
    if (totalSmsSent == 0) return 0.0;
    return (totalSuccessful / totalSmsSent) * 100;
  }

  /// Calculate blocked rate percentage
  double get blockedRate {
    if (totalSmsSent == 0) return 0.0;
    return (totalBlocked / totalSmsSent) * 100;
  }

  /// Calculate pending rate percentage
  double get pendingRate {
    if (totalSmsSent == 0) return 0.0;
    return (totalPending / totalSmsSent) * 100;
  }
}

/// Model class for graph data points
class SmsGraphData {
  final DateTime date;
  final int totalSent;
  final int totalBlocked;
  final int totalSuccess;

  SmsGraphData({
    required this.date,
    required this.totalSent,
    required this.totalBlocked,
    required this.totalSuccess,
  });

  /// Create SmsGraphData from JSON
  factory SmsGraphData.fromJson(Map<String, dynamic> json) {
    return SmsGraphData(
      date: DateTime.tryParse(json['date']?.toString() ?? '') ?? DateTime.now(),
      totalSent: int.tryParse(json['total_sent']?.toString() ?? json['totalSent']?.toString() ?? '0') ?? 0,
      totalBlocked: int.tryParse(json['total_blocked']?.toString() ?? json['totalBlocked']?.toString() ?? '0') ?? 0,
      totalSuccess: int.tryParse(json['total_success']?.toString() ?? json['totalSuccess']?.toString() ?? '0') ?? 0,
    );
  }

  /// Convert SmsGraphData to JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'total_sent': totalSent,
      'total_blocked': totalBlocked,
      'total_success': totalSuccess,
    };
  }
}
