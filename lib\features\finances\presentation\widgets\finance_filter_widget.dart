import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/features/finances/controllers/finance_controller.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';

class FinanceFilterWidget extends StatelessWidget {
  const FinanceFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<FinanceController>();
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(controller, context),
            const Divider(height: 24),
            _buildFilter<PERSON>ontrols(controller, context),
            const SizedBox(height: 16),
            _buildActionButtons(controller),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(FinanceController controller, BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Row(
      children: [
        Icon(IconlyLight.filter, color: colorScheme.primary),
        const SizedBox(width: 8),
        Text(
          'Filter Finance Accounts',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const Spacer(),
        _buildClearButton(controller, context),
      ],
    );
  }

  Widget _buildClearButton(FinanceController controller, BuildContext context) {
    return TextButton(
      onPressed: controller.clearFilters,
      style: TextButton.styleFrom(
        foregroundColor: Colors.red,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: Row(
        children: [
          const Icon(Icons.clear_all, size: 18),
          const SizedBox(width: 4),
          Text('Clear All', style: Theme.of(context).textTheme.labelMedium),
        ],
      ),
    );
  }

  Widget _buildFilterControls(
    FinanceController controller,
    BuildContext context,
  ) {
    final isMobile = MediaQuery.of(context).size.width < 600;

    return Wrap(
      runSpacing: 8,
      spacing: 8,
      children: [
        // Search field
        if (isMobile) ...[
          SizedBox(width: 180.w, child: buildSearchField(controller, context)),
          const SizedBox(width: 12),
        ],
        // Status filter
        SizedBox(width: 150.w, child: _buildStatusFilter(controller, context)),
        const SizedBox(width: 12),

        // Category filter
        SizedBox(
          width: 150.w,
          child: _buildCategoryFilter(controller, context),
        ),
        const SizedBox(width: 12),

        // Date filters
        SizedBox(
          width: 130.w,
          child: _buildDateField(context, controller, isStartDate: true),
        ),
        const SizedBox(width: 12),
        SizedBox(
          width: 130.w,
          child: _buildDateField(context, controller, isStartDate: false),
        ),
      ],
    );
  }

  Widget _buildStatusFilter(
    FinanceController controller,
    BuildContext context,
  ) {
    final colorScheme = Theme.of(context).colorScheme;

    return Obx(
      () => DropdownButtonFormField<String>(
        decoration: InputDecoration(
          labelText: 'Status',
          prefixIcon: Icon(IconlyLight.tickSquare, color: colorScheme.primary),
          border: _inputBorder(),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 14,
            horizontal: 16,
          ),
        ),
        value:
            controller.selectedStatus.value.isEmpty ||
                    controller.selectedStatus.value == 'All'
                ? 'all'
                : controller.selectedStatus.value.toLowerCase(),
        items: const [
          DropdownMenuItem(value: 'all', child: Text('All Status')),
          DropdownMenuItem(value: 'active', child: Text('Active')),
          DropdownMenuItem(value: 'inactive', child: Text('Inactive')),
          DropdownMenuItem(value: 'pending', child: Text('Pending')),
          DropdownMenuItem(value: 'suspended', child: Text('Suspended')),
        ],
        onChanged: (value) {
          if (value != null) {
            if (value == 'all') {
              controller.setStatusFilter('All');
            } else {
              controller.setStatusFilter(value.toUpperCase());
            }
          }
        },
        icon: Icon(Icons.arrow_drop_down, color: colorScheme.primary),
      ),
    );
  }

  Widget _buildCategoryFilter(
    FinanceController controller,
    BuildContext context,
  ) {
    final colorScheme = Theme.of(context).colorScheme;

    return Obx(
      () => DropdownButtonFormField<String>(
        decoration: InputDecoration(
          labelText: 'Category',
          prefixIcon: Icon(IconlyLight.category, color: colorScheme.primary),
          border: _inputBorder(),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 14,
            horizontal: 16,
          ),
        ),
        value:
            controller.selectedCategory.value.isEmpty ||
                    controller.selectedCategory.value == 'All'
                ? 'all'
                : controller.selectedCategory.value,
        items: [
          const DropdownMenuItem(value: 'all', child: Text('All Categories')),
          ...controller.categories.map(
            (category) => DropdownMenuItem(
              value: category.title ?? 'Untitled',
              child: Text(category.title ?? 'Untitled'),
            ),
          ),
        ],
        onChanged: (value) {
          if (value != null) {
            if (value == 'all') {
              controller.setCategoryFilter('All');
            } else {
              controller.setCategoryFilter(value);
            }
          }
        },
        icon: Icon(Icons.arrow_drop_down, color: colorScheme.primary),
      ),
    );
  }

  Widget _buildDateField(
    BuildContext context,
    FinanceController controller, {
    required bool isStartDate,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Obx(() {
      final date =
          isStartDate ? controller.startDate.value : controller.endDate.value;
      final label = isStartDate ? 'Start Date' : 'End Date';

      return CustomTextFormField(
        readOnly: true,
        labelText: label,
        prefixIcon: Icon(IconlyLight.calendar, color: colorScheme.primary),
        suffixIcon: _buildClearIcon(
          () =>
              isStartDate
                  ? controller.setDateFilters(null, controller.endDate.value)
                  : controller.setDateFilters(controller.startDate.value, null),
          showClear: date != null,
        ),
        hintText: 'Select $label',
        controller: TextEditingController(
          text: date != null ? DateFormat('MMM dd, yyyy').format(date) : null,
        ),
        onTap:
            () =>
                isStartDate
                    ? _selectStartDate(context, controller)
                    : _selectEndDate(context, controller),
      );
    });
  }

  Widget _buildActionButtons(FinanceController controller) {
    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        onPressed: controller.fetchAccounts,
        icon: const Icon(IconlyLight.filter),
        label: const Text('Apply Filters'),
      ),
    );
  }

  // Select start date
  Future<void> _selectStartDate(
    BuildContext context,
    FinanceController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.startDate.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      controller.setDateFilters(picked, controller.endDate.value);
    }
  }

  // Select end date
  Future<void> _selectEndDate(
    BuildContext context,
    FinanceController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.endDate.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      // Set to end of day for inclusive filtering
      final endOfDay = DateTime(
        picked.year,
        picked.month,
        picked.day,
        23,
        59,
        59,
      );
      controller.setDateFilters(controller.startDate.value, endOfDay);
    }
  }
}

Widget buildSearchField(FinanceController controller, BuildContext context) {
  final colorScheme = Theme.of(context).colorScheme;

  return CustomTextFormField(
    controller: controller.searchController,
    labelText: 'Search',
    hintText: 'Title, Description...',
    prefixIcon: Icon(IconlyLight.search, color: colorScheme.primary),
    suffixIcon: _buildClearIcon(() {
      controller.searchController.clear();
      controller.setSearchQuery('');
    }, showClear: controller.searchController.text.isNotEmpty),

    onFieldSubmitted: (value) {
      controller.setSearchQuery(value);
      controller.fetchAccounts();
    },
    onChanged: (value) => controller.setSearchQuery(value),
  );
}

Widget _buildClearIcon(VoidCallback onPressed, {bool showClear = true}) {
  if (!showClear) return const SizedBox.shrink();

  return IconButton(
    icon: const Icon(Icons.clear, size: 18),
    onPressed: onPressed,
    splashRadius: 20,
  );
}

OutlineInputBorder _inputBorder() {
  return OutlineInputBorder(
    borderRadius: BorderRadius.circular(12),
    borderSide: const BorderSide(width: 1),
  );
}
