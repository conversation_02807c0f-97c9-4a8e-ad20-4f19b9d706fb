import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:onechurch/features/members/controllers/member_controller.dart';
import '../../../../../../../core/app/widgets/custom_text_field.dart';

class SearchFieldWidget extends StatelessWidget {
  const SearchFieldWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MemberController>();
    return CustomTextField(
      controller: controller.searchController,
      hintText: 'Search members...',
      prefixIcon: const Icon(IconlyLight.search),
      onSubmitted: (value) {
        controller.setSearchQuery(value);
      },
    );
  }
}
