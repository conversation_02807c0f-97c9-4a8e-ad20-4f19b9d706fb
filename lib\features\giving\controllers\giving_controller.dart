import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../services/giving_service.dart';
import '../../../core/app/utils/show_toast.dart';

class GivingController extends GetxController {
  final GivingService _givingService = GivingService();
  final logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxBool isSubmitting = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<Map<String, dynamic>> givingRecords = <Map<String, dynamic>>[].obs;
  final RxList<String> givingTypes = <String>[].obs;
  
  // Pagination
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalItems = 0.obs;
  final RxInt pageSize = 10.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;

  // Form controllers
  final TextEditingController amountController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final RxString selectedGivingType = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadGivingTypes();
    fetchGivingRecords();
  }

  @override
  void onClose() {
    amountController.dispose();
    notesController.dispose();
    super.onClose();
  }

  // Load giving types
  Future<void> loadGivingTypes() async {
    try {
      givingTypes.value = [
        'Tithe',
        'Offering',
        'Special Offering',
        'Building Fund',
        'Mission Fund',
        'Other',
      ];
    } catch (e) {
      logger.e('Error loading giving types: $e');
    }
  }

  // Fetch giving records
  Future<void> fetchGivingRecords() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final response = await _givingService.fetchGivingRecords(
        page: currentPage.value,
        size: pageSize.value,
      );

      if (response['status'] == true && response['data'] != null) {
        final data = response['data'] as Map<String, dynamic>;
        
        if (data['items'] != null) {
          final items = data['items'] as List<dynamic>;
          givingRecords.value = items.cast<Map<String, dynamic>>();
          
          // Update pagination metadata
          totalPages.value = data['total_pages'] as int? ?? 0;
          totalItems.value = data['total'] as int? ?? 0;
          isLastPage.value = data['last'] as bool? ?? false;
          isFirstPage.value = data['first'] as bool? ?? true;
          
          logger.d('Fetched ${items.length} giving records');
        } else {
          givingRecords.clear();
          logger.w('No giving records found');
        }
      } else {
        givingRecords.clear();
        logger.w('Invalid response format or status false');
      }
    } catch (e) {
      errorMessage.value = 'Failed to fetch giving records: ${e.toString()}';
      logger.e('Error in fetchGivingRecords: $e');
      givingRecords.clear();
    } finally {
      isLoading.value = false;
    }
  }

  // Submit giving record
  Future<bool> submitGiving() async {
    try {
      isSubmitting.value = true;

      if (amountController.text.isEmpty || selectedGivingType.value.isEmpty) {
        ToastUtils.showErrorToast('Error', 'Please fill in all required fields');
        return false;
      }

      final amount = double.tryParse(amountController.text);
      if (amount == null || amount <= 0) {
        ToastUtils.showErrorToast('Error', 'Please enter a valid amount');
        return false;
      }

      final response = await _givingService.submitGiving(
        amount: amount,
        givingType: selectedGivingType.value,
        notes: notesController.text,
      );

      if (response['status'] == true) {
        ToastUtils.showSuccessToast('Success', 'Giving record submitted successfully');
        clearForm();
        await fetchGivingRecords();
        return true;
      } else {
        ToastUtils.showErrorToast('Error', response['message'] ?? 'Failed to submit giving');
        return false;
      }
    } catch (e) {
      ToastUtils.showErrorToast('Error', 'An unexpected error occurred');
      logger.e('Error submitting giving: $e');
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Clear form
  void clearForm() {
    amountController.clear();
    notesController.clear();
    selectedGivingType.value = '';
  }

  // Refresh giving records
  Future<void> refreshGivingRecords() async {
    currentPage.value = 0;
    await fetchGivingRecords();
  }
}
