import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';

import '../models/staff_model.dart';
import '../services/staff_service.dart';

class StaffController extends GetxController {
  final StaffService _staffService = StaffService();
  final logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<StaffModel> staffs = <StaffModel>[].obs;
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalItems = 0.obs;
  final RxInt pageSize = 10.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;

  // Filter variables
  final RxString identifierFilter =
      ''.obs; // Combined filter for phone, email, ID, code
  final RxString phoneNumberFilter = ''.obs; // Kept for backward compatibility
  final RxString emailFilter = ''.obs; // Kept for backward compatibility
  final RxString idNumberFilter = ''.obs; // Kept for backward compatibility
  final RxString codeFilter = ''.obs; // Kept for backward compatibility
  final RxString roleFilter = ''.obs;
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  // Form controllers for create/edit
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController secondNameController = TextEditingController();
  final TextEditingController codeController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController idNumberController = TextEditingController();
  final RxString roleValue = ''.obs;
  final RxBool isMakerValue = true.obs;
  final RxBool isCheckerValue = false.obs;
  final RxBool isSignatoryValue = false.obs;
  final RxList<dynamic> assignedLocations = <dynamic>[].obs;
  final RxBool isSubmitting = false.obs;
  final RxBool isEditing = false.obs;
  final RxString currentStaffId = ''.obs;

  // Phone number validation
  final RxBool isPhoneValid = false.obs;
  final RxBool isPhoneChecking = false.obs;
  final RxBool isMemberFound = false.obs;
  final RxMap<String, dynamic> memberData = <String, dynamic>{}.obs;

  @override
  void onInit() {
    super.onInit();
    fetchStaffs();
  }

  @override
  void onClose() {
    phoneNumberController.dispose();
    firstNameController.dispose();
    secondNameController.dispose();
    codeController.dispose();
    emailController.dispose();
    idNumberController.dispose();
    super.onClose();
  }

  // Set filter values
  void setIdentifierFilter(String value) {
    identifierFilter.value = value;
    // Clear individual filters when using the combined identifier
    phoneNumberFilter.value = '';
    emailFilter.value = '';
    idNumberFilter.value = '';
    codeFilter.value = '';
    currentPage.value = 0; // Reset to first page when filtering
  }

  // Legacy filter methods - now just set the identifier filter
  void setPhoneNumberFilter(String value) {
    identifierFilter.value = value;
    phoneNumberFilter.value = value; // For backward compatibility
    currentPage.value = 0;
  }

  void setEmailFilter(String value) {
    identifierFilter.value = value;
    emailFilter.value = value; // For backward compatibility
    currentPage.value = 0;
  }

  void setIdNumberFilter(String value) {
    identifierFilter.value = value;
    idNumberFilter.value = value; // For backward compatibility
    currentPage.value = 0;
  }

  void setCodeFilter(String value) {
    identifierFilter.value = value;
    codeFilter.value = value; // For backward compatibility
    currentPage.value = 0;
  }

  void setRoleFilter(String value) {
    roleFilter.value = value;
    currentPage.value = 0;
  }

  // Set date filters
  void setDateFilters(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0; // Reset to first page when filtering
  }

  // Clear all filters
  void clearFilters() {
    identifierFilter.value = '';
    phoneNumberFilter.value = '';
    emailFilter.value = '';
    idNumberFilter.value = '';
    codeFilter.value = '';
    roleFilter.value = '';
    startDate.value = null;
    endDate.value = null;
    currentPage.value = 0;
  }

  // Navigate to next page
  void nextPage() {
    if (!isLastPage.value) {
      currentPage.value++;
      fetchStaffs();
    }
  }

  // Navigate to previous page
  void previousPage() {
    if (currentPage.value > 0) {
      currentPage.value--;
      fetchStaffs();
    }
  }

  // Fetch staff members with pagination and filters
  Future<void> fetchStaffs() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _staffService.fetchStaffs(
        page: currentPage.value,
        size: pageSize.value,
        identifier:
            identifierFilter.value.isEmpty ? null : identifierFilter.value,
        role: roleFilter.value.isEmpty ? null : roleFilter.value,
        startDate: startDate.value,
        endDate: endDate.value,
      );

      if (response["status"] ?? false) {
        final data = response["data"];

        if (data != null) {
          // Parse pagination data
          currentPage.value = data["page"] ?? 0;
          pageSize.value = data["size"] ?? 10;
          totalPages.value = data["total_pages"] ?? 0;
          totalItems.value = data["total"] ?? 0;
          isLastPage.value = data["last"] ?? false;
          isFirstPage.value = data["first"] ?? true;

          // Parse staff items
          final List<dynamic> items = data["items"] ?? [];
          logger.d('Staff items found: ${items.length}');
          staffs.value = _staffService.parseStaffs(items);
          staffs.refresh();
        } else {
          logger.e('Data structure is null');
          errorMessage.value = 'Invalid data structure in response';
        }
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to fetch staff members';
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to fetch staff members: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
    update();
  }

  // Refresh staff list
  Future<void> refreshStaffs() async {
    currentPage.value = 0;
    await fetchStaffs();
  }

  // Get staff by ID
  Future<StaffModel?> getStaffById(String id) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _staffService.getStaffById(id);

      if (response["status"] ?? false) {
        final data = response["data"];
        return StaffModel.fromJson(data);
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to fetch staff member';
        return null;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to fetch staff member: ${e.toString()}';
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // Check if phone number belongs to a member
  Future<void> checkMemberByPhone(String phoneNumber) async {
    isPhoneChecking.value = true;
    isMemberFound.value = false;
    memberData.clear();

    try {
      final response = await _staffService.checkMemberByPhone(phoneNumber);

      if (response["status"] ?? false) {
        final data = response["data"];
        if (data != null) {
          isMemberFound.value = true;
          memberData.value = data;

          // Pre-fill form fields with member data
          firstNameController.text = data["first_name"] ?? '';
          secondNameController.text = data["second_name"] ?? '';
          emailController.text = data["email"] ?? '';
          idNumberController.text = data["id_number"] ?? '';
        } else {
          isMemberFound.value = false;
        }
      } else {
        isMemberFound.value = false;
        errorMessage.value = response["message"] ?? 'Member not found';
      }
    } catch (e) {
      logger.e(e);
      isMemberFound.value = false;
      errorMessage.value = 'Failed to check member: ${e.toString()}';
    } finally {
      isPhoneChecking.value = false;
    }
    update();
  }

  // Create a new staff member
  Future<bool> createStaff() async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      // Validate required fields
      if (phoneNumberController.text.isEmpty ||
          firstNameController.text.isEmpty ||
          secondNameController.text.isEmpty ||
          emailController.text.isEmpty) {
        errorMessage.value = 'Please fill all required fields';
        return false;
      }

      final response = await _staffService.createStaff(
        phoneNumber: phoneNumberController.text,
        firstName: firstNameController.text,
        secondName: secondNameController.text,
        code: codeController.text,
        email: emailController.text,
        idNumber: idNumberController.text,
        assignedLocations: assignedLocations,
        role: roleValue.value,
        isMaker: isMakerValue.value,
        isChecker: isCheckerValue.value,
        isSignatory: isSignatoryValue.value,
      );

      if (response["status"] ?? false) {
        clearForm();
        await refreshStaffs();
        return true;
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to create staff member';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to create staff member: ${e.toString()}';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Update an existing staff member
  Future<bool> updateStaff() async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      // Validate required fields
      if (phoneNumberController.text.isEmpty ||
          firstNameController.text.isEmpty ||
          secondNameController.text.isEmpty ||
          emailController.text.isEmpty ||
          currentStaffId.value.isEmpty) {
        errorMessage.value = 'Please fill all required fields';
        return false;
      }

      final response = await _staffService.updateStaff(
        id: currentStaffId.value,
        phoneNumber: phoneNumberController.text,
        firstName: firstNameController.text,
        secondName: secondNameController.text,
        code: codeController.text,
        email: emailController.text,
        idNumber: idNumberController.text,
        assignedLocations: assignedLocations,
        role: roleValue.value,
        isMaker: isMakerValue.value,
        isChecker: isCheckerValue.value,
        isSignatory: isSignatoryValue.value,
      );

      if (response["status"] ?? false) {
        clearForm();
        await refreshStaffs();
        return true;
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to update staff member';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to update staff member: ${e.toString()}';
      return false;
    } finally {
      isSubmitting.value = false;
      isEditing.value = false;
    }
  }

  // Delete a staff member
  Future<bool> deleteStaff(String id) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final response = await _staffService.deleteStaff(id);

      if (response["status"] ?? false) {
        await refreshStaffs();
        return true;
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to delete staff member';
        return false;
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to delete staff member: ${e.toString()}';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Load staff data for editing
  void loadStaffForEdit(StaffModel staff) {
    isEditing.value = true;
    currentStaffId.value = staff.id ?? '';
    phoneNumberController.text = staff.phoneNumber ?? '';
    firstNameController.text = staff.firstName ?? '';
    secondNameController.text = staff.secondName ?? '';
    codeController.text = staff.code ?? '';
    emailController.text = staff.email ?? '';
    idNumberController.text = staff.idNumber ?? '';
    assignedLocations.value = staff.assignedLocations ?? [];
    roleValue.value =
        staff.roleAssignments?.isNotEmpty ?? false
            ? staff.roleAssignments![0].toString()
            : '';
    isMakerValue.value = staff.isMaker ?? false;
    isCheckerValue.value = staff.isChecker ?? false;
    isSignatoryValue.value = staff.isSignatory ?? false;
  }

  // Load staff data for editing by ID
  Future<void> loadStaffForEditById(String id) async {
    isLoading.value = true;
    try {
      final staff = await getStaffById(id);
      if (staff != null) {
        loadStaffForEdit(staff);
      } else {
        errorMessage.value = 'Failed to load staff member';
        ToastUtils.showErrorToast('Error', 'Failed to load staff member');
      }
    } catch (e) {
      logger.e('Error loading staff for edit: $e');
      errorMessage.value = 'Failed to load staff member: ${e.toString()}';
      ToastUtils.showErrorToast('Error', 'Failed to load staff member');
    } finally {
      isLoading.value = false;
    }
  }

  // Clear form fields
  void clearForm() {
    isEditing.value = false;
    currentStaffId.value = '';
    phoneNumberController.clear();
    firstNameController.clear();
    secondNameController.clear();
    codeController.clear();
    emailController.clear();
    idNumberController.clear();
    assignedLocations.clear();
    roleValue.value = '';
    isMakerValue.value = true;
    isCheckerValue.value = false;
    isSignatoryValue.value = false;
    isMemberFound.value = false;
    memberData.clear();
  }

  // Handle phone number validation
  void onPhoneNumberChanged(PhoneNumber number) {
    phoneNumberController.text = number.phoneNumber ?? '';
    isPhoneValid.value =
        number.phoneNumber != null &&
        number.phoneNumber!.isNotEmpty &&
        number.phoneNumber!.length > 8;
  }

  // Check member when phone number is entered
  void onPhoneNumberSubmitted() {
    if (isPhoneValid.value) {
      checkMemberByPhone(phoneNumberController.text);
    }
  }
}
