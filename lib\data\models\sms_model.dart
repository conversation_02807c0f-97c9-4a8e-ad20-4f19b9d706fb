// To parse this JSON data, do
//
//     final smsModel = smsModelFromJson(jsonString);

import 'dart:convert';

List<SmsModel> smsModelFromJson(String str) =>
    List<SmsModel>.from(json.decode(str).map((x) => SmsModel.fromJson(x)));

String smsModelToJson(List<SmsModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class SmsModel {
  final DateTime? createdAt;
  final dynamic deletedAt;
  final int? id;
  final DateTime? updatedAt;
  final int? characters;
  final double? costOrg;
  final String? createdById;
  final int? externalUserId;
  final String? message;
  final String? messageHash;
  final String? messageId;
  final int? numSms;
  final String? organisation;
  final String? organisationName;
  final double? pricePerUnit;
  final String? senderEmail;
  final String? senderId;
  final String? senderPhone;
  final String? status;
  final double? thirdPartyCharge;
  final double? totalCharges;
  final int? totalRecipients;
  final int? transactionId;

  SmsModel({
    this.createdAt,
    this.deletedAt,
    this.id,
    this.updatedAt,
    this.characters,
    this.costOrg,
    this.createdById,
    this.externalUserId,
    this.message,
    this.messageHash,
    this.messageId,
    this.numSms,
    this.organisation,
    this.organisationName,
    this.pricePerUnit,
    this.senderEmail,
    this.senderId,
    this.senderPhone,
    this.status,
    this.thirdPartyCharge,
    this.totalCharges,
    this.totalRecipients,
    this.transactionId,
  });

  SmsModel copyWith({
    DateTime? createdAt,
    dynamic deletedAt,
    int? id,
    DateTime? updatedAt,
    int? characters,
    double? costOrg,
    String? createdById,
    int? externalUserId,
    String? message,
    String? messageHash,
    String? messageId,
    int? numSms,
    String? organisation,
    String? organisationName,
    double? pricePerUnit,
    String? senderEmail,
    String? senderId,
    String? senderPhone,
    String? status,
    double? thirdPartyCharge,
    double? totalCharges,
    int? totalRecipients,
    int? transactionId,
  }) => SmsModel(
    createdAt: createdAt ?? this.createdAt,
    deletedAt: deletedAt ?? this.deletedAt,
    id: id ?? this.id,
    updatedAt: updatedAt ?? this.updatedAt,
    characters: characters ?? this.characters,
    costOrg: costOrg ?? this.costOrg,
    createdById: createdById ?? this.createdById,
    externalUserId: externalUserId ?? this.externalUserId,
    message: message ?? this.message,
    messageHash: messageHash ?? this.messageHash,
    messageId: messageId ?? this.messageId,
    numSms: numSms ?? this.numSms,
    organisation: organisation ?? this.organisation,
    organisationName: organisationName ?? this.organisationName,
    pricePerUnit: pricePerUnit ?? this.pricePerUnit,
    senderEmail: senderEmail ?? this.senderEmail,
    senderId: senderId ?? this.senderId,
    senderPhone: senderPhone ?? this.senderPhone,
    status: status ?? this.status,
    thirdPartyCharge: thirdPartyCharge ?? this.thirdPartyCharge,
    totalCharges: totalCharges ?? this.totalCharges,
    totalRecipients: totalRecipients ?? this.totalRecipients,
    transactionId: transactionId ?? this.transactionId,
  );

  factory SmsModel.fromJson(Map<String, dynamic> json) => SmsModel(
    createdAt:
        json["CreatedAt"] == null ? null : DateTime.parse(json["CreatedAt"]),
    deletedAt: json["DeletedAt"],
    id: json["ID"],
    updatedAt:
        json["UpdatedAt"] == null ? null : DateTime.parse(json["UpdatedAt"]),
    characters: json["characters"],
    costOrg: json["cost_org"]?.toDouble(),
    createdById: json["created_by_id"],
    externalUserId: json["external_user_id"],
    message: json["message"],
    messageHash: json["message_hash"],
    messageId: json["message_id"],
    numSms: json["num_sms"],
    organisation: json["organisation"],
    organisationName: json["organisation_name"],
    pricePerUnit: json["price_per_unit"]?.toDouble(),
    senderEmail: json["sender_email"],
    senderId: json["sender_id"],
    senderPhone: json["sender_phone"],
    status: json["status"],
    thirdPartyCharge: json["third_party_charge"]?.toDouble(),
    totalCharges: json["total_charges"]?.toDouble(),
    totalRecipients: json["total_recipients"],
    transactionId: json["transaction_id"],
  );

  Map<String, dynamic> toJson() => {
    "CreatedAt": createdAt?.toIso8601String(),
    "DeletedAt": deletedAt,
    "ID": id,
    "UpdatedAt": updatedAt?.toIso8601String(),
    "characters": characters,
    "cost_org": costOrg,
    "created_by_id": createdById,
    "external_user_id": externalUserId,
    "message": message,
    "message_hash": messageHash,
    "message_id": messageId,
    "num_sms": numSms,
    "organisation": organisation,
    "organisation_name": organisationName,
    "price_per_unit": pricePerUnit,
    "sender_email": senderEmail,
    "sender_id": senderId,
    "sender_phone": senderPhone,
    "status": status,
    "third_party_charge": thirdPartyCharge,
    "total_charges": totalCharges,
    "total_recipients": totalRecipients,
    "transaction_id": transactionId,
  };
}
