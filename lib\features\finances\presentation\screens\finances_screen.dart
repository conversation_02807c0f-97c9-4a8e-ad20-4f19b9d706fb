import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:onechurch/core/app/utils/currency_format.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
class FinancesScreen extends StatelessWidget {
  const FinancesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Finances',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(icon: const Icon(IconlyLight.document), onPressed: () {}),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFinancialSummary(context),
            const SizedBox(height: 24),
            _buildSectionTitle('Recent Transactions'),
            const SizedBox(height: 12),
            ...List.generate(
              5,
              (index) => _buildTransactionItem(
                title: 'Transaction ${index + 1}',
                date:
                    '${DateTime.now().day - index}/${DateTime.now().month}/${DateTime.now().year}',
                amount: FormattedCurrency.getFormattedCurrency(
                  (index + 1) * 10,
                ),

                isIncoming: index % 2 == 0,
                context: context,
              ),
            ),
            const SizedBox(height: 24),
            _buildSectionTitle('Budget Overview'),
            const SizedBox(height: 12),
            _buildBudgetList(context),
            const SizedBox(height: 24),
            _buildDonateButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummary(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.primary,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          const Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Balance',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              Icon(IconlyLight.chart, color: Colors.white),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Text(
                FormattedCurrency.getFormattedCurrency('2,458.00'),
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildFinancialInfoItem(
                label: 'Income',
                value: 'Ksh. 3,245.00',
                icon: IconlyLight.arrowUp,
                color: Colors.green,
              ),
              _buildFinancialInfoItem(
                label: 'Expenses',
                value: 'Ksh. 787.00',
                icon: IconlyLight.arrowDown,
                color: Colors.red.shade300,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialInfoItem({
    required String label,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
    );
  }

  Widget _buildTransactionItem({
    required String title,
    required String date,
    required String amount,
    required bool isIncoming,
    required BuildContext context,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      margin: const EdgeInsets.only(bottom: 10),
      elevation: 0,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color:
                isIncoming
                    ? Colors.green.withValues(alpha: 0.1)
                    : Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            isIncoming ? IconlyLight.arrowDown : IconlyLight.arrowUp,
            color: isIncoming ? Colors.green : Colors.red,
          ),
        ),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
        subtitle: Text(date),
        trailing: Text(
          amount,
          style: TextStyle(
            color: isIncoming ? Colors.green : Colors.red,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        onTap: () {},
      ),
    );
  }

  Widget _buildBudgetList(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 0,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildBudgetItem(
              context: context,
              category: 'Ministry',
              spent: 1200,
              total: 1500,
              color: Colors.blue,
            ),
            const SizedBox(height: 16),
            _buildBudgetItem(
              context: context,
              category: 'Outreach',
              spent: 800,
              total: 1000,
              color: Colors.green,
            ),
            const SizedBox(height: 16),
            _buildBudgetItem(
              context: context,
              category: 'Operations',
              spent: 600,
              total: 1200,
              color: Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetItem({
    required BuildContext context,
    required String category,
    required double spent,
    required double total,
    required Color color,
  }) {
    final percentage = (spent / total * 100).clamp(0, 100);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(category, style: const TextStyle(fontWeight: FontWeight.bold)),
            Text(
              '\$${spent.toInt()} / \$${total.toInt()}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Stack(
          children: [
            Container(
              height: 8,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            Container(
              height: 8,
              width:
                  (MediaQuery.of(context).size.width - 64) * (percentage / 100),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDonateButton(BuildContext context) {
    return CustomButton(
      onPressed: () {},
      icon: const Icon(IconlyLight.heart, color: Colors.white),
      label: const Text(
        'Make a Donation',
        style: TextStyle(color: Colors.white),
      )
    );
  }
}
