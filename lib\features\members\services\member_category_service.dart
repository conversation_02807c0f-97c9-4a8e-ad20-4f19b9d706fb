import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../data/models/member_category_model.dart';
import '../../../features/auth/controllers/auth_controller.dart';

class MemberCategoryService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();
  final authController = Get.find<AuthController>();

  // Initialize the service
  MemberCategoryService() {
    _httpService.initializeDio();
  }

  // Fetch member categories with pagination and search
  Future<Map<String, dynamic>> fetchMemberCategories({
    int page = 0,
    int size = 10,
    String? search,
  }) async {
    try {
      // Build URL with query parameters
      String url = "${ApiUrls.memberCategories}/";
      url += "?page=$page";
      url += "&size=$size";
      // Add search parameter if provided
      if (search != null && search.isNotEmpty) {
        url += "&search=$search";
      }

      logger.i('Fetching member categories with URL: $url');
      final response = await _httpService.request(url: url, method: Method.GET);
      return response.data;
    } catch (e) {
      logger.e('Error fetching member categories: $e');
      return {
        'status': false,
        'message': 'Failed to load categories: $e',
        'data': null,
      };
    }
  }

  // Create a new member category
  Future<Map<String, dynamic>> createMemberCategory({
    required String title,
    required String description,
    required String code,
    required bool isGeneral,
  }) async {
    try {
      // Get organisation ID from auth controller
      final organisationId = authController.currentOrg.value?.id.toString();

      final Map<String, dynamic> data = {
        'title': title,
        'description': description,
        'code': code,
        'organisation_id': organisationId,
        'is_general': isGeneral,
      };

      final response = await _httpService.request(
        url: ApiUrls.createCustomerCategory,
        method: Method.POST,
        params: data,
      );

      return response.data;
    } catch (e) {
      logger.e('Error creating member category: $e');
      return {
        'status': false,
        'message': 'Failed to create category: $e',
        'data': null,
      };
    }
  }

  // Update an existing member category
  Future<Map<String, dynamic>> updateMemberCategory({
    required String id,
    required String title,
    required String description,
    required String code,
  }) async {
    try {
      // Get organisation ID from auth controller
      final organisationId = authController.currentOrg.value?.id.toString();

      final Map<String, dynamic> data = {
        'title': title,
        'description': description,
        'code': code,
        'organisation_id': organisationId,
      };

      final response = await _httpService.request(
        url: ApiUrls.updateCustomerCategory,
        method: Method.PUT,
        params: data,
      );

      return response.data;
    } catch (e) {
      logger.e('Error updating member category: $e');
      return {
        'status': false,
        'message': 'Failed to update category: $e',
        'data': null,
      };
    }
  }

  // Delete a member category
  Future<Map<String, dynamic>> deleteMemberCategory(String id) async {
    try {
      final response = await _httpService.request(
        url: ApiUrls.deleteCustomerCategory,
        method: Method.DELETE,
      );

      return response.data;
    } catch (e) {
      logger.e('Error deleting member category: $e');
      return {
        'status': false,
        'message': 'Failed to delete category: $e',
        'data': null,
      };
    }
  }

  // Parse category items from API response
  List<MemberCategory> parseCategories(List<dynamic> items) {
    return items.map((item) => MemberCategory.fromJson(item)).toList();
  }
}
