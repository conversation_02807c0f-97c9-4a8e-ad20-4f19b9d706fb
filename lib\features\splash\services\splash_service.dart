import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../core/app/services/storage_service.dart';

class SplashService {
  final HttpService _httpService = Get.find();
  final StorageService _storageService = Get.find();
  final logger = Get.find<Logger>();

  // Initialize the service
  SplashService() {
    _httpService.initializeDio();
  }

  // Check app version and updates
  Future<Map<String, dynamic>> checkAppVersion() async {
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/app/version',
        method: Method.GET,
      );

      return response.data;
    } catch (e) {
      logger.e('Error checking app version: $e');
      return {
        'status': false,
        'message': 'Failed to check app version: $e',
        'data': null,
      };
    }
  }

  // Fetch app configuration
  Future<Map<String, dynamic>> fetchAppConfig() async {
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/app/config',
        method: Method.GET,
      );

      // Save config locally if successful
      if (response.data['status'] == true && response.data['data'] != null) {
        await _saveConfigLocally(response.data['data']);
      }

      return response.data;
    } catch (e) {
      logger.e('Error fetching app config: $e');
      return {
        'status': false,
        'message': 'Failed to fetch app config: $e',
        'data': null,
      };
    }
  }

  // Save configuration locally
  Future<void> _saveConfigLocally(Map<String, dynamic> config) async {
    try {
      await _storageService.write('app_config', config);
      logger.d('App config saved locally');
    } catch (e) {
      logger.e('Error saving app config locally: $e');
    }
  }

  // Get local configuration
  Map<String, dynamic>? getLocalConfig() {
    try {
      return _storageService.read<Map<String, dynamic>>('app_config');
    } catch (e) {
      logger.e('Error reading local config: $e');
      return null;
    }
  }

  // Check server connectivity
  Future<bool> checkServerConnectivity() async {
    try {
      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/health',
        method: Method.GET,
      );

      return response.data['status'] == true;
    } catch (e) {
      logger.e('Error checking server connectivity: $e');
      return false;
    }
  }

  // Initialize app data
  Future<Map<String, dynamic>> initializeAppData() async {
    try {
      // Check server connectivity
      final isConnected = await checkServerConnectivity();
      if (!isConnected) {
        return {
          'status': false,
          'message': 'Server not reachable',
          'data': null,
        };
      }

      // Fetch app configuration
      final configResult = await fetchAppConfig();
      if (configResult['status'] != true) {
        return configResult;
      }

      // Check app version
      final versionResult = await checkAppVersion();
      if (versionResult['status'] != true) {
        logger.w('Could not check app version: ${versionResult['message']}');
      }

      return {
        'status': true,
        'message': 'App initialized successfully',
        'data': {
          'config': configResult['data'],
          'version': versionResult['data'],
        },
      };
    } catch (e) {
      logger.e('Error initializing app data: $e');
      return {
        'status': false,
        'message': 'Failed to initialize app: $e',
        'data': null,
      };
    }
  }
}
