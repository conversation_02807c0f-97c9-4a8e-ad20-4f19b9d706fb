import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import '../../../auth/controllers/auth_controller.dart';
import '../../../../core/app/constants/routes.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final AuthController authController = Get.find<AuthController>();
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isTablet =
        MediaQuery.of(context).size.width >= 768 &&
        MediaQuery.of(context).size.width < 1024;
    final isDesktop = MediaQuery.of(context).size.width >= 1024;

    return Scaffold(
      appBar: AppBar(
        title: Text('Profile', style: theme.textTheme.titleLarge),
        actions: [
          IconButton(
            icon: Icon(
              IconlyLight.logout,
              color: colorScheme.onSurface,
              size: isDesktop ? 22 : 20,
            ),
            onPressed: () => authController.logout(context),
          ),
        ],
      ),
      body:
          isDesktop
              ? _buildDesktopLayout(context, authController, theme, colorScheme)
              : _buildMobileTabletLayout(
                context,
                authController,
                theme,
                colorScheme,
                isTablet,
              ),
    );
  }

  Widget _buildDesktopLayout(
    BuildContext context,
    AuthController authController,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: 1200.w),
        padding: EdgeInsets.all(24.r),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left panel with user info
            Expanded(
              flex: 1,
              child: Card(
                elevation: 2,
                color: colorScheme.surface,
                child: Padding(
                  padding: EdgeInsets.all(24.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      CircleAvatar(
                        radius: 70.r,
                        backgroundColor: colorScheme.primary.withValues(
                          alpha: 0.7,
                        ),
                        child: Icon(
                          IconlyBold.profile,
                          size: 60,
                          color: colorScheme.onPrimary,
                        ),
                      ),
                      SizedBox(height: 24.h),
                      Obx(
                        () => Text(
                          authController.userData['first_name'] +
                                  authController.userData['last_name'] ??
                              '',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Obx(
                        () => Text(
                          authController.userData['email'] ??
                              '<EMAIL>',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                      ),
                      SizedBox(height: 36.h),
                      SizedBox(
                        width: double.infinity,
                        child: CustomButton(
                          text: 'Logout',
                          onPressed: () => authController.logout(context),
                          isLoading: false,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(width: 24.w),
            // Right panel with settings
            Expanded(
              flex: 2,
              child: Card(
                elevation: 2,
                color: colorScheme.surface,
                child: Padding(
                  padding: EdgeInsets.all(24.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Settings',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 24.h),
                      _buildSettingItem(
                        context,
                        icon: IconlyLight.setting,
                        title: 'Account Settings',
                        onTap: () {},
                        theme: theme,
                        colorScheme: colorScheme,
                      ),
                      Divider(
                        color: colorScheme.onSurface.withValues(alpha: 0.1),
                      ),
                      _buildSettingItem(
                        context,
                        icon: IconlyLight.notification,
                        title: 'Notifications',
                        onTap: () {},
                        theme: theme,
                        colorScheme: colorScheme,
                      ),
                      Divider(
                        color: colorScheme.onSurface.withValues(alpha: 0.1),
                      ),
                      _buildSettingItem(
                        context,
                        icon: IconlyLight.shieldDone,
                        title: 'Privacy and Security',
                        onTap: () {},
                        theme: theme,
                        colorScheme: colorScheme,
                      ),
                      Divider(
                        color: colorScheme.onSurface.withValues(alpha: 0.1),
                      ),
                      _buildSettingItem(
                        context,
                        icon: IconlyLight.infoSquare,
                        title: 'About',
                        onTap: () {},
                        theme: theme,
                        colorScheme: colorScheme,
                      ),
                      Divider(
                        color: colorScheme.onSurface.withValues(alpha: 0.1),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileTabletLayout(
    BuildContext context,
    AuthController authController,
    ThemeData theme,
    ColorScheme colorScheme,
    bool isTablet,
  ) {
    return Padding(
      padding: EdgeInsets.all(16.0.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User information section
          isTablet
              ? Card(
                elevation: 2,
                color: colorScheme.surface,
                child: Padding(
                  padding: EdgeInsets.all(16.0.r),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 50.r,
                        backgroundColor: colorScheme.primary.withValues(
                          alpha: 0.7,
                        ),
                        child: Icon(
                          IconlyBold.profile,
                          size: 45,
                          color: colorScheme.onPrimary,
                        ),
                      ),
                      SizedBox(width: 24.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Obx(
                              () => Text(
                                authController.userData['name'] ?? 'User',
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            SizedBox(height: 8.h),
                            Obx(
                              () => Text(
                                authController.userData['email'] ??
                                    '<EMAIL>',
                                style: theme.textTheme.bodyLarge?.copyWith(
                                  color: colorScheme.onSurface.withValues(
                                    alpha: 0.6,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              )
              : Card(
                elevation: 2,
                color: colorScheme.surface,
                child: Padding(
                  padding: EdgeInsets.all(16.0.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CircleAvatar(
                        radius: 40.r,
                        backgroundColor: colorScheme.primary.withValues(
                          alpha: 0.7,
                        ),
                        child: Icon(
                          IconlyBold.profile,
                          size: 35,
                          color: colorScheme.onPrimary,
                        ),
                      ),
                      SizedBox(height: 16.h),
                      Obx(
                        () => Text(
                          authController.userData['name'] ?? 'User',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Obx(
                        () => Text(
                          authController.userData['email'] ??
                              '<EMAIL>',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          SizedBox(height: 24.h),

          // Profile actions list
          ListTile(
            leading: Icon(
              IconlyLight.setting,
              color: colorScheme.primary,
              size: 20,
            ),
            title: Text('Settings', style: theme.textTheme.bodyLarge),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 14,
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            onTap: () {
              Navigator.of(context).pushNamed(Routes.SETTINGS);
            },
          ),
          Divider(color: colorScheme.onSurface.withValues(alpha: 0.1)),
          ListTile(
            leading: Icon(
              IconlyLight.notification,
              color: colorScheme.primary,
              size: 20,
            ),
            title: Text('Notifications', style: theme.textTheme.bodyLarge),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 14,
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            onTap: () {},
          ),
          Divider(color: colorScheme.onSurface.withValues(alpha: 0.1)),
          ListTile(
            leading: Icon(
              IconlyLight.shieldDone,
              color: colorScheme.primary,
              size: 20,
            ),
            title: Text('Privacy', style: theme.textTheme.bodyLarge),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 14,
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            onTap: () {},
          ),
          Divider(color: colorScheme.onSurface.withValues(alpha: 0.1)),

          const Spacer(),

          // Logout button
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0.r, vertical: 8.0.r),
            child: CustomButton(
              text: 'Logout',
              onPressed: () => authController.logout(context),
              isLoading: false,
            ),
          ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  Widget _buildSettingItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required Function() onTap,
    required ThemeData theme,
    required ColorScheme colorScheme,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 8.w),
        child: Row(
          children: [
            Icon(icon, color: colorScheme.primary, size: 22),
            SizedBox(width: 16.w),
            Text(title, style: theme.textTheme.bodyLarge),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              size: 14,
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ],
        ),
      ),
    );
  }
}
