import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import '../../../../../core/app/constants/routes.dart';
import '../../../controllers/inventory_category_controller.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
class AddInventoryCategoryScreen extends StatefulWidget {
  const AddInventoryCategoryScreen({super.key});

  @override
  State<AddInventoryCategoryScreen> createState() =>
      _AddInventoryCategoryScreenState();
}

class _AddInventoryCategoryScreenState
    extends State<AddInventoryCategoryScreen> {
  final controller = Get.find<InventoryCategoryController>();
  final _formKey = GlobalKey<FormState>();

  final titleController = TextEditingController();
  final descriptionController = TextEditingController();
  final codeController = TextEditingController();

  bool isGeneral = false;

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    codeController.dispose();
    super.dispose();
  }

  // Submit form
  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      final result = await controller.createCategory(
        title: titleController.text.trim(),
        description: descriptionController.text.trim(),
        code: codeController.text.trim().toUpperCase(),
        isGeneral: isGeneral,
      );

      if (result) {
        context.go(Routes.INVENTORY_CATEGORIES);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Inventory Category'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(24.r),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header info
              Container(
                padding: EdgeInsets.all(16.r),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(
                    color: theme.colorScheme.primary.withOpacity(0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      IconlyLight.infoSquare,
                      color: theme.colorScheme.primary,
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Create Inventory Category',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            'Add a new category for organizing inventory items.',
                            style: theme.textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Gap(24.h),

              // Title
              CustomTextFormField(
                controller: titleController,
                labelText: 'Title',
                hintText: 'e.g., Electronics, Furniture, Office Supplies',
                prefixIcon: const Icon(IconlyLight.category),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              Gap(16.h),

              // Code
              CustomTextFormField(
                controller: codeController,
                labelText: 'Code',
                hintText: 'e.g., ELEC, FURN, OFFICE',
                prefixIcon: const Icon(IconlyLight.ticket),
                textCapitalization: TextCapitalization.characters,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a code';
                  }
                  if (value.length < 2) {
                    return 'Code must be at least 2 characters';
                  }
                  return null;
                },
              ),
              Gap(16.h),

              // Description
              CustomTextFormField(
                controller: descriptionController,
                labelText: 'Description',
                hintText: 'Describe this category...',
                prefixIcon: const Icon(IconlyLight.paper),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              Gap(16.h),
              // Submit button
              SizedBox(
                width: double.infinity,
                child: Obx(
                  () => CustomButton(
                    onPressed:
                        controller.isSubmitting.value ? null : _submitForm,
                    label:
                        controller.isSubmitting.value
                            ? const CircleLoadingAnimation()
                            : const Text('Create Category'),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
