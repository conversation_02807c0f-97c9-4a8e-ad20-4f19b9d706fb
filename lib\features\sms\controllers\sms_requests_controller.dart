import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import '../../../data/models/sms_model.dart';
import '../services/sms_requests_service.dart';

class SmsRequestsController extends GetxController {
  final SmsRequestsService _smsService = SmsRequestsService();
  final logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<SmsModel> messages = <SmsModel>[].obs;
  final RxInt currentPage = 0.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalItems = 0.obs;
  final RxInt pageSize = 20.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;
  final RxString searchQuery = ''.obs;
  final TextEditingController searchController = TextEditingController();

  // Filter variables
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);
  final RxString phoneNumberFilter = ''.obs;
  final RxString messageFilter = ''.obs;
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController messageFilterController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    fetchMessages();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    currentPage.value = 0; // Reset to first page when searching
    fetchMessages();
  }

  // Set date filters
  void setDateFilters(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0; // Reset to first page when filtering
    fetchMessages();
  }

  // Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    searchController.clear();
    startDate.value = null;
    endDate.value = null;
    phoneNumberFilter.value = '';
    phoneNumberController.clear();
    messageFilter.value = '';
    messageFilterController.clear();
    currentPage.value = 0;
    fetchMessages();
  }

  // Navigate to next page
  void nextPage() {
    if (!isLastPage.value) {
      currentPage.value++;
      fetchMessages();
    }
  }

  // Navigate to previous page
  void previousPage() {
    if (currentPage.value > 0) {
      currentPage.value--;
      fetchMessages();
    }
  }

  // Fetch SMS requests with pagination and filters
  Future<void> fetchMessages() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final response = await _smsService.fetchSmsRequests(
        page: currentPage.value,
        size: pageSize.value,
        searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
        startDate: startDate.value,
        endDate: endDate.value,
        phoneNumber:
            phoneNumberFilter.value.isEmpty ? null : phoneNumberFilter.value,
        message: messageFilter.value.isEmpty ? null : messageFilter.value,
        organisationId: Get.find<AuthController>().currentOrg.value?.id,
      );

      if (response["status"] ?? false) {
        final data = response["data"];

        // Parse pagination data
        currentPage.value = data["page"] ?? 0;
        pageSize.value = data["size"] ?? 10;
        totalPages.value = data["total_pages"] ?? 0;
        totalItems.value = data["total"] ?? 0;
        isLastPage.value = data["last"] ?? false;
        isFirstPage.value = data["first"] ?? true;

        // Parse SMS items
        final List<dynamic> items = data["items"] ?? [];
        messages.value = _smsService.parseMessages(items);
        messages.refresh();
      } else {
        errorMessage.value =
            response["message"] ?? 'Failed to fetch SMS requests';
      }
    } catch (e) {
      logger.e(e);
      errorMessage.value = 'Failed to fetch SMS requests: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
    update();
  }

  // Refresh messages
  Future<void> refreshMessages() async {
    currentPage.value = 0;
    await fetchMessages();
  }
}
