import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/app/constants/routes.dart';
import '../../controllers/inventory_item_controller.dart';
import '../widgets/inventory_filter_widget.dart';
import '../widgets/inventory_items_table_widget.dart';

class InventoryItemsScreen extends StatelessWidget {
  const InventoryItemsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize the inventory item controller
    final controller = Get.put(InventoryItemController());
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.background,
      appBar: AppBar(
        title: const Text('Inventory Items'),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () => controller.fetchInventoryItems(),
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // Filter Section
          Container(
            padding: EdgeInsets.all(16.w),
            child: InventoryFilterWidget(
              isItemsView: true,
            ),
          ),

          // Content Section
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              child: const InventoryItemsTableWidget(),
            ),
          ),

          // Bottom spacing
          SizedBox(height: 16.h),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.go(Routes.CREATE_INVENTORY_ITEM),
        icon: const Icon(
          IconlyLight.plus,
          size: 20,
        ),
        label: const Text(
          'Add Item',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        backgroundColor: colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 4,
        extendedPadding: EdgeInsets.symmetric(
          horizontal: 20.w,
          vertical: 12.h,
        ),
        tooltip: 'Add New Item',
      ),
    );
  }
}
