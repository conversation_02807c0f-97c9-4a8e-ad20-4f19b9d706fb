import 'dart:convert';

/// Result class for organization fetch operation
class OrganizationResult {
  final List<NewOrganizations> organizations;
  final int totalPages;

  OrganizationResult({required this.organizations, required this.totalPages});
}

class NewOrganizations {
  final String? organisationId;
  final String? slogan;
  final String? organisationName;
  final List<dynamic>? memberCategories;
  final String? logo;
  final dynamic organisationLocations;

  NewOrganizations({
    this.organisationId,
    this.slogan,
    this.organisationName,
    this.memberCategories,
    this.logo,
    this.organisationLocations,
  });

  NewOrganizations copyWith({
    String? organisationId,
    String? slogan,
    String? organisationName,
    List<dynamic>? memberCategories,
    String? logo,
    dynamic organisationLocations,
  }) => NewOrganizations(
    organisationId: organisationId ?? this.organisationId,
    slogan: slogan ?? this.slogan,
    organisationName: organisationName ?? this.organisationName,
    memberCategories: memberCategories ?? this.memberCategories,
    logo: logo ?? this.logo,
    organisationLocations: organisationLocations ?? this.organisationLocations,
  );

  factory NewOrganizations.fromRawJson(String str) =>
      NewOrganizations.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory NewOrganizations.fromJson(Map<String, dynamic> json) =>
      NewOrganizations(
        organisationId: json["organisation_id"],
        slogan: json["slogan"],
        organisationName: json["organisation_name"],
        memberCategories:
            json["member_categories"] == null
                ? []
                : List<dynamic>.from(json["member_categories"]!.map((x) => x)),
        logo: json["logo"],
        organisationLocations: json["organisation_locations"],
      );

  Map<String, dynamic> toJson() => {
    "organisation_id": organisationId,
    "slogan": slogan,
    "organisation_name": organisationName,
    "member_categories":
        memberCategories == null
            ? []
            : List<dynamic>.from(memberCategories!.map((x) => x)),
    "logo": logo,
    "organisation_locations": organisationLocations,
  };
}
