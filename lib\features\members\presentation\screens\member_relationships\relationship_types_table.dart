import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../../controllers/relationship_controller.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
// Import the split widget files
import 'add_relationship_type_dialog.dart';
import 'confirm_delete_dialogs.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
/// Build relationship types table
Widget buildRelationshipTypesTable(
  BuildContext context,
  RelationshipController relationshipController,
) {
  return Obx(() {
    if (relationshipController.isLoadingTypes.value) {
      return const Center(child: CircleLoadingAnimation());
    }

    if (relationshipController.relationshipTypes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              IconlyBold.document,
              size: 48,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            ),
            Gap(16.h),
            Text(
              'No relationship types found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            Gap(8.h),
            CustomButton(
              onPressed:
                  () => showAddRelationshipTypeDialog(
                    context,
                    relationshipController,
                  ),
              icon: const Icon(IconlyLight.plus),
              label: const Text('Add Relationship Type'),
             
            ),
          ],
        ),
      );
    }

    final columns = <PlutoColumn>[
      PlutoColumn(
        title: 'Title',
        field: 'title',
        type: PlutoColumnType.text(),
        width: 150,
        titleTextAlign: PlutoColumnTextAlign.start,
        textAlign: PlutoColumnTextAlign.start,
        renderer: (rendererContext) {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              rendererContext.cell.value.toString(),
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              overflow: TextOverflow.ellipsis,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        width: 200,
        titleTextAlign: PlutoColumnTextAlign.start,
        textAlign: PlutoColumnTextAlign.start,
        renderer: (rendererContext) {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              rendererContext.cell.value.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Category',
        field: 'category',
        type: PlutoColumnType.text(),
        width: 120,
        titleTextAlign: PlutoColumnTextAlign.start,
        textAlign: PlutoColumnTextAlign.start,
        renderer: (rendererContext) {
          final category = rendererContext.cell.value.toString();
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Text(
                category,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 80,
        titleTextAlign: PlutoColumnTextAlign.center,
        textAlign: PlutoColumnTextAlign.center,
        renderer: (rendererContext) {
          return Center(
            child: IconButton(
              icon: Icon(
                IconlyLight.delete,
                size: 20,
                color: Theme.of(context).colorScheme.error,
              ),
              onPressed: () {
                final relationshipType = rendererContext.row.cells['id']!.value;
                confirmDeleteRelationshipType(
                  context,
                  relationshipController,
                  relationshipType,
                );
              },
              tooltip: 'Delete',
            ),
          );
        },
      ),
    ];

    final rows =
        relationshipController.relationshipTypes.map((type) {
          return PlutoRow(
            cells: {
              'title': PlutoCell(value: type.title ?? ''),
              'description': PlutoCell(value: type.description ?? ''),
              'category': PlutoCell(value: type.category ?? ''),
              'actions': PlutoCell(value: ''),
              'id': PlutoCell(value: type.id),
            },
          );
        }).toList();

    return PlutoGrid(
      columns: columns,
      rows: rows,
      onLoaded: (PlutoGridOnLoadedEvent event) {
        // relationshipTypesStateManager = event.stateManager;
      },
      configuration: PlutoGridConfiguration(
        style: PlutoGridStyleConfig(
          borderColor: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          gridBackgroundColor: Theme.of(context).colorScheme.surface,
          rowHeight: 48,
          columnTextStyle: Theme.of(context).textTheme.bodyMedium!,
          cellTextStyle: Theme.of(context).textTheme.bodyMedium!,
          iconColor: Theme.of(context).colorScheme.primary,
          activatedColor: Theme.of(
            context,
          ).colorScheme.primary.withOpacity(0.1),
          gridBorderRadius: BorderRadius.circular(8.r),
        ),
      ),
    );
  });
}
