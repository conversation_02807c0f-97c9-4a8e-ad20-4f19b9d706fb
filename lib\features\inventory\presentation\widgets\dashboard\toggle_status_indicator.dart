import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gap/gap.dart';
import 'package:onechurch/features/inventory/controllers/inventory_dashboard_controller.dart';

class ToggleStatusIndicator extends StatelessWidget {
  const ToggleStatusIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<InventoryDashboardController>();
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Obx(() {
      // Only show if at least one section is visible
      if (!controller.hasVisibleSections) {
        return const SizedBox.shrink();
      }

      return Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: colorScheme.primary.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.visibility, size: 16, color: colorScheme.primary),
            Gap(8.w),
            Text(
              'Visible: ',
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
            _buildStatusChip(
              'Dashboard',
              controller.showDashboard.value,
              colorScheme,
            ),
            if (controller.showQuickActions.value) ...[
              Gap(4.w),
              _buildStatusChip(
                'Actions',
                controller.showQuickActions.value,
                colorScheme,
              ),
            ],
            if (controller.showFilters.value) ...[
              Gap(4.w),
              _buildStatusChip(
                'Filters',
                controller.showFilters.value,
                colorScheme,
              ),
            ],
          ],
        ),
      );
    });
  }

  Widget _buildStatusChip(
    String label,
    bool isVisible,
    ColorScheme colorScheme,
  ) {
    if (!isVisible) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: colorScheme.primary.withOpacity(0.2),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: colorScheme.primary,
        ),
      ),
    );
  }
}
