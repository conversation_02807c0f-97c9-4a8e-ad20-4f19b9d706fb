import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../auth/controllers/auth_controller.dart';

class ProfileService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();
  final authController = Get.find<AuthController>();

  // Initialize the service
  ProfileService() {
    _httpService.initializeDio();
  }

  // Fetch user profile
  Future<Map<String, dynamic>> fetchProfile() async {
    try {
      final userId = authController.user.value?.id;
      if (userId == null) {
        return {
          'status': false,
          'message': 'User not authenticated',
          'data': null,
        };
      }

      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/profile/$userId',
        method: Method.GET,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching profile: $e');
      return {
        'status': false,
        'message': 'Failed to fetch profile: $e',
        'data': null,
      };
    }
  }

  // Update user profile
  Future<Map<String, dynamic>> updateProfile({
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? address,
    String? profileImageUrl,
  }) async {
    try {
      final userId = authController.user.value?.id;
      if (userId == null) {
        return {
          'status': false,
          'message': 'User not authenticated',
          'data': null,
        };
      }

      final Map<String, dynamic> profileData = {};

      if (firstName != null) profileData['first_name'] = firstName;
      if (lastName != null) profileData['last_name'] = lastName;
      if (email != null) profileData['email'] = email;
      if (phoneNumber != null) profileData['phone_number'] = phoneNumber;
      if (address != null) profileData['address'] = address;
      if (profileImageUrl != null) {
        profileData['profile_image_url'] = profileImageUrl;
      }

      logger.d('Updating profile with payload: $profileData');

      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/profile/$userId',
        method: Method.PUT,
        params: profileData,
      );

      return response.data;
    } catch (e) {
      logger.e('Error updating profile: $e');
      return {
        'status': false,
        'message': 'Failed to update profile: $e',
        'data': null,
      };
    }
  }

  // Change password
  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final userId = authController.user.value?.id;
      if (userId == null) {
        return {
          'status': false,
          'message': 'User not authenticated',
          'data': null,
        };
      }

      final Map<String, dynamic> passwordData = {
        'current_password': currentPassword,
        'new_password': newPassword,
      };

      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/profile/$userId/change-password',
        method: Method.POST,
        params: passwordData,
      );

      return response.data;
    } catch (e) {
      logger.e('Error changing password: $e');
      return {
        'status': false,
        'message': 'Failed to change password: $e',
        'data': null,
      };
    }
  }

  // Upload profile image
  Future<Map<String, dynamic>> uploadProfileImage(String imagePath) async {
    try {
      final userId = authController.user.value?.id;
      if (userId == null) {
        return {
          'status': false,
          'message': 'User not authenticated',
          'data': null,
        };
      }

      // This would typically use FormData for file upload
      // Implementation depends on your specific upload requirements
      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/profile/$userId/upload-image',
        method: Method.POST,
        params: {'image_path': imagePath},
      );

      return response.data;
    } catch (e) {
      logger.e('Error uploading profile image: $e');
      return {
        'status': false,
        'message': 'Failed to upload image: $e',
        'data': null,
      };
    }
  }
}
