import 'dart:convert';
import 'package:onechurch/data/models/organisation_model.dart';
import 'package:onechurch/data/models/member_category_model.dart';
import 'package:onechurch/data/models/group_model.dart';
import 'package:onechurch/data/models/user_model.dart';
import 'package:onechurch/features/members/models/relationship_models.dart';

class MemberModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final String? phoneNumber;
  final String? secondaryNumber;
  final DateTime? joinDate;
  final DateTime? dob;
  final DateTime? baptismDate;
  final String? profileUrl;
  final String? firstName;
  final String? secondName;
  final String? email;
  final String? address;
  final int? userId;
  final String? idNumber;
  final int? balance;
  final String? accountNumber;
  final Organisation? organisation;
  final String? organisationId;
  final String? verificationStatus;
  final String? status;
  final String? gender;
  final String? maritalStatus;
  final String? occupation;
  final String? educationLevel;
  final String? tribe;
  final String? disability;
  final String? nationality;
  final String? incomeBracket;
  final String? employmentStatus;
  final int? householdSize;
  final String? housingType;
  final List<String>? assetOwnership;
  final MemberCategory? memberCategory;
  final String? memberCategoryId;
  final String? locationId;
  final String? locationCode;

  final int? createdByUserId;
  final UserModel? createdByUser;
  final List<MemberRelationship?>? relationships;
  final List<MemberRelationship?>? relatedTo;
  final List<GroupModel>? groups;

  final String? internalAccount;
  final String? categoryCode;
  final String? categoryName;
  /// NationalId, Passport, AlienId, BirthCertificate
  final String? idType;
  final bool? isChild;
  final Map<String, dynamic>? customFields;

  MemberModel( {
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.phoneNumber,
    this.secondaryNumber,
    this.joinDate,
    this.dob,
    this.baptismDate,
    this.profileUrl,
    this.firstName,
    this.secondName,
    this.email,
    this.address,
    this.userId,
    this.idNumber,
    this.balance,
    this.accountNumber,
    this.organisation,
    this.organisationId,
    this.verificationStatus,
    this.status,
    this.gender,
    this.maritalStatus,
    this.occupation,
    this.educationLevel,
    this.tribe,
    this.disability,
    this.nationality,
    this.incomeBracket,
    this.employmentStatus,
    this.householdSize,
    this.housingType,
    this.assetOwnership,
    this.memberCategory,
    this.memberCategoryId,
    this.locationId,
    this.createdByUserId,
    this.createdByUser,
    this.relationships,
    this.relatedTo,
    this.groups,
    this.locationCode,
    this.internalAccount,
    this.categoryCode,
    this.categoryName,
    this.idType,
    this.isChild,
    this.customFields,
  });

  MemberModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    String? phoneNumber,
    String? secondaryNumber,
    DateTime? joinDate,
    DateTime? dob,
    DateTime? baptismDate,
    String? profileUrl,
    String? firstName,
    String? secondName,
    String? email,
    String? address,
    int? userId,
    String? idNumber,
    int? balance,
    String? accountNumber,
    Organisation? organisation,
    String? organisationId,
    String? verificationStatus,
    String? status,
    String? gender,
    String? maritalStatus,
    String? occupation,
    String? educationLevel,
    String? tribe,
    String? disability,
    String? nationality,
    String? incomeBracket,
    String? employmentStatus,
    int? householdSize,
    String? housingType,
    List<String>? assetOwnership,
    MemberCategory? memberCategory,
    String? memberCategoryId,
    String? locationId,
    String? locationCode,
    int? createdByUserId,
    UserModel? createdByUser,
    List<MemberRelationship?>? relationships,
    List<MemberRelationship?>? relatedTo,
    List<GroupModel>? groups,
    String? internalAccount,
    String? categoryCode,
    String? categoryName,
    String? idType,
    bool? isChild,
    Map<String, dynamic>? customFields,
  }) => MemberModel(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    secondaryNumber: secondaryNumber ?? this.secondaryNumber,
    joinDate: joinDate ?? this.joinDate,
    dob: dob ?? this.dob,
    baptismDate: baptismDate ?? this.baptismDate,
    profileUrl: profileUrl ?? this.profileUrl,
    firstName: firstName ?? this.firstName,
    secondName: secondName ?? this.secondName,
    email: email ?? this.email,
    address: address ?? this.address,
    userId: userId ?? this.userId,
    idNumber: idNumber ?? this.idNumber,
    balance: balance ?? this.balance,
    accountNumber: accountNumber ?? this.accountNumber,
    organisation: organisation ?? this.organisation,
    organisationId: organisationId ?? this.organisationId,
    verificationStatus: verificationStatus ?? this.verificationStatus,
    status: status ?? this.status,
    gender: gender ?? this.gender,
    maritalStatus: maritalStatus ?? this.maritalStatus,
    occupation: occupation ?? this.occupation,
    educationLevel: educationLevel ?? this.educationLevel,
    tribe: tribe ?? this.tribe,
    disability: disability ?? this.disability,
    nationality: nationality ?? this.nationality,
    incomeBracket: incomeBracket ?? this.incomeBracket,
    employmentStatus: employmentStatus ?? this.employmentStatus,
    householdSize: householdSize ?? this.householdSize,
    housingType: housingType ?? this.housingType,
    assetOwnership: assetOwnership ?? this.assetOwnership,
    memberCategory: memberCategory ?? this.memberCategory,
    memberCategoryId: memberCategoryId ?? this.memberCategoryId,
    locationId: locationId ?? this.locationId,
    locationCode: locationCode ?? this.locationCode,
    createdByUserId: createdByUserId ?? this.createdByUserId,
    createdByUser: createdByUser ?? this.createdByUser,
    relationships: relationships ?? this.relationships,
    relatedTo: relatedTo ?? this.relatedTo,
    groups: groups ?? this.groups,
    idType: idType ?? this.idType,
    isChild: isChild ?? this.isChild,
    customFields: customFields ?? this.customFields,
  );

  factory MemberModel.fromRawJson(String str) =>
      MemberModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MemberModel.fromJson(Map<String, dynamic> json) => MemberModel(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"] == null ? null : DateTime.parse(json["deleted_at"]),
    phoneNumber: json["phone_number"],
    secondaryNumber: json["secondary_number"],
    joinDate:
        json["join_date"] == null ? null : DateTime.parse(json["join_date"]),
    dob: json["dob"] == null ? null : DateTime.parse(json["dob"]),
    baptismDate: json["baptism_date"] == null ? null : DateTime.parse(json["baptism_date"]),
    profileUrl: json["profile_url"],
    firstName: json["first_name"],
    secondName: json["second_name"],
    email: json["email"],
    address: json["address"],
    userId: json["user_id"],
    idNumber: json["id_number"],
    balance: json["balance"],
    accountNumber: json["account_number"],
    organisation: json["organisation"] == null ? null : Organisation.fromJson(json["organisation"]),
    organisationId: json["organisation_id"],
    verificationStatus: json["verification_status"],
    status: json["status"],
    gender: json["gender"],
    maritalStatus: json["marital_status"],
    occupation: json["occupation"],
    educationLevel: json["education_level"],
    tribe: json["tribe"],
    disability: json["disability"],
    nationality: json["nationality"],
    incomeBracket: json["income_bracket"],
    employmentStatus: json["employment_status"],
    householdSize: json["household_size"],
    housingType: json["housing_type"],
    assetOwnership: json["asset_ownership"],
    memberCategory: json["member_category"] == null ? null : MemberCategory.fromJson(json["member_category"]),
    memberCategoryId: json["member_category_id"],
    locationId: json["location_id"],
    locationCode: json["location_code"],
    createdByUserId: json["created_by_user_id"],
    createdByUser: json["created_by_user"] == null ? null : UserModel.fromJson(json["created_by_user"]),
    relationships: json["relationships"],
    relatedTo: json["related_to"],
    groups:
        json["groups"] == null
            ? []
            : List<GroupModel>.from(
              json["groups"]!.map((x) => GroupModel.fromJson(x)),
            ),
    idType: json["id_type"],
    isChild: json["is_child"],
    customFields: json["custom_fields"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt?.toIso8601String(),
    "phone_number": phoneNumber,
    "secondary_number": secondaryNumber,
    "join_date": joinDate?.toIso8601String(),
    "dob": dob?.toIso8601String(),
    "baptism_date": baptismDate?.toIso8601String(),
    "profile_url": profileUrl,
    "first_name": firstName,
    "second_name": secondName,
    "email": email,
    "address": address,
    "user_id": userId,
    "id_number": idNumber,
    "balance": balance,
    "account_number": accountNumber,
    "organisation": organisation,
    "organisation_id": organisationId,
    "verification_status": verificationStatus,
    "status": status,
    "gender": gender,
    "marital_status": maritalStatus,
    "occupation": occupation,
    "education_level": educationLevel,
    "tribe": tribe,
    "disability": disability,
    "nationality": nationality,
    "income_bracket": incomeBracket,
    "employment_status": employmentStatus,
    "household_size": householdSize,
    "housing_type": housingType,
    "asset_ownership": assetOwnership,
    "member_category": memberCategory,
    "member_category_id": memberCategoryId,
    "location_id": locationId,
    "location_code": locationCode,
    "created_by_user_id": createdByUserId,
    "created_by_user": createdByUser,
    "relationships": relationships,
    "related_to": relatedTo,
    "groups": groups,
    "id_type": idType,
    "is_child": isChild,
    "custom_fields": customFields,
  };
}
