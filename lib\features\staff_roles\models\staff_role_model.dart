import 'dart:convert';
import 'permission_model.dart';

class StaffRoleModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? name;
  final String? description;
  final String? organisationId;
  final dynamic organisation;
  final bool? isGeneral;
  final String? status;
  final List<PermissionModel>? permissions;
  final List<dynamic>? assignments;

  StaffRoleModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.name,
    this.description,
    this.organisationId,
    this.organisation,
    this.isGeneral,
    this.status,
    this.permissions,
    this.assignments,
  });

  StaffRoleModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? name,
    String? description,
    String? organisationId,
    dynamic organisation,
    bool? isGeneral,
    String? status,
    List<PermissionModel>? permissions,
    List<dynamic>? assignments,
  }) =>
      StaffRoleModel(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        name: name ?? this.name,
        description: description ?? this.description,
        organisationId: organisationId ?? this.organisationId,
        organisation: organisation ?? this.organisation,
        isGeneral: isGeneral ?? this.isGeneral,
        status: status ?? this.status,
        permissions: permissions ?? this.permissions,
        assignments: assignments ?? this.assignments,
      );

  factory StaffRoleModel.fromRawJson(String str) =>
      StaffRoleModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StaffRoleModel.fromJson(Map<String, dynamic> json) => StaffRoleModel(
        id: json["id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        deletedAt: json["deleted_at"],
        name: json["name"],
        description: json["description"],
        organisationId: json["organisation_id"],
        organisation: json["organisation"],
        isGeneral: json["is_general"],
        status: json["status"],
        permissions: json["permissions"] == null
            ? []
            : List<PermissionModel>.from(
                json["permissions"]!.map((x) => PermissionModel.fromJson(x))),
        assignments: json["assignments"] == null
            ? []
            : List<dynamic>.from(json["assignments"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt,
        "name": name,
        "description": description,
        "organisation_id": organisationId,
        "organisation": organisation,
        "is_general": isGeneral,
        "status": status,
        "permissions": permissions == null
            ? []
            : List<dynamic>.from(permissions!.map((x) => x.toJson())),
        "assignments": assignments == null
            ? []
            : List<dynamic>.from(assignments!.map((x) => x)),
      };
}
