import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../core/app/services/storage_service.dart';
import '../../auth/controllers/auth_controller.dart';

class SettingsService {
  final HttpService _httpService = Get.find();
  final StorageService _storageService = Get.find();
  final logger = Get.find<Logger>();
  final authController = Get.find<AuthController>();

  // Initialize the service
  SettingsService() {
    _httpService.initializeDio();
  }

  // Fetch user settings
  Future<Map<String, dynamic>> fetchSettings() async {
    try {
      final userId = authController.user.value?.id;
      if (userId == null) {
        return {
          'status': false,
          'message': 'User not authenticated',
          'data': null,
        };
      }

      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/settings/$userId',
        method: Method.GET,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching settings: $e');
      return {
        'status': false,
        'message': 'Failed to fetch settings: $e',
        'data': null,
      };
    }
  }

  // Update user settings
  Future<Map<String, dynamic>> updateSettings({
    bool? notificationsEnabled,
    bool? emailNotifications,
    bool? smsNotifications,
    String? language,
    String? theme,
    bool? biometricEnabled,
  }) async {
    try {
      final userId = authController.user.value?.id;
      if (userId == null) {
        return {
          'status': false,
          'message': 'User not authenticated',
          'data': null,
        };
      }

      final Map<String, dynamic> settingsData = {};

      if (notificationsEnabled != null) {
        settingsData['notifications_enabled'] = notificationsEnabled;
      }
      if (emailNotifications != null) {
        settingsData['email_notifications'] = emailNotifications;
      }
      if (smsNotifications != null) {
        settingsData['sms_notifications'] = smsNotifications;
      }
      if (language != null) settingsData['language'] = language;
      if (theme != null) settingsData['theme'] = theme;
      if (biometricEnabled != null) {
        settingsData['biometric_enabled'] = biometricEnabled;
      }

      logger.d('Updating settings with payload: $settingsData');

      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/settings/$userId',
        method: Method.PUT,
        params: settingsData,
      );

      // Also save settings locally
      if (response.data['status'] == true) {
        await _saveSettingsLocally(settingsData);
      }

      return response.data;
    } catch (e) {
      logger.e('Error updating settings: $e');
      return {
        'status': false,
        'message': 'Failed to update settings: $e',
        'data': null,
      };
    }
  }

  // Save settings locally
  Future<void> _saveSettingsLocally(Map<String, dynamic> settings) async {
    try {
      for (final entry in settings.entries) {
        await _storageService.write('setting_${entry.key}', entry.value);
      }
      logger.d('Settings saved locally');
    } catch (e) {
      logger.e('Error saving settings locally: $e');
    }
  }

  // Get local setting
  T? getLocalSetting<T>(String key) {
    try {
      return _storageService.read<T>('setting_$key');
    } catch (e) {
      logger.e('Error reading local setting $key: $e');
      return null;
    }
  }

  // Clear all settings
  Future<Map<String, dynamic>> clearSettings() async {
    try {
      final userId = authController.user.value?.id;
      if (userId == null) {
        return {
          'status': false,
          'message': 'User not authenticated',
          'data': null,
        };
      }

      final response = await _httpService.request(
        url: '${ApiUrls.baseUrl}/settings/$userId/clear',
        method: Method.DELETE,
      );

      // Also clear local settings
      if (response.data['status'] == true) {
        await _clearLocalSettings();
      }

      return response.data;
    } catch (e) {
      logger.e('Error clearing settings: $e');
      return {
        'status': false,
        'message': 'Failed to clear settings: $e',
        'data': null,
      };
    }
  }

  // Clear local settings
  Future<void> _clearLocalSettings() async {
    try {
      // Remove all settings keys
      final settingsKeys = [
        'setting_notifications_enabled',
        'setting_email_notifications',
        'setting_sms_notifications',
        'setting_language',
        'setting_theme',
        'setting_biometric_enabled',
      ];

      for (final key in settingsKeys) {
        await _storageService.remove(key);
      }
      logger.d('Local settings cleared');
    } catch (e) {
      logger.e('Error clearing local settings: $e');
    }
  }
}
