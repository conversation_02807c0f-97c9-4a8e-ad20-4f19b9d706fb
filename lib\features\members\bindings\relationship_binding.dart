import 'package:get/get.dart';
import '../controllers/relationship_controller.dart';
import '../services/relationship_service.dart';

class RelationshipBinding extends Bindings {
  @override
  void dependencies() {
    // Register the relationship service if not already registered
    if (!Get.isRegistered<RelationshipService>()) {
      Get.put(RelationshipService());
    }

    // Register the relationship controller
    Get.put(RelationshipController());
  }
}
