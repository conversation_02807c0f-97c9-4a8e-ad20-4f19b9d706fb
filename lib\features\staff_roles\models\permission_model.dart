import 'dart:convert';

class PermissionModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? code;
  final String? name;
  final String? description;
  final dynamic roles;

  PermissionModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.code,
    this.name,
    this.description,
    this.roles,
  });

  PermissionModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? code,
    String? name,
    String? description,
    dynamic roles,
  }) =>
      PermissionModel(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        code: code ?? this.code,
        name: name ?? this.name,
        description: description ?? this.description,
        roles: roles ?? this.roles,
      );

  factory PermissionModel.fromRawJson(String str) =>
      PermissionModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PermissionModel.fromJson(Map<String, dynamic> json) => PermissionModel(
        id: json["id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        deletedAt: json["deleted_at"],
        code: json["code"],
        name: json["name"],
        description: json["description"],
        roles: json["roles"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt,
        "code": code,
        "name": name,
        "description": description,
        "roles": roles,
      };
}
