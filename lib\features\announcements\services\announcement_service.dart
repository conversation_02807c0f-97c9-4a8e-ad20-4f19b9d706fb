import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../data/models/announcement_model.dart';

class AnnouncementService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();

  // Initialize the service
  AnnouncementService() {
    _httpService.initializeDio();
  }

  // Fetch announcements with pagination and filters
  Future<Map<String, dynamic>> fetchAnnouncements({
    required int page,
    required int size,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? status,
  }) async {
    try {
      // Build URL with query parameters
      String url = "${ApiUrls.announcements}?";

      url += "page=$page";
      url += "&size=$size";

      // Add optional filters to URL
      if (searchQuery != null && searchQuery.isNotEmpty) {
        url += "&identifier=${Uri.encodeComponent(searchQuery)}";
      }

      if (startDate != null) {
        final formattedStart = startDate.toUtc().toIso8601String();
        url += "&start_date=${Uri.encodeComponent(formattedStart)}";
      }

      if (endDate != null) {
        final formattedEnd = endDate.toUtc().toIso8601String();
        url += "&end_date=${Uri.encodeComponent(formattedEnd)}";
      }

      if (status != null && status.isNotEmpty) {
        url += "&status=${Uri.encodeComponent(status)}";
      }

      final response = await _httpService.request(url: url, method: Method.GET);
      logger.d('Announcements API Response: ${response.data}');

      // Return the entire response data structure
      return response.data;
    } catch (e) {
      logger.e('Error fetching announcements: $e');
      rethrow;
    }
  }

  // Get announcement by ID
  Future<Map<String, dynamic>> getAnnouncementById(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.announcementById}$id/",
        method: Method.GET,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching announcement by ID: $e');
      rethrow;
    }
  }

  // // Create a new announcement
  // Future<Map<String, dynamic>> createAnnouncement({
  //   required String title,
  //   required String description,
  //   required String status,
  //   List<Map<String, dynamic>>? media,
  // }) async {
  //   try {
  //     final Map<String, dynamic> announcementData = {
  //       'title': title,
  //       'description': description,
  //       'status': status,
  //       'organisation_id': Get.find<AuthController>().currentOrg.value?.id,
  //     };

  //     if (media != null && media.isNotEmpty) {
  //       announcementData['media'] = media;
  //     }

  //     // Wrap the announcement data in an array as expected by the API
  //     final List<Map<String, dynamic>> requestData = [announcementData];

  //     logger.d('Creating announcement with payload: $requestData');

  //     final response = await _httpService.request(
  //       url: ApiUrls.createAnnouncement,
  //       method: Method.POST,
  //       params: requestData, // Send as array
  //     );

  //     logger.d('Announcement creation response: ${response.data}');
  //     return response.data;
  //   } catch (e) {
  //     logger.e('Error creating announcement: $e');
  //     rethrow;
  //   }
  // }

  // Update an existing announcement
  Future<Map<String, dynamic>> updateAnnouncement({
    required String id,
    String? title,
    String? description,
    String? status,
    List<Map<String, dynamic>>? media,
  }) async {
    try {
      final Map<String, dynamic> announcementData = {
        'id': id,
        'organisation_id':
            Get.find<AuthController>()
                .currentOrg
                .value
                ?.id, // Include organisation_id in the payload
      };

      if (title != null) announcementData['title'] = title;
      if (description != null) announcementData['description'] = description;
      if (status != null) announcementData['status'] = status;
      if (media != null) announcementData['media'] = media;

      // Wrap the announcement data in an array as expected by the API
      final List<Map<String, dynamic>> requestData = [announcementData];

      logger.d('Updating announcement with payload: $requestData');

      final response = await _httpService.request(
        url: "${ApiUrls.updateAnnouncement}$id/",
        method: Method.PUT,
        params: requestData, // Send as array
      );

      logger.d('Announcement update response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error updating announcement: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> createMultipleAnnouncements(
    List<Map<String, dynamic>> announcementGroups,
  ) async {
    try {
      logger.d(
        'Creating multiple announcements with payload: $announcementGroups',
      );

      final response = await _httpService.request(
        url: ApiUrls.createAnnouncement,
        method: Method.POST,
        params:
            announcementGroups, // Send the array directly with new structure
      );

      logger.d('Multiple announcements creation response: ${response.data}');
      return response.data;
    } catch (e) {
      logger.e('Error creating multiple announcements: $e');
      rethrow;
    }
  }

  // Delete an announcement
  Future<void> deleteAnnouncement(String id) async {
    try {
      await _httpService.request(
        url: "${ApiUrls.deleteAnnouncement}$id/",
        method: Method.DELETE,
      );
    } catch (e) {
      logger.e('Error deleting announcement: $e');
      rethrow;
    }
  }

  // Parse announcement items from API response
  List<AnnouncementModel> parseAnnouncements(List<dynamic> items) {
    return items.map((item) => AnnouncementModel.fromJson(item)).toList();
  }
}
