import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class LoadingAnimations extends StatelessWidget {
  const LoadingAnimations({super.key});

  @override
  Widget build(BuildContext context) {
    return SpinKitSquareCircle(
      color: Colors.orange,
      size: 50.0,
      // controller: AnimationController(
      //   vsync: context as TickerProvider,
      //   duration: const Duration(milliseconds: 1200),
      // ),
    );
  }
}
