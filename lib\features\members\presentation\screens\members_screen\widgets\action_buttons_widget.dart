import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../../../../../../core/app/constants/routes.dart';

class ActionButtonsWidget extends StatelessWidget {
  final PlutoColumnRendererContext rendererContext;

  const ActionButtonsWidget({super.key, required this.rendererContext});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.visibility, size: 20),
          onPressed: () {
            final memberId = rendererContext.cell.value as String?;
            if (memberId != null) {
              if (kDebugMode) {
                print(
                  'View button navigating to member details with ID: $memberId',
                );
              }
              context.go(Routes.VIEW_MEMBER.replaceFirst(':id', memberId));
            }
          },
          tooltip: 'View Details',
          constraints: const BoxConstraints(),
          padding: const EdgeInsets.all(8),
          visualDensity: VisualDensity.compact,
        ),
        IconButton(
          icon: const Icon(Icons.edit, size: 20),
          onPressed: () {
            final memberId = rendererContext.cell.value as String?;
            if (memberId != null) {
              context.go('${Routes.EDIT_MEMBER}/$memberId');
              if (kDebugMode) {
                debugPrint('Edit member: $memberId');
              }
            }
          },
          tooltip: 'Edit',
          constraints: const BoxConstraints(),
          padding: const EdgeInsets.all(8),
          visualDensity: VisualDensity.compact,
        ),
      ],
    );
  }
}
