import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:pluto_grid/pluto_grid.dart';

class MemberNameCellWidget extends StatelessWidget {
  final PlutoColumnRendererContext rendererContext;

  const MemberNameCellWidget({super.key, required this.rendererContext});

  @override
  Widget build(BuildContext context) {
    final name = rendererContext.cell.value as String?;
    final profile = rendererContext.row.cells['profile']!.value;

    return Row(
      children: [
        SizedBox(
          child: CachedNetworkImage(
            imageUrl: profile,
            fadeOutCurve: Curves.bounceInOut,
            imageBuilder:
                (context, imageProvider) =>
                    CircleAvatar(radius: 20, backgroundImage: imageProvider),
            placeholder:
                (context, url) => LoadingAnimationWidget.discreteCircle(
                  color: Colors.white,
                  size: 16,
                ),
            errorWidget:
                (context, url, error) => CircleAvatar(
                  radius: 20,
                  child: Icon(IconlyBroken.profile, size: 16),
                ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(name ?? '', maxLines: 1, overflow: TextOverflow.ellipsis),
        ),
      ],
    );
  }
}
