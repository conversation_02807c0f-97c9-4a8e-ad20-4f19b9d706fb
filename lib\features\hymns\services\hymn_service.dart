import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../data/models/hymn_model.dart';

class HymnService {
  final HttpService _httpService = HttpService();
  final logger = Get.find<Logger>();

  // Initialize the service
  HymnService() {
    _httpService.initializeDio();
  }

  // Fetch hymns with pagination and filters
  Future<Map<String, dynamic>> fetchHymns({
    required int page,
    required int size,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? organisationId,
    String? createdBy,
  }) async {
    try {
      // Prepare request parameters
      final Map<String, dynamic> params = {'page': page, 'size': size};

      // Add search query if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        params['search'] = searchQuery;
      }

      if (startDate != null) {
        params['start_date'] = startDate.toIso8601String();
      }

      if (endDate != null) {
        params['end_date'] = endDate.toIso8601String();
      }

      if (createdBy != null && createdBy.isNotEmpty) {
        params['created_by'] = createdBy;
      }
      final response = await _httpService.request(
        url: "${ApiUrls.getHymns}?page=$page&size=$size",
        method: Method.GET,
        params: params,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching hymns: $e');
      rethrow;
    }
  }

  // Get hymn by ID
  Future<Map<String, dynamic>> getHymnById(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.getHymnbyID}$id",
        method: Method.GET,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching hymn by ID: $e');
      rethrow;
    }
  }

  // Create a new hymn
  Future<Map<String, dynamic>> createHymn(HymnModel hymn) async {
    try {
      final response = await _httpService.request(
        url: ApiUrls.createHymn,
        method: Method.POST,
        params: hymn.toJson(),
      );

      return response.data;
    } catch (e) {
      logger.e('Error creating hymn: $e');
      rethrow;
    }
  }

  // Add categories to a hymn
  Future<Map<String, dynamic>> addHymnCategories(
    String hymnId,
    List<String> categoryIds,
  ) async {
    try {
      final requests = categoryIds.map(
        (categoryId) => _httpService.request(
          url: ApiUrls.createHymnCategoryRelationship,
          method: Method.POST,
          params: {'hymn_id': hymnId, 'category_id': categoryId},
        ),
      );

      final responses = await Future.wait(requests);
      return {'status': true, 'data': responses.map((r) => r.data).toList()};
    } catch (e) {
      logger.e('Error adding hymn categories: $e');
      rethrow;
    }
  }

  // Update an existing hymn
  Future<Map<String, dynamic>> updateHymn(String id, HymnModel hymn) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.updateHymn}$id",
        method: Method.PUT,
        params: hymn.toJson(),
      );

      return response.data;
    } catch (e) {
      logger.e('Error updating hymn: $e');
      rethrow;
    }
  }

  // Delete a hymn
  Future<Map<String, dynamic>> deleteHymn(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.deleteHymn}$id",
        method: Method.DELETE,
      );

      return response.data;
    } catch (e) {
      logger.e('Error deleting hymn: $e');
      rethrow;
    }
  }

  // Parse hymn items from API response
  List<HymnModel> parseHymns(List<dynamic> items) {
    return items.map((item) => HymnModel.fromJson(item)).toList();
  }
}
