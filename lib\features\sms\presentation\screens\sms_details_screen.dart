import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../data/models/sms_model.dart';

class SmsDetailsScreen extends StatelessWidget {
  final SmsModel message;

  const SmsDetailsScreen({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('MMM dd, yyyy HH:mm');
    final createdAt =
        message.createdAt != null
            ? dateFormat.format(message.createdAt!)
            : 'Unknown date';
    final updatedAt =
        message.updatedAt != null
            ? dateFormat.format(message.updatedAt!)
            : 'Not updated';

    return Scaffold(
      appBar: AppBar(title: const Text('SMS Details')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(
              title: 'Message Information',
              children: [
                _buildInfoRow(
                  'Status',
                  message.status ?? 'UNKNOWN',
                  _getStatusColor(message.status),
                ),
                _buildInfoRow('Created', createdAt),
                _buildInfoRow('Updated', updatedAt),
                _buildInfoRow('Message ID', message.messageId ?? 'N/A'),
                _buildInfoRow(
                  'Transaction ID',
                  message.transactionId?.toString() ?? 'N/A',
                ),
                _buildInfoRow(
                  'External User ID',
                  message.externalUserId?.toString() ?? 'N/A',
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoCard(
              title: 'Message Content',
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    message.message ?? 'No content',
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                _buildInfoRow(
                  'Characters',
                  message.characters?.toString() ?? '0',
                ),
                _buildInfoRow(
                  'Number of SMS',
                  message.numSms?.toString() ?? '0',
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoCard(
              title: 'Organization Information',
              children: [
                _buildInfoRow(
                  'Organization',
                  message.organisationName ?? 'N/A',
                ),
                _buildInfoRow('Organization ID', message.organisation ?? 'N/A'),
                _buildInfoRow(
                  'Total Recipients',
                  message.totalRecipients?.toString() ?? '0',
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoCard(
              title: 'Cost Information',
              children: [
                _buildInfoRow('Cost (Org)', message.costOrg?.toString() ?? '0'),
                _buildInfoRow(
                  'Price per Unit',
                  message.pricePerUnit?.toString() ?? '0',
                ),
                _buildInfoRow(
                  'Third Party Charge',
                  message.thirdPartyCharge?.toString() ?? '0',
                ),
                _buildInfoRow(
                  'Total Charges',
                  message.totalCharges?.toString() ?? '0',
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoCard(
              title: 'Sender Information',
              children: [
                _buildInfoRow('Sender ID', message.senderId ?? 'N/A'),
                _buildInfoRow('Sender Email', message.senderEmail ?? 'N/A'),
                _buildInfoRow('Sender Phone', message.senderPhone ?? 'N/A'),
                _buildInfoRow('Created By ID', message.createdById ?? 'N/A'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, [Color? valueColor]) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value, style: TextStyle(color: valueColor))),
        ],
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status) {
      case 'SENT':
        return Colors.green;
      case 'DELIVERED':
        return Colors.blue;
      case 'PENDING':
        return Colors.orange;
      case 'FAILED':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
