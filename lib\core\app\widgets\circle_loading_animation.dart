import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class CircleLoadingAnimation extends StatelessWidget {
  final double? strokeWidth;
  final dynamic valueColor;
  final Color? color;
  final double? value;
  const CircleLoadingAnimation({
    super.key,
    this.strokeWidth,
    this.valueColor,
    this.color = Colors.orange,
    this.value,
  });

  @override
  Widget build(BuildContext context) {
    return SpinKitFadingCircle(color: Colors.orange, size: 50.0);
  }
}
