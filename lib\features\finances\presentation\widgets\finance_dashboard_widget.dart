import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/features/finances/controllers/finance_controller.dart';

class FinanceDashboardWidget extends StatelessWidget {
  const FinanceDashboardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<FinanceController>();

    return Container(
      padding: EdgeInsets.all(16.w),
      margin: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(IconlyBold.chart, color: Colors.blue.shade700, size: 24),
              SizedBox(width: 12.w),
              Text(
                'Finance Overview',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
              const Spacer(),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      IconlyLight.timeCircle,
                      size: 16,
                      color: Colors.blue.shade700,
                    ),
                    SizedBox(width: 6.w),
                    Text(
                      'Last updated: ${DateFormat('HH:mm').format(DateTime.now())}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          Gap(20.h),

          // Statistics Cards
          Obx(() {
            final totalAccounts = controller.subaccounts.length;
            final totalCategories = controller.categories.length;
            final activeAccounts =
                controller.subaccounts
                    .where(
                      (account) => account.status?.toUpperCase() == 'ACTIVE',
                    )
                    .length;
            final inactiveAccounts = totalAccounts - activeAccounts;

            return Column(
              children: [
                // First Row - Main Stats
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        title: 'Total Accounts',
                        value: totalAccounts.toString(),
                        icon: IconlyBold.wallet,
                        color: Colors.blue,
                        subtitle: 'Sub-accounts',
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _buildStatCard(
                        title: 'Categories',
                        value: totalCategories.toString(),
                        icon: IconlyBold.category,
                        color: Colors.green,
                        subtitle: 'Account categories',
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _buildStatCard(
                        title: 'Active Accounts',
                        value: activeAccounts.toString(),
                        icon: IconlyBold.tickSquare,
                        color: Colors.orange,
                        subtitle: 'Currently active',
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _buildStatCard(
                        title: 'Inactive',
                        value: inactiveAccounts.toString(),
                        icon: IconlyBold.closeSquare,
                        color: Colors.red,
                        subtitle: 'Not active',
                      ),
                    ),
                  ],
                ),

                Gap(16.h),

                // Second Row - Additional Stats
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        title: 'General Categories',
                        value:
                            controller.categories
                                .where((cat) => cat.isGeneral == true)
                                .length
                                .toString(),
                        icon: Icons.category,
                        color: Colors.purple,
                        subtitle: 'System-wide',
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _buildStatCard(
                        title: 'Org Categories',
                        value:
                            controller.categories
                                .where((cat) => cat.isGeneral != true)
                                .length
                                .toString(),
                        icon: IconlyBold.home,
                        color: Colors.teal,
                        subtitle: 'Organization',
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _buildStatCard(
                        title: 'Current Page',
                        value: '${controller.currentPage.value + 1}',
                        icon: IconlyBold.document,
                        color: Colors.indigo,
                        subtitle: 'of ${controller.totalPages.value}',
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _buildStatCard(
                        title: 'Total Items',
                        value: controller.totalItems.value.toString(),
                        icon: IconlyBold.activity,
                        color: Colors.cyan,
                        subtitle: 'All records',
                      ),
                    ),
                  ],
                ),
              ],
            );
          }),

          Gap(20.h),

          // Quick Actions
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Quick Actions',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade700,
                  ),
                ),
                Gap(12.h),
                Row(
                  children: [
                    Expanded(
                      child: _buildQuickActionButton(
                        label: 'Refresh Data',
                        icon: Icons.refresh,
                        onTap: () async {
                          await controller.fetchAccounts();
                          await controller.fetchCategories();
                        },
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _buildQuickActionButton(
                        label: 'Export Data',
                        icon: IconlyLight.download,
                        onTap: () {
                          // TODO: Implement export functionality
                        },
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _buildQuickActionButton(
                        label: 'View Reports',
                        icon: IconlyLight.chart,
                        onTap: () {
                          // TODO: Implement reports functionality
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          Gap(8.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          Text(
            subtitle,
            style: TextStyle(fontSize: 10, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton({
    required String label,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 8.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Column(
          children: [
            Icon(icon, color: Colors.blue.shade600, size: 20),
            Gap(6.h),
            Text(
              label,
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
