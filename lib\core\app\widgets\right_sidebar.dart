import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RightSidebar extends StatefulWidget {
  final Widget child;
  final String title;
  final VoidCallback onClose;

  const RightSidebar({
    super.key,
    required this.child,
    required this.title,
    required this.onClose,
  });

  static Future show({
    required BuildContext context,
    required Widget child,
    required String title,
    required VoidCallback onClose,
  }) async {
    return Navigator.of(
      context,
    ).push(RightSidebarRoute(title: title, onClose: onClose, child: child));
  }

  @override
  State<RightSidebar> createState() => _RightSidebarState();
}

class _RightSidebarState extends State<RightSidebar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _close() {
    _animationController.reverse().then((_) {
      widget.onClose();
      Navigator.of(context).pop();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final size = MediaQuery.of(context).size;
    final sidebarWidth = (size.width * 0.4).clamp(350.0, 450.0);

    return WillPopScope(
      onWillPop: () async {
        _close();
        return false;
      },
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Stack(
          children: [
            // Semi-transparent backdrop
            Positioned.fill(
              child: GestureDetector(
                onTap: _close,
                child: Container(color: Colors.black.withOpacity(0.1)),
              ),
            ),

            // Animated sidebar
            AnimatedBuilder(
              animation: _animation,
              builder: (context, _) {
                return Positioned(
                  right:
                      _animation.value * 0 +
                      (1 - _animation.value) * -sidebarWidth,
                  top: 0,
                  bottom: 0,
                  width: sidebarWidth,
                  child: Material(
                    elevation: 16,
                    color: colorScheme.surface,
                    child: SafeArea(
                      child: Column(
                        children: [
                          // Header
                          Container(
                            padding: EdgeInsets.all(16.r),
                            decoration: BoxDecoration(
                              color: colorScheme.surface,
                              boxShadow: [
                                BoxShadow(
                                  color: colorScheme.shadow.withOpacity(0.05),
                                  blurRadius: 2,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                              border: Border(
                                bottom: BorderSide(
                                  color: colorScheme.outline.withOpacity(0.1),
                                  width: 1,
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                Text(
                                  widget.title,
                                  style: Theme.of(context).textTheme.titleLarge
                                      ?.copyWith(fontWeight: FontWeight.bold),
                                ),
                                const Spacer(),
                                IconButton(
                                  icon: const Icon(Icons.close),
                                  onPressed: _close,
                                  tooltip: 'Close',
                                ),
                              ],
                            ),
                          ),

                          // Form content - made scrollable
                          Expanded(
                            child: SingleChildScrollView(
                              padding: EdgeInsets.zero,
                              child: widget.child,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class RightSidebarRoute extends PageRoute<void> {
  final Widget child;
  final String title;
  final VoidCallback onClose;

  RightSidebarRoute({
    required this.child,
    required this.title,
    required this.onClose,
  }) : super(fullscreenDialog: false);

  @override
  bool get opaque => false;

  @override
  bool get barrierDismissible => true;

  @override
  Color? get barrierColor => Colors.transparent;

  @override
  String? get barrierLabel => null;

  @override
  bool get maintainState => true;

  @override
  Duration get transitionDuration => const Duration(milliseconds: 300);

  @override
  Widget buildPage(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
  ) {
    return RightSidebar(title: title, onClose: onClose, child: child);
  }

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return child;
  }
}
