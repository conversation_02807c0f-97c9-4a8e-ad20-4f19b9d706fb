import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import '../../controllers/sms_requests_controller.dart';

class NewSmsRequestsFilterWidget extends StatelessWidget {
  const NewSmsRequestsFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SmsRequestsController>();

    return Card(
      margin: const EdgeInsets.all(12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(IconlyLight.filter, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Filters',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: () => controller.clearFilters(),
                  icon: const Icon(Icons.clear_all, size: 18),
                  label: const Text('Clear All'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                    visualDensity: VisualDensity.compact,
                  ),
                ),
              ],
            ),
            const Divider(),
            Wrap(
              spacing: 16.0,
              runSpacing: 16.0,
              children: [
                // Phone Number filter
                SizedBox(
                  width: 200.w,
                  child: CustomTextField(
                    controller: controller.phoneNumberController,
                    labelText: 'Phone Number',
                    hintText: 'Enter phone number...',
                    prefixIcon: const Icon(IconlyLight.call, size: 18),

                    onChanged:
                        (value) => controller.phoneNumberFilter.value = value,
                  ),
                ),

                // Message filter
                SizedBox(
                  width: 200.w,
                  child: CustomTextField(
                    controller: controller.messageFilterController,
                    labelText: 'Message Content',
                    hintText: 'Filter by message content...',
                    prefixIcon: const Icon(IconlyLight.message, size: 18),
                    onChanged:
                        (value) => controller.messageFilter.value = value,
                  ),
                ),

                // Date range filters
                SizedBox(
                  width: 200.w,
                  child: Obx(() {
                    final startDateText =
                        controller.startDate.value != null
                            ? DateFormat(
                              'dd/MM/yyyy',
                            ).format(controller.startDate.value!)
                            : 'Start Date';

                    return CustomButton(
                      style: CustomButtonStyle.outlined,
                      onPressed: () => _selectStartDate(context, controller),
                      icon: const Icon(IconlyLight.calendar, size: 18),
                      label: Text(startDateText),
                    );
                  }),
                ),

                SizedBox(
                  width: 200.w,
                  child: Obx(() {
                    final endDateText =
                        controller.endDate.value != null
                            ? DateFormat(
                              'dd/MM/yyyy',
                            ).format(controller.endDate.value!)
                            : 'End Date';

                    return CustomButton(
                      style: CustomButtonStyle.outlined,
                      onPressed: () => _selectEndDate(context, controller),
                      icon: const Icon(IconlyLight.calendar, size: 18),
                      label: Text(endDateText),
                    );
                  }),
                ),
              ],
            ),
            Gap(16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CustomButton(
                  onPressed: () => controller.fetchMessages(),
                  icon: const Icon(IconlyLight.filter, size: 18),
                  label: const Text('Apply Filters'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Select start date
  Future<void> _selectStartDate(
    BuildContext context,
    SmsRequestsController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.startDate.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      // Set to start of day for inclusive filtering
      final startOfDay = DateTime(
        picked.year,
        picked.month,
        picked.day,
        0,
        0,
        0,
      );
      controller.startDate.value = startOfDay;
    }
  }

  // Select end date
  Future<void> _selectEndDate(
    BuildContext context,
    SmsRequestsController controller,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.endDate.value ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      // Set to end of day for inclusive filtering
      final endOfDay = DateTime(
        picked.year,
        picked.month,
        picked.day,
        23,
        59,
        59,
      );
      controller.endDate.value = endOfDay;
    }
  }
}
