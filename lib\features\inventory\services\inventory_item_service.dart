import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../core/app/services/api_urls.dart';
import '../../../core/app/services/http_service.dart';
import '../../auth/controllers/auth_controller.dart';
import '../models/inventory_category_model.dart';

class InventoryItemService {
  final HttpService _httpService = Get.find<HttpService>();
  final Logger logger = Get.find<Logger>();

  // Fetch inventory items with pagination and filters
  Future<Map<String, dynamic>> fetchInventoryItems({
    required int page,
    required int size,
    String? searchQuery,
    String? category,
    DateTime? startDate,
    DateTime? endDate,
    String? organisationId,
  }) async {
    try {
      // Prepare request parameters
      final Map<String, dynamic> params = {
        'organisation_id': organisationId,
        'page': page,
        'size': size,
      };

      // Add search query if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        params['search'] = searchQuery;
      }

      // Add category filter if provided
      if (category != null && category.isNotEmpty) {
        params['category'] = category;
      }

      // Add date filters if provided
      if (startDate != null) {
        params['start_date'] = startDate.toUtc().toIso8601String();
      }

      if (endDate != null) {
        params['end_date'] = endDate.toUtc().toIso8601String();
      }

      final response = await _httpService.request(
        url: "${ApiUrls.getInventoryItems}?page=$page&size=$size",
        method: Method.GET,
        params: params,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching inventory items: $e');
      rethrow;
    }
  }

  // Get inventory item by ID
  Future<Map<String, dynamic>> getInventoryItemById(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.getInventoryItemById}$id",
        method: Method.GET,
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching inventory item by ID: $e');
      rethrow;
    }
  }

  // Create a new inventory item
  Future<Map<String, dynamic>> createInventoryItem({
    required String title,
    required String categoryId,
    required String unitOfMeasureId,
    String? barcode,
    String? description,
    required String organisationId,
    List<Map<String, dynamic>>? media,
  }) async {
    try {
      final Map<String, dynamic> itemData = {
        'title': title,
        'category_id': categoryId, // Send category_id instead of category name
        'unit_of_measure_id': unitOfMeasureId,
        'organisation_id': organisationId,
        'status': 'active',
      };

      if (barcode != null && barcode.isNotEmpty) {
        itemData['barcode'] = barcode;
      }

      if (description != null && description.isNotEmpty) {
        itemData['description'] = description;
      }

      if (media != null && media.isNotEmpty) {
        itemData['media'] = media;
      }

      logger.d('Creating inventory item with payload: $itemData');

      final response = await _httpService.request(
        url: ApiUrls.createInventoryItem,
        method: Method.POST,
        params: [itemData],
      );

      return response.data;
    } catch (e) {
      logger.e('Error creating inventory item: $e');
      rethrow;
    }
  }

  /*
item_category_id: // nullable
*/

  // Update inventory item
  Future<Map<String, dynamic>> updateInventoryItem({
    required String id,
    required String title,
    required String category,
    required String unitOfMeasureId,
    String? barcode,
    String? description,
    required String organisationId,
    String? status,
    List<Map<String, dynamic>>? media,
  }) async {
    try {
      final Map<String, dynamic> itemData = {
        'id': id,
        'title': title,
        'category': category,
        'unit_of_measure_id': unitOfMeasureId,
        'organisation_id': organisationId,
        'status': status ?? 'active',
      };

      if (barcode != null && barcode.isNotEmpty) {
        itemData['barcode'] = barcode;
      }

      if (description != null && description.isNotEmpty) {
        itemData['description'] = description;
      }

      if (media != null && media.isNotEmpty) {
        itemData['media'] = media;
      }

      logger.d('Updating inventory item with payload: $itemData');

      final response = await _httpService.request(
        url: "${ApiUrls.updateInventoryItem}$id",
        method: Method.PUT,
        params: itemData,
      );

      return response.data;
    } catch (e) {
      logger.e('Error updating inventory item: $e');
      rethrow;
    }
  }

  // Delete inventory item
  Future<Map<String, dynamic>> deleteInventoryItem(String id) async {
    try {
      final response = await _httpService.request(
        url: "${ApiUrls.deleteInventoryItem}$id",
        method: Method.DELETE,
      );

      return response.data;
    } catch (e) {
      logger.e('Error deleting inventory item: $e');
      rethrow;
    }
  }

  // Get inventory categories from the items API response
  Future<List<InventoryCategory>> getInventoryCategories() async {
    try {
      // Fetch inventory items to get categories from the response
      final response = await fetchInventoryItems(
        page: 0,
        size: 1, // We only need one item to get the categories
        organisationId: Get.find<AuthController>().currentOrg.value?.id,
      );

      if (response["status"] ?? false) {
        final data = response["data"];

        if (data != null && data is Map) {
          final itemCategories = data["item_categories"];

          if (itemCategories != null && itemCategories is List) {
            return itemCategories
                .map((category) => InventoryCategory.fromJson(category))
                .toList();
          }
        }
      }

      // Return empty list if no categories found
      return [];
    } catch (e) {
      logger.e('Error fetching inventory categories: $e');
      return [];
    }
  }

  // Get inventory conditions
  List<String> getInventoryConditions() {
    return [
      'New',
      'Like New',
      'Good',
      'Fair',
      'Poor',
      'Damaged',
      'Needs Repair',
    ];
  }

  // Get inventory types
  List<String> getInventoryTypes() {
    return [
      'Donation',
      'Purchase',
      'Transfer In',
      'Transfer Out',
      'Lost',
      'Damaged',
      'Disposed',
      'Maintenance',
      'Loan In',
      'Loan Out',
    ];
  }
}
