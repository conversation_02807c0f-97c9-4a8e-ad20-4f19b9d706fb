import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onechurch/features/inventory/controllers/inventory_dashboard_controller.dart';

class DashboardAppBar extends StatelessWidget implements PreferredSizeWidget {
  const DashboardAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<InventoryDashboardController>();
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AppBar(
      title: Text(
        controller.appBarTitle,
        style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 20),
      ),
      backgroundColor: colorScheme.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: false,
      actions: [
        // Search Action
        IconButton(
          onPressed: () => _showSearchDialog(context, controller),
          icon: const Icon(IconlyLight.search),
          tooltip: 'Search',
        ),

        // Toggle All Sections
        Obx(
          () => IconButton(
            onPressed: controller.toggleAllSections,
            icon: Icon(
              controller.hasVisibleSections
                  ? IconlyBold.hide
                  : IconlyLight.show,
            ),
            tooltip:
                controller.hasVisibleSections
                    ? 'Hide All Sections'
                    : 'Show All Sections',
          ),
        ),

        // Toggle Dashboard Stats
        Obx(
          () => IconButton(
            onPressed: controller.toggleDashboard,
            icon: Icon(
              controller.showDashboard.value
                  ? IconlyBold.chart
                  : IconlyLight.chart,
            ),
            tooltip:
                controller.showDashboard.value
                    ? 'Hide Dashboard'
                    : 'Show Dashboard',
          ),
        ),

        // Toggle Quick Actions
        Obx(
          () => IconButton(
            onPressed: controller.toggleQuickActions,
            icon: Icon(
              controller.showQuickActions.value
                  ? IconlyBold.activity
                  : IconlyLight.activity,
            ),
            tooltip:
                controller.showQuickActions.value
                    ? 'Hide Quick Actions'
                    : 'Show Quick Actions',
          ),
        ),

        // Toggle Filters
        Obx(
          () => IconButton(
            onPressed: controller.toggleFilters,
            icon: Icon(
              controller.showFilters.value
                  ? IconlyBold.filter
                  : IconlyLight.filter,
            ),
            tooltip:
                controller.showFilters.value ? 'Hide Filters' : 'Show Filters',
          ),
        ),

        // Refresh Action
        IconButton(
          onPressed: () => controller.refreshData(),
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),

        // Create Action
        Obx(
          () => IconButton(
            onPressed: () => controller.handleCreateAction(context),
            icon: Icon(
              controller.isItemsView.value
                  ? IconlyLight.plus
                  : IconlyLight.document,
            ),
            tooltip: controller.currentActionTooltip,
          ),
        ),

        SizedBox(width: 8.w),
      ],
      bottom: TabBar(
        controller: controller.tabController,
        indicatorColor: Colors.white,
        indicatorWeight: 3.h,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        labelStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        unselectedLabelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        tabs: const [
          Tab(icon: Icon(IconlyLight.category, size: 20), text: 'Items'),
          Tab(icon: Icon(IconlyLight.document, size: 20), text: 'Records'),
        ],
      ),
    );
  }

  void _showSearchDialog(
    BuildContext context,
    InventoryDashboardController controller,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Search'),
            content: TextField(
              autofocus: true,
              decoration: const InputDecoration(
                hintText: 'Enter search term...',
                border: OutlineInputBorder(),
              ),
              onSubmitted: (value) {
                Navigator.of(context).pop();
                if (value.trim().isNotEmpty) {
                  controller.performSearch(value.trim());
                }
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
            ],
          ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight + 48.h);
}
