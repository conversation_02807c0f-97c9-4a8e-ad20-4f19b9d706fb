import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/app/constants/routes.dart';
import '../../../../core/app/widgets/custom_dropdown.dart';
import '../../../../core/app/widgets/custom_textformfield.dart';
import '../../../media_upload/media_upload.dart';
import '../../controllers/create_inventory_item_controller.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';

class CreateInventoryItemScreen extends StatelessWidget {
  const CreateInventoryItemScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final CreateInventoryItemController controller =
        Get.find<CreateInventoryItemController>();
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text(
          'Create Inventory Item',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(Routes.INVENTORY),
        ),
      ),
      body: Form(
        key: controller.formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                'Item Information',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
              Gap(24.h),

              // Title field
              CustomTextFormField(
                controller: controller.titleController,
                labelText: 'Item Title *',
                hintText: 'Enter item title',
                prefixIcon: Icon(IconlyLight.bag, color: colorScheme.primary),
                validator: controller.validateTitle,
              ),
              Gap(16.h),

              // Category dropdown
              Obx(
                () => CustomDropdown<String>(
                  labelText: 'Category *',
                  value:
                      controller.selectedCategory.value.isEmpty
                          ? null
                          : controller.selectedCategory.value,
                  hintText: 'Select a category',
                  prefixIcon: Icon(
                    IconlyLight.category,
                    color: colorScheme.primary,
                  ),
                  items:
                      controller.categories.map((category) {
                        return DropdownMenuItem<String>(
                          value: category.id, // Use category ID as value
                          child: Text(
                            category.title ?? 'Unknown Category',
                          ), // Display category name
                        );
                      }).toList(),
                  onChanged: controller.setSelectedCategory,
                  validator: controller.validateCategory,
                ),
              ),
              Gap(16.h),

              // Unit of measure dropdown
              Obx(
                () => CustomDropdown<String>(
                  labelText: 'Unit of Measure *',
                  value:
                      controller.selectedUnitOfMeasure.value.isEmpty
                          ? null
                          : controller.selectedUnitOfMeasure.value,
                  hintText: 'Select unit of measure',
                  prefixIcon: Icon(
                    IconlyLight.chart,
                    color: colorScheme.primary,
                  ),
                  items:
                      controller.unitsOfMeasure.map((unit) {
                        return DropdownMenuItem<String>(
                          value: unit.id,
                          child: Text(unit.fullDisplay),
                        );
                      }).toList(),
                  onChanged: controller.setSelectedUnitOfMeasure,
                  validator: controller.validateUnitOfMeasure,
                ),
              ),
              Gap(16.h),

              // Barcode field
              CustomTextFormField(
                controller: controller.barcodeController,
                labelText: 'Barcode',
                hintText: 'Enter barcode (optional)',
                prefixIcon: Icon(IconlyLight.scan, color: colorScheme.primary),
                keyboardType: TextInputType.text,
              ),
              Gap(16.h),

              // Description field
              CustomTextFormField(
                controller: controller.descriptionController,
                labelText: 'Description',
                hintText: 'Enter item description (optional)',
                prefixIcon: Icon(
                  IconlyLight.document,
                  color: colorScheme.primary,
                ),
                maxLines: 3,
                keyboardType: TextInputType.multiline,
              ),
              Gap(24.h),

              // Media upload section
              Card(
                elevation: 0,
                color: colorScheme.surfaceVariant.withOpacity(0.3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  side: BorderSide(color: colorScheme.outline.withOpacity(0.5)),
                ),
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(IconlyLight.image, color: colorScheme.primary),
                          Gap(8.w),
                          Text(
                            'Media (Optional)',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      Gap(8.h),
                      Text(
                        'Add photos of the inventory item',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                      Gap(16.h),
                      MediaUploadWidget(
                        category: 'INVENTORY',
                        multipleSelect: true,
                        onMediaSelected: controller.setMediaItems,
                        width: double.infinity,
                        height: 120.h,
                      ),
                      Gap(12.h),
                      Obx(
                        () =>
                            controller.mediaItems.isEmpty
                                ? Center(
                                  child: Padding(
                                    padding: EdgeInsets.all(16.r),
                                    child: Text(
                                      'No media items added yet',
                                      style: theme.textTheme.bodyMedium
                                          ?.copyWith(
                                            color: colorScheme.onSurface
                                                .withOpacity(0.6),
                                          ),
                                    ),
                                  ),
                                )
                                : ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: controller.mediaItems.length,
                                  itemBuilder: (context, index) {
                                    final item = controller.mediaItems[index];
                                    return ListTile(
                                      contentPadding: EdgeInsets.zero,
                                      leading: ClipRRect(
                                        borderRadius: BorderRadius.circular(
                                          4.r,
                                        ),
                                        child: Image.network(
                                          item.mediaUrl ?? '',
                                          width: 40.w,
                                          height: 40.h,
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) =>
                                                  Container(
                                                    width: 40.w,
                                                    height: 40.h,
                                                    color:
                                                        colorScheme
                                                            .surfaceVariant,
                                                    child: Icon(
                                                      Icons.error,
                                                      size: 20.r,
                                                      color: colorScheme.error,
                                                    ),
                                                  ),
                                        ),
                                      ),
                                      title: Text(
                                        item.title ?? 'Media Item',
                                        style: theme.textTheme.bodyMedium,
                                      ),
                                      subtitle: Text(
                                        item.mediaUrl ?? '',
                                        overflow: TextOverflow.ellipsis,
                                        style: theme.textTheme.bodySmall
                                            ?.copyWith(
                                              color: colorScheme.onSurface
                                                  .withOpacity(0.6),
                                            ),
                                      ),
                                      trailing: IconButton(
                                        icon: Icon(
                                          Icons.delete,
                                          color: colorScheme.error,
                                          size: 20.r,
                                        ),
                                        onPressed:
                                            () => controller.removeMediaItem(
                                              index,
                                            ),
                                        tooltip: 'Remove',
                                      ),
                                    );
                                  },
                                ),
                      ),
                    ],
                  ),
                ),
              ),
              Gap(32.h),

              // Submit button
              Obx(
                () => SizedBox(
                  width: double.infinity,
                  height: 48.h,
                  child: CustomButton(
                    onPressed:
                        controller.isLoading.value
                            ? null
                            : () => controller.submitForm(context),
                    isLoading: controller.isLoading.value,

                    label: Text(
                      'Create Item',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
              Gap(16.h),
            ],
          ),
        ),
      ),
    );
  }
}
