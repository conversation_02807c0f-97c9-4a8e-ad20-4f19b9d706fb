import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import '../../../../../core/app/constants/routes.dart';
import '../../../../../core/app/utils/show_toast.dart';
import '../../../../../data/models/member_category_model.dart';
import '../../../controllers/member_category_controller.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';

class EditMemberCategoryScreen extends StatefulWidget {
  final String categoryId;

  const EditMemberCategoryScreen({super.key, required this.categoryId});

  @override
  State<EditMemberCategoryScreen> createState() =>
      _EditMemberCategoryScreenState();
}

class _EditMemberCategoryScreenState extends State<EditMemberCategoryScreen> {
  final _formKey = GlobalKey<FormState>();
  final controller = Get.find<MemberCategoryController>();

  // Form controllers
  final titleController = TextEditingController();
  final descriptionController = TextEditingController();
  final codeController = TextEditingController();

  // Loading state
  final isLoading = false.obs;
  final Rx<MemberCategory?> category = Rx<MemberCategory?>(null);

  @override
  void initState() {
    super.initState();
    _loadCategory();
  }

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    codeController.dispose();
    super.dispose();
  }

  // Load category details
  Future<void> _loadCategory() async {
    isLoading.value = true;

    try {
      // Find the category in the controller's list
      final foundCategory = controller.categories.firstWhere(
        (c) => c.id == widget.categoryId,
        orElse: () => throw Exception('Category not found'),
      );

      category.value = foundCategory;
      titleController.text = foundCategory.title ?? '';
      descriptionController.text = foundCategory.description ?? '';
      codeController.text = foundCategory.code ?? '';

      isLoading.value = false;
    } catch (e) {
      ToastUtils.showErrorToast('Failed to load category: $e', null);
      context.go(Routes.MEMBER_CATEGORIES);
    }
  }

  // Submit form
  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      final result = await controller.updateCategory(
        id: widget.categoryId,
        title: titleController.text.trim(),
        description: descriptionController.text.trim(),
        code: codeController.text.trim().toUpperCase(),
      );

      if (result) {
        ToastUtils.showSuccessToast('Category updated successfully', null);
        context.go(Routes.MEMBER_CATEGORIES);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(title: const Text('Edit Member Category')),
      body: Obx(
        () =>
            isLoading.value
                ? const Center(child: CircleLoadingAnimation())
                : SingleChildScrollView(
                  padding: EdgeInsets.all(16.r),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Info banner
                        Container(
                          padding: EdgeInsets.all(16.r),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primaryContainer
                                .withOpacity(0.3),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                IconlyLight.infoSquare,
                                color: theme.colorScheme.primary,
                              ),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Edit Member Category',
                                      style: theme.textTheme.titleMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                    ),
                                    SizedBox(height: 4.h),
                                    Text(
                                      'Update the details of this member category.',
                                      style: theme.textTheme.bodyMedium,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        Gap(24.h),

                        // Title
                        CustomTextFormField(
                          controller: titleController,
                          labelText: 'Title',
                          hintText: 'e.g., Youth Member, Elder, Deacon',
                          prefixIcon: const Icon(IconlyLight.category),

                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a title';
                            }
                            return null;
                          },
                        ),
                        Gap(16.h),

                        // Code
                        CustomTextFormField(
                          controller: codeController,
                          labelText: 'Code',
                          hintText: 'e.g., YOUTH, ELDER, DEACON',
                          prefixIcon: const Icon(IconlyLight.document),
                          helperText:
                              'A unique identifier for this category (max 6 characters)',

                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a code';
                            }
                            if (value.length > 6) {
                              return 'Code must be 6 characters or less';
                            }
                            return null;
                          },
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                              RegExp(r'[A-Za-z0-9]'),
                            ),
                            LengthLimitingTextInputFormatter(6),
                            TextInputFormatter.withFunction((
                              oldValue,
                              newValue,
                            ) {
                              return TextEditingValue(
                                text: newValue.text.toUpperCase(),
                                selection: newValue.selection,
                              );
                            }),
                          ],
                        ),
                        Gap(16.h),

                        // Description
                        CustomTextFormField(
                          controller: descriptionController,
                          labelText: 'Description',
                          hintText: 'Describe this category...',
                          prefixIcon: const Icon(IconlyLight.paper),

                          maxLines: 3,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a description';
                            }
                            return null;
                          },
                        ),
                        Gap(16.h),

                        // Submit button
                        SizedBox(
                          width: double.infinity,
                          child: Obx(
                            () => CustomButton(
                              onPressed:
                                  controller.isSubmitting.value
                                      ? null
                                      : _submitForm,
                              isLoading: controller.isSubmitting.value,
                              label: const Text('Update Category'),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
      ),
    );
  }
}
