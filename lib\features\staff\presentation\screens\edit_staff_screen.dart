import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/constants/enums.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/features/staff/models/staff_model.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
import '../../controllers/staff_controller.dart';
import '../../../../core/app/constants/routes.dart';
import '../../../../core/app/utils/show_toast.dart';
import '../../../../core/widgets/custom_phone_input.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
class EditStaffScreen extends StatefulWidget {
  final String staffId;
  final StaffModel? staff;

  const EditStaffScreen({super.key, required this.staffId, this.staff});

  @override
  State<EditStaffScreen> createState() => _EditStaffScreenState();
}

class _EditStaffScreenState extends State<EditStaffScreen> {
  final StaffController controller = Get.find<StaffController>();
  final RxBool isLoading = true.obs;
  final Rx<StaffModel?> staff = Rx<StaffModel?>(null);

  @override
  void initState() {
    super.initState();
    _loadStaffData();
  }

  Future<void> _loadStaffData() async {
    isLoading.value = true;
    try {
      // Use the staff model passed from the table if available
      if (widget.staff != null) {
        // Set the staff data in the local state
        staff.value = widget.staff;

        // Populate the form fields with the staff data
        controller.phoneNumberController.text = widget.staff!.phoneNumber ?? '';
        controller.firstNameController.text = widget.staff!.firstName ?? '';
        controller.secondNameController.text = widget.staff!.secondName ?? '';
        controller.codeController.text = widget.staff!.code ?? '';
        controller.emailController.text = widget.staff!.email ?? '';
        controller.idNumberController.text = widget.staff!.idNumber ?? '';
        controller.roleValue.value =
            widget.staff!.roleAssignments?.isNotEmpty ?? false
                ? widget.staff!.roleAssignments![0].toString()
                : '';
        controller.isMakerValue.value = widget.staff!.isMaker ?? false;
        controller.isCheckerValue.value = widget.staff!.isChecker ?? false;
        controller.isSignatoryValue.value = widget.staff!.isSignatory ?? false;
        controller.assignedLocations.value =
            widget.staff!.assignedLocations ?? [];
        controller.isEditing.value = true;
        controller.currentStaffId.value = widget.staff!.id ?? '';
      } else {
        // Fallback to API call if staff model wasn't passed
        final staffData = await controller.getStaffById(widget.staffId);

        if (staffData != null) {
          // Set the staff data in the local state
          staff.value = staffData;

          // Populate the form fields with the staff data
          controller.phoneNumberController.text = staffData.phoneNumber ?? '';
          controller.firstNameController.text = staffData.firstName ?? '';
          controller.secondNameController.text = staffData.secondName ?? '';
          controller.codeController.text = staffData.code ?? '';
          controller.emailController.text = staffData.email ?? '';
          controller.idNumberController.text = staffData.idNumber ?? '';
          controller.roleValue.value =
              staffData.roleAssignments?.isNotEmpty ?? false
                  ? staffData.roleAssignments![0].toString()
                  : '';
          controller.isMakerValue.value = staffData.isMaker ?? false;
          controller.isCheckerValue.value = staffData.isChecker ?? false;
          controller.isSignatoryValue.value = staffData.isSignatory ?? false;
          controller.assignedLocations.value =
              staffData.assignedLocations ?? [];
          controller.isEditing.value = true;
          controller.currentStaffId.value = staffData.id ?? '';
        } else {
          ToastUtils.showErrorToast(
            'Staff not found',
            'Unable to find staff with ID: ${widget.staffId}',
          );
        }
      }
    } catch (e) {
      ToastUtils.showErrorToast('Error loading staff details', e.toString());
    } finally {
      isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Staff Member'),
        leading: IconButton(
          icon: const Icon(IconlyLight.arrowLeft),
          onPressed: () {
            controller.clearForm();
            context.go(Routes.STAFF);
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(IconlyLight.show),
            onPressed:
                () => context.go('${Routes.STAFF}/${widget.staffId}/view'),
            tooltip: 'View Staff',
          ),
        ],
      ),
      body: Obx(() {
        if (isLoading.value) {
          return const Center(child: CircleLoadingAnimation());
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Personal Information',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Phone Number
              CustomPhoneInput(
                label: 'Phone Number',
                controller: controller.phoneNumberController,
                onPhoneNumberChanged: controller.onPhoneNumberChanged,
                errorText:
                    controller.isPhoneValid.value
                        ? null
                        : 'Invalid phone number',
              ),
              const SizedBox(height: 16),

              // First Name
              _buildTextField(
                controller: controller.firstNameController,
                label: 'First Name',
                prefixIcon: IconlyLight.profile,
                isRequired: true,
              ),
              const SizedBox(height: 16),

              // Second Name
              _buildTextField(
                controller: controller.secondNameController,
                label: 'Second Name',
                prefixIcon: IconlyLight.profile,
                isRequired: true,
              ),
              const SizedBox(height: 16),

              // Email
              _buildTextField(
                controller: controller.emailController,
                label: 'Email',
                prefixIcon: IconlyLight.message,
                keyboardType: TextInputType.emailAddress,
                isRequired: true,
              ),
              const SizedBox(height: 16),

              // ID Number
              _buildTextField(
                controller: controller.idNumberController,
                label: 'ID Number',
                prefixIcon: IconlyLight.document,
              ),
              const SizedBox(height: 16),

              // Staff Code
              _buildTextField(
                controller: controller.codeController,
                label: 'Staff Code',
                prefixIcon: IconlyLight.scan,
                helperText: 'Unique identifier for this staff member',
              ),
              const SizedBox(height: 24),

              Text(
                'Role & Permissions',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Role Dropdown
              Obx(
                () => DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: 'Role *',
                    prefixIcon: Icon(
                      IconlyLight.profile,
                      color: colorScheme.primary,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  value:
                      controller.roleValue.value.isEmpty
                          ? null
                          : controller.roleValue.value,
                  items:
                      StaffRole.values.map((role) {
                        return DropdownMenuItem(
                          value: role.name,
                          child: Text(role.name),
                        );
                      }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      controller.roleValue.value = value;
                    }
                  },
                  hint: const Text('Select Role'),
                ),
              ),
              const SizedBox(height: 16),

              // Permission Switches
              Obx(
                () => SwitchListTile(
                  title: const Text('Maker'),
                  subtitle: const Text('Can create new records'),
                  value: controller.isMakerValue.value,
                  onChanged: (value) => controller.isMakerValue.value = value,
                  activeColor: colorScheme.primary,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              Obx(
                () => SwitchListTile(
                  title: const Text('Checker'),
                  subtitle: const Text('Can approve/review records'),
                  value: controller.isCheckerValue.value,
                  onChanged: (value) => controller.isCheckerValue.value = value,
                  activeColor: colorScheme.primary,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              Obx(
                () => SwitchListTile(
                  title: const Text('Signatory'),
                  subtitle: const Text('Can sign off on documents'),
                  value: controller.isSignatoryValue.value,
                  onChanged:
                      (value) => controller.isSignatoryValue.value = value,
                  activeColor: colorScheme.primary,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const SizedBox(height: 24),

              // Submit Button
              Obx(
                () => SizedBox(
                  width: double.infinity,
                  child: CustomButton(
                    onPressed:
                        controller.isSubmitting.value
                            ? null
                            : () => _submitForm(context),
                    icon:
                        controller.isSubmitting.value
                            ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircleLoadingAnimation(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                            : const Icon(IconlyLight.tickSquare),
                    label: const Text('Update Staff Member'),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData prefixIcon,
    TextInputType? keyboardType,
    String? helperText,
    bool isRequired = false,
  }) {
    return CustomTextField(
      controller: controller,
      keyboardType: keyboardType,
      labelText: isRequired ? '$label *' : label,
      prefixIcon: Icon(prefixIcon),
      hintText: helperText,
    );
  }

  void _submitForm(BuildContext context) async {
    // Validate required fields
    if (controller.firstNameController.text.isEmpty ||
        controller.secondNameController.text.isEmpty ||
        controller.emailController.text.isEmpty ||
        controller.roleValue.value.isEmpty) {
      ToastUtils.showErrorToast('Please fill all required fields', null);
      return;
    }

    final result = await controller.updateStaff();

    if (result) {
      ToastUtils.showSuccessToast('Staff member updated successfully', null);
      if (mounted) {
        context.go(Routes.STAFF);
      }
    }
  }
}
