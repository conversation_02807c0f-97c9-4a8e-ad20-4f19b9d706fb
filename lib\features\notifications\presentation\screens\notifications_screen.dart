import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../controllers/notification_controller.dart';
import '../../../../data/models/notification_model.dart';
import '../../../../core/app/constants/routes.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final controller = Get.find<NotificationController>();
    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Notifications',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(IconlyLight.search),
            onPressed: () => _showSearchDialog(context),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'mark_all_read') {
                controller.markAllAsRead();
              } else if (value == 'toggle_unread') {
                controller.toggleUnreadOnly();
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem<String>(
                    value: 'mark_all_read',
                    child: Text('Mark all as read'),
                  ),
                  PopupMenuItem<String>(
                    value: 'toggle_unread',
                    child: Obx(
                      () => Text(
                        controller.showUnreadOnly.value
                            ? 'Show all notifications'
                            : 'Show unread only',
                      ),
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value && controller.notifications.isEmpty) {
          return const Center(child: CircleLoadingAnimation());
        }

        if (controller.notifications.isEmpty && !controller.isLoading.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(IconlyLight.notification, size: 48),
                const SizedBox(height: 16),
                Text(
                  'No notifications found',
                  style: theme.textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  controller.showUnreadOnly.value
                      ? 'You have read all your notifications'
                      : 'You don\'t have any notifications yet',
                  style: theme.textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: controller.refreshNotifications,
          child: ListView.separated(
            padding: const EdgeInsets.all(16),
            itemCount:
                controller.notifications.length +
                (controller.isLoading.value ? 1 : 0),
            separatorBuilder: (context, index) => const Divider(),
            itemBuilder: (context, index) {
              if (index == controller.notifications.length) {
                return const Center(child: CircleLoadingAnimation());
              }

              final notification = controller.notifications[index];
              return _buildNotificationItem(context, notification);
            },
          ),
        );
      }),
      // bottomNavigationBar: const BottomNavBar(currentIndex: 3),
    );
  }

  Widget _buildNotificationItem(
    BuildContext context,
    NotificationModel notification,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final controller = Get.find<NotificationController>();

    return Obx(() {
      final isRead = notification.isRead.value;

      return Dismissible(
        key: Key('notification_${notification.id}'),
        background: Container(
          color: Colors.red,
          alignment: Alignment.centerRight,
          padding: const EdgeInsets.only(right: 16),
          child: const Icon(Icons.delete, color: Colors.white),
        ),
        direction: DismissDirection.endToStart,
        onDismissed: (_) => controller.deleteNotification(notification),
        child: InkWell(
          onTap: () {
            controller.markAsRead(notification);
            _navigateToNotificationDetail(notification, context);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
            decoration: BoxDecoration(
              color:
                  isRead ? null : colorScheme.primaryContainer.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (!isRead)
                  Container(
                    width: 12,
                    height: 12,
                    margin: const EdgeInsets.only(top: 4, right: 8),
                    decoration: BoxDecoration(
                      color: colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                if (notification.imageUrl != null)
                  Container(
                    width: 50,
                    height: 50,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: NetworkImage(notification.imageUrl!),
                        fit: BoxFit.cover,
                      ),
                    ),
                  )
                else
                  Container(
                    width: 50,
                    height: 50,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color: colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getIconForType(notification.type),
                      color: colorScheme.onPrimaryContainer,
                    ),
                  ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        notification.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight:
                              isRead ? FontWeight.normal : FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        notification.message,
                        style: theme.textTheme.bodyMedium,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        timeago.format(notification.createdAt),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  IconData _getIconForType(String type) {
    switch (type) {
      case 'event':
        return IconlyLight.calendar;
      case 'sermon':
        return IconlyLight.play;
      case 'announcement':
        return IconlyLight.document;
      case 'message':
        return IconlyLight.message;
      default:
        return IconlyLight.notification;
    }
  }

  void _navigateToNotificationDetail(
    NotificationModel notification,
    BuildContext context,
  ) {
    // Handle navigation based on notification type
    switch (notification.type) {
      case 'event':
        if (notification.data != null &&
            notification.data!.containsKey('event_id')) {
          context.go(
            Routes.EVENT_DETAIL,
            extra: notification.data!['event_id'],
          );
        }
        break;
      case 'sermon':
        if (notification.data != null &&
            notification.data!.containsKey('sermon_id')) {
          context.go(
            Routes.SERMON_DETAIL,
            extra: notification.data!['sermon_id'],
          );
        }
        break;
      default:
        context.go(Routes.NOTIFICATION_DETAIL, extra: notification);
        break;
    }
  }

  void _showSearchDialog(BuildContext context) {
    final controller = Get.find<NotificationController>();
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Search Notifications'),
            content: CustomTextField(
              controller: controller.searchController,
              hintText: 'Enter search term',
              prefixIcon: Icon(IconlyLight.search),
              onSubmitted: (value) {
                controller.setSearchQuery(value);
                Navigator.pop(context);
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  controller.setSearchQuery(controller.searchController.text);
                  Navigator.pop(context);
                },
                child: const Text('Search'),
              ),
            ],
          ),
    );
  }
}
