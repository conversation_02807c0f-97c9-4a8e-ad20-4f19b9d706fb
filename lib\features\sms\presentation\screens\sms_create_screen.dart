import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/features/sms/presentation/widgets/phone_row_widget.dart';
import '../../controllers/sms_controller.dart';
import '../widgets/recipient_chip.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';

class SmsCreateScreen extends StatelessWidget {
  const SmsCreateScreen({super.key});

  Future<bool?> _showPreviewDialog(
    BuildContext context,
    Map<String, dynamic> result,
  ) {
    return showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('SMS Preview'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Message Details',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildDetailRow(
                            'Price per SMS:',
                            'Ksh ${result['data']['price_per_unit']}',
                          ),
                          const Divider(),
                          _buildDetailRow(
                            'Total Characters:',
                            '${result['data']['total_characters']}',
                          ),
                          const Divider(),
                          _buildDetailRow(
                            'Total Recipients:',
                            '${result['data']['total_recipients']}',
                          ),
                          const Divider(),
                          _buildDetailRow(
                            'Number of SMS:',
                            '${result['data']['total_sms']}',
                          ),
                          const Divider(),
                          _buildDetailRow(
                            'Total Cost:',
                            'Ksh ${result['data']['total_charges']}',
                            valueStyle: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Message Preview:',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Text('${result['data']['message']}'),
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              CustomButton(
                onPressed: () => Navigator.pop(context, true),
                label: const Text('Send'),
              ),
            ],
          ),
    );
  }

  Widget _buildDetailRow(String label, String value, {TextStyle? valueStyle}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [Text(label), Text(value, style: valueStyle)],
    );
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SmsController>();

    return Scaffold(
      appBar: AppBar(title: const Text('Create SMS')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Message input
            CustomTextField(
              controller: controller.messageController,
              labelText: 'Message',
              hintText: 'Enter your message here...',

              maxLines: 5,
            ),
            const SizedBox(height: 16),
            // Character count
            Text(
              '${controller.messageController.text.length} characters',
              style: TextStyle(
                color:
                    controller.messageController.text.length > 160
                        ? Colors.red
                        : Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            // Recipients section
            const Text(
              'Recipients',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            // Add recipient input with integrated paste functionality
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: controller.recipientController,
                    labelText: 'Phone Number',
                    hintText: 'e.g. +254712345678',
                    prefixIcon: const Icon(Icons.phone),
                    suffixIcon: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: CustomButton(
                        onPressed: () {
                          if (controller.recipientController.text.isNotEmpty) {
                            controller.addRecipient(
                              controller.recipientController.text,
                            );
                          }
                        },
                        icon: Icon(Icons.add),
                        label: Text('Add'),
                      ),
                    ),
                    keyboardType: TextInputType.phone,
                  ),
                ),
                PhoneRowWidget(),
              ],
            ),

            // Group selection moved to PhoneRowWidget
            const SizedBox(height: 16),
            // Recipients list
            Obx(
              () => Wrap(
                spacing: 8.0,
                runSpacing: 4.0,
                children:
                    controller.recipients
                        .map(
                          (phone) => RecipientChip(
                            phone: phone,
                            onDeleted: () => controller.removeRecipient(phone),
                          ),
                        )
                        .toList(),
              ),
            ),
            const Spacer(),
            // Send buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: Obx(
                    () => CustomButton(
                      onPressed: () async {
                        if (controller.isSending.value) return;
                        final result = await controller.sendSmsPreview(
                          controller.messageController.text,
                          controller.recipients,
                        );
                        if (result['status'] == true) {
                          final shouldSend = await _showPreviewDialog(
                            context,
                            result,
                          );
                          if (shouldSend == true) {
                            final sendResult = await controller.sendSms(
                              controller.messageController.text,
                              controller.recipients,
                            );
                            if (sendResult['status'] == true) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('SMS sent successfully'),
                                ),
                              );
                              ToastUtils.showSuccessToast(
                                "Message sent successfully",
                                "Success",
                              );
                              Navigator.pop(context);
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    sendResult['message'] ??
                                        'Failed to send SMS',
                                  ),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          }
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                result['message'] ??
                                    'Failed to send SMS preview',
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      },
                      isLoading: controller.isSending.value,
                     label:
                           const Text('Send Preview'),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
