import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart' show Get, Inst;
import 'package:logger/logger.dart';
import '../../../core/app/services/http_service.dart';
import '../../../core/app/services/api_urls.dart';
import '../models/media_model.dart';
import '../../../features/auth/controllers/auth_controller.dart';

class MediaService {
  final HttpService _httpService = Get.find();
  final logger = Get.find<Logger>();
  late final Dio _dio;

  // Constructor to initialize Dio with HTTP service configuration
  MediaService() {
    _dio = _httpService.initializeDio();
  }

  // Upload media file
  Future<MediaModel> uploadMedia({
    File? file,
    Uint8List? bytes,
    required String title,
    String? type,
    String? category,
    String? fileName,
    String? organisationId,
    int? sizeBytes,
  }) async {
    try {
      // Validate that at least one file source is provided
      if (file == null && bytes == null) {
        throw Exception('Either file or bytes must be provided for upload');
      }

      // Create form data
      // Enforce the required filename format: /<organisation_id>/<CATEGORY>/<type>/filename.ext
      String safeFileName;
      if (fileName != null &&
          fileName.startsWith('/') &&
          fileName.split('/').length >= 4) {
        // Use the formatted filename directly as provided by the controller
        // But ensure no whitespace in the filename part
        final parts = fileName.split('/');
        final cleanedFileName = parts.last
            .replaceAll(' ', '')
            .replaceAll(RegExp(r'\s+'), '');
        parts[parts.length - 1] = cleanedFileName;
        safeFileName = parts.join('/');
        logger.d('Using provided formatted filename: $fileName');
        logger.d('Cleaned formatted filename: $safeFileName');
      } else {
        // Create the proper filename format if not provided correctly
        String rawFileName;
        if (file != null) {
          rawFileName = file.path
              .split('/')
              .last
              .replaceAll(' ', '')
              .replaceAll(RegExp(r'\s+'), '');
        } else {
          rawFileName = 'file_${DateTime.now().millisecondsSinceEpoch}';
        }

        // Ensure we have organization ID for the path
        final orgId = organisationId ?? 'unknown';
        final cat = category ?? 'GENERAL';
        final typ = type ?? 'image';

        safeFileName = '/$orgId/$cat/$typ/$rawFileName';
        logger.d('Generated formatted filename: $safeFileName');
      }

      logger.d('Final safe filename for upload: $safeFileName');

      // Get organisation ID from auth controller if not provided
      if (organisationId == null) {
        try {
          final authController = Get.find<AuthController>();
          organisationId = authController.currentOrg.value?.id;
          logger.d(
            'Using organisation ID from auth controller: $organisationId',
          );
        } catch (e) {
          logger.e('Error getting organisation ID from auth controller: $e');
        }
      }

      // Calculate file size if not provided
      if (sizeBytes == null) {
        if (file != null) {
          sizeBytes = await file.length();
        } else if (bytes != null) {
          sizeBytes = bytes.length;
        }
      }

      final formData = FormData.fromMap({
        'title': title,
        'type': type ?? 'image',
        'category': category ?? 'GENERAL',
        'organisation_id': organisationId,
        'size_bytes': sizeBytes,

        'file':
            file != null
                ? await MultipartFile.fromFile(
                  file.path,
                  filename: safeFileName,
                )
                : bytes != null
                ? MultipartFile.fromBytes(bytes, filename: safeFileName)
                : null,
      });

      // Log MediaService specific information with debugPrint for visibility
      debugPrint('=== MediaService.uploadMedia START ===');
      debugPrint('Final safe filename for upload: $safeFileName');
      debugPrint('Upload parameters prepared:');
      debugPrint('  - title: $title');
      debugPrint('  - type: ${type ?? "image"}');
      debugPrint('  - category: ${category ?? "GENERAL"}');
      debugPrint('  - organisation_id: $organisationId');
      debugPrint('  - size_bytes: $sizeBytes');
      if (file != null) {
        debugPrint('  - file path: ${file.path}');
        debugPrint('  - file exists: ${file.existsSync()}');
      } else if (bytes != null) {
        debugPrint('  - bytes length: ${bytes.length}');
      }

      // Use Dio directly for file uploads to avoid JSON encoding issues with FormData
      final response = await _dio.post(
        ApiUrls.baseUrl + ApiUrls.uploadFile,
        data: formData,
        options: Options(headers: await _httpService.header()),
      );

      // MediaService specific response handling (HTTP service logs the raw response)
      if (response.statusCode == 200 || response.statusCode == 201) {
        logger.d('=== MediaService.uploadMedia SUCCESS ===');
        final mediaModel = MediaModel.fromJson(response.data);
        logger.d('Created MediaModel ID: ${mediaModel.id}');
        logger.d('Media URL: ${mediaModel.mediaUrl}');
        logger.d('Media upload successful');
        return mediaModel;
      } else {
        logger.e('=== MediaService.uploadMedia FAILED ===');
        logger.e('Upload failed with status: ${response.statusCode}');
        throw Exception('Upload failed with status: ${response.statusCode}');
      }
    } catch (e) {
      if (e is DioException) {
        // Extract and log detailed error information from the DioException
        final DioException dioError = e;
        logger.e('Error uploading media: ${dioError.message}');

        // Log response data if available to see server error details
        if (dioError.response != null) {
          logger.e('Server response: ${dioError.response?.data}');
          logger.e('Status code: ${dioError.response?.statusCode}');
        }

        // Log request data to help debug what was sent
        logger.d('Request data: ${dioError.requestOptions.data}');
      } else {
        logger.e('Error uploading media: $e');
      }
      rethrow;
    }
  }

  // Get media with pagination and filters
  Future<Map<String, dynamic>> getMedia({
    required int page,
    required int size,
    String? type,
    String? category,
    String? title,
  }) async {
    try {
      // Build URL with query parameters
      String url = "${ApiUrls.getMedia}?";

      url += "page=$page";
      url += "&size=$size";

      // Add optional filters to URL
      if (type != null && type.isNotEmpty) {
        url += "&type=${Uri.encodeComponent(type)}";
      }

      if (category != null && category.isNotEmpty) {
        url += "&category=${Uri.encodeComponent(category)}";
      }

      if (title != null && title.isNotEmpty) {
        url += "&title=${Uri.encodeComponent(title)}";
      }

      // Use Dio directly to avoid potential FormData issues
      final response = await _dio.get(
        ApiUrls.baseUrl + url,
        options: Options(headers: await _httpService.header()),
      );

      return response.data;
    } catch (e) {
      logger.e('Error fetching media: $e');
      rethrow;
    }
  }

  // Delete media by ID
  Future<Map<String, dynamic>> deleteMedia(String mediaId) async {
    try {
      // Use Dio directly to avoid potential FormData issues
      final response = await _dio.delete(
        "${ApiUrls.baseUrl}${ApiUrls.deleteMedia}$mediaId/",
        options: Options(headers: await _httpService.header()),
      );

      return response.data;
    } catch (e) {
      logger.e('Error deleting media: $e');
      rethrow;
    }
  }

  // Parse media items from API response
  List<MediaModel> parseMedia(List<dynamic> items) {
    if (items.isEmpty) return [];
    return items.map((item) => MediaModel.fromJson(item)).toList();
  }

  // Upload media from web
  Future<MediaModel> uploadWebMedia({
    required Uint8List bytes,
    required String fileName,
    required String title,
    String? type,
    String? category,
    String? organisationId,
    int? sizeBytes,
  }) async {
    try {
      logger.d('Starting web media upload for file: $fileName');

      // Ensure the filename has a valid extension and remove whitespace
      String processedFileName = fileName
          .replaceAll(' ', '')
          .replaceAll(RegExp(r'\s+'), '');
      if (!processedFileName.contains('.')) {
        // If no extension, try to infer from type or default to .png
        String extension = type == 'image' ? '.png' : '.file';
        processedFileName = '$processedFileName$extension';
        logger.d('Added extension to filename: $processedFileName');
      }
      logger.d('Original filename: $fileName');
      logger.d('Processed filename (whitespace removed): $processedFileName');

      // Get organisation ID from auth controller if not provided
      if (organisationId == null) {
        try {
          final authController = Get.find<AuthController>();
          organisationId = authController.currentOrg.value?.id;
          logger.d(
            'Using organisation ID from auth controller: $organisationId',
          );
        } catch (e) {
          logger.e('Error getting organisation ID from auth controller: $e');
        }
      }

      // Set file size if not provided
      sizeBytes ??= bytes.length;

      // Enforce the required filename format: /<organisation_id>/<CATEGORY>/<type>/filename.ext
      String fileNameToUse;
      if (fileName.startsWith('/') && fileName.split('/').length >= 4) {
        // This is already a formatted path, use it directly
        fileNameToUse = fileName;
        logger.d('Using pre-formatted filename path: $fileNameToUse');
      } else {
        // This should not happen if the controller is working correctly
        // But we'll handle it gracefully by creating the proper format
        logger.w('Filename not in expected format, creating formatted path');
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final lastDotIndex = processedFileName.lastIndexOf('.');
        final nameWithoutExt =
            lastDotIndex > 0
                ? processedFileName.substring(0, lastDotIndex)
                : processedFileName;
        final extension =
            lastDotIndex > 0 ? processedFileName.substring(lastDotIndex) : '';
        final uniqueFileName = '${nameWithoutExt}_$timestamp$extension';
        fileNameToUse =
            '/$organisationId/${category ?? "GENERAL"}/${type ?? "image"}/$uniqueFileName';
        logger.d('Generated formatted filename: $fileNameToUse');
      }

      return await uploadMedia(
        bytes: bytes,
        fileName: fileNameToUse,
        title: title,
        type: type ?? 'image',
        category: category ?? 'GENERAL',
        organisationId: organisationId,
        sizeBytes: sizeBytes,
      );
    } catch (e) {
      logger.e('Error in web media upload: $e');
      rethrow;
    }
  }
}
