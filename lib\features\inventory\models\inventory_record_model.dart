import '../../media_upload/models/media_model.dart';
import '../../../data/models/member_model.dart';
import 'inventory_item_model.dart';

class InventoryRecordModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final String? memberId;
  final String? organisationId;
  final String? inventoryItemId;
  final int? quantity;
  final String? condition;
  final String? inventoryType;
  final DateTime? receivedAt;
  final DateTime? expiryDate;
  final String? batchNo;
  final double? estimatedValue;
  final DateTime? purchaseDate;
  final double? cost;
  final DateTime? warrantyExpiry;
  final String? notes;
  final List<MediaModel>? media;
  final bool? isAnonymous;
  final String? fullNames;
  final String? email;
  final String? phoneNumber;
  final String? county;
  final String? city;
  final String? address;

  // Related objects
  final MemberModel? member;
  final InventoryItemModel? inventoryItem;

  InventoryRecordModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.memberId,
    this.organisationId,
    this.inventoryItemId,
    this.quantity,
    this.condition,
    this.inventoryType,
    this.receivedAt,
    this.expiryDate,
    this.batchNo,
    this.estimatedValue,
    this.purchaseDate,
    this.cost,
    this.warrantyExpiry,
    this.notes,
    this.media,
    this.isAnonymous,
    this.fullNames,
    this.email,
    this.phoneNumber,
    this.county,
    this.city,
    this.address,
    this.member,
    this.inventoryItem,
  });

  InventoryRecordModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    String? memberId,
    String? organisationId,
    String? inventoryItemId,
    int? quantity,
    String? condition,
    String? inventoryType,
    DateTime? receivedAt,
    DateTime? expiryDate,
    String? batchNo,
    double? estimatedValue,
    DateTime? purchaseDate,
    double? cost,
    DateTime? warrantyExpiry,
    String? notes,
    List<MediaModel>? media,
    bool? isAnonymous,
    String? fullNames,
    String? email,
    String? phoneNumber,
    String? county,
    String? city,
    String? address,
    MemberModel? member,
    InventoryItemModel? inventoryItem,
  }) => InventoryRecordModel(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    memberId: memberId ?? this.memberId,
    organisationId: organisationId ?? this.organisationId,
    inventoryItemId: inventoryItemId ?? this.inventoryItemId,
    quantity: quantity ?? this.quantity,
    condition: condition ?? this.condition,
    inventoryType: inventoryType ?? this.inventoryType,
    receivedAt: receivedAt ?? this.receivedAt,
    expiryDate: expiryDate ?? this.expiryDate,
    batchNo: batchNo ?? this.batchNo,
    estimatedValue: estimatedValue ?? this.estimatedValue,
    purchaseDate: purchaseDate ?? this.purchaseDate,
    cost: cost ?? this.cost,
    warrantyExpiry: warrantyExpiry ?? this.warrantyExpiry,
    notes: notes ?? this.notes,
    media: media ?? this.media,
    isAnonymous: isAnonymous ?? this.isAnonymous,
    fullNames: fullNames ?? this.fullNames,
    email: email ?? this.email,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    county: county ?? this.county,
    city: city ?? this.city,
    address: address ?? this.address,
    member: member ?? this.member,
    inventoryItem: inventoryItem ?? this.inventoryItem,
  );

  factory InventoryRecordModel.fromJson(
    Map<String, dynamic> json,
  ) => InventoryRecordModel(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt:
        json["deleted_at"] == null ? null : DateTime.parse(json["deleted_at"]),
    memberId: json["member_id"],
    organisationId: json["organisation_id"],
    inventoryItemId: json["inventory_item_id"],
    quantity: json["quantity"],
    condition: json["condition"],
    inventoryType: json["inventory_type"],
    receivedAt:
        json["received_at"] == null
            ? null
            : DateTime.parse(json["received_at"]),
    expiryDate:
        json["expiry_date"] == null
            ? null
            : DateTime.parse(json["expiry_date"]),
    batchNo: json["batch_no"],
    estimatedValue: json["estimated_value"]?.toDouble(),
    purchaseDate:
        json["purchase_date"] == null
            ? null
            : DateTime.parse(json["purchase_date"]),
    cost: json["cost"]?.toDouble(),
    warrantyExpiry:
        json["warranty_expiry"] == null
            ? null
            : DateTime.parse(json["warranty_expiry"]),
    notes: json["notes"],
    media:
        json["media"] == null
            ? []
            : List<MediaModel>.from(
              json["media"]!.map((x) => MediaModel.fromJson(x)),
            ),
    isAnonymous: json["is_anonymous"],
    fullNames: json["full_names"],
    email: json["email"],
    phoneNumber: json["phone_number"],
    county: json["county"],
    city: json["city"],
    address: json["address"],
    member:
        json["member"] == null ? null : MemberModel.fromJson(json["member"]),
    inventoryItem:
        json["inventory_item"] == null
            ? null
            : InventoryItemModel.fromJson(json["inventory_item"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt?.toIso8601String(),
    "member_id": memberId,
    "organisation_id": organisationId,
    "inventory_item_id": inventoryItemId,
    "quantity": quantity,
    "condition": condition,
    "inventory_type": inventoryType,
    "received_at": receivedAt?.toIso8601String(),
    "expiry_date": expiryDate?.toIso8601String(),
    "batch_no": batchNo,
    "estimated_value": estimatedValue,
    "purchase_date": purchaseDate?.toIso8601String(),
    "cost": cost,
    "warranty_expiry": warrantyExpiry?.toIso8601String(),
    "notes": notes,
    "media":
        media == null ? [] : List<dynamic>.from(media!.map((x) => x.toJson())),
    "is_anonymous": isAnonymous,
    "full_names": fullNames,
    "email": email,
    "phone_number": phoneNumber,
    "county": county,
    "city": city,
    "address": address,
  };

  // Helper methods
  String get displayName =>
      isAnonymous == true
          ? fullNames ?? 'Anonymous'
          : member != null
          ? '${member!.firstName ?? ''} ${member!.secondName ?? ''}'.trim()
          : fullNames ?? 'Unknown';

  String get itemName => inventoryItem?.title ?? 'Unknown Item';

  bool get hasMedia => media != null && media!.isNotEmpty;

  String? get firstMediaUrl => hasMedia ? media!.first.mediaUrl : null;

  bool get isExpired =>
      expiryDate != null && expiryDate!.isBefore(DateTime.now());

  bool get isWarrantyExpired =>
      warrantyExpiry != null && warrantyExpiry!.isBefore(DateTime.now());
}
