import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/core/app/widgets/custom_textformfield.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import '../../controllers/inventory_controller.dart';
import '../../controllers/inventory_item_controller.dart';

class InventoryFilterWidget extends StatelessWidget {
  final bool isItemsView;

  const InventoryFilterWidget({super.key, this.isItemsView = true});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const Divider(height: 24),
            _buildFilterControls(context),
            const SizedBox(height: 16),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Row(
      children: [
        Icon(IconlyLight.filter, color: colorScheme.primary),
        const SizedBox(width: 8),
        Text(
          'Filter ${isItemsView ? 'Items' : 'Records'}',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const Spacer(),
        _buildClearButton(context),
      ],
    );
  }

  Widget _buildClearButton(BuildContext context) {
    return TextButton(
      onPressed: () {
        if (isItemsView) {
          Get.find<InventoryItemController>().clearFilters();
        } else {
          Get.find<InventoryController>().clearFilters();
        }
      },
      style: TextButton.styleFrom(
        foregroundColor: Colors.red,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: Row(
        children: [
          const Icon(Icons.clear_all, size: 18),
          const SizedBox(width: 4),
          Text('Clear All', style: Theme.of(context).textTheme.labelMedium),
        ],
      ),
    );
  }

  Widget _buildFilterControls(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    return Wrap(
      runSpacing: 12,
      spacing: 12,
      children: [
        if (isMobile)
          SizedBox(
            width: 280.w,
            child: buildTextFilter(
              context,
              label: 'Search ${isItemsView ? 'Items' : 'Records'}',
              icon: IconlyLight.search,
              onChanged: (value) {
                if (isItemsView) {
                  Get.find<InventoryItemController>().setSearchQuery(value);
                } else {
                  Get.find<InventoryController>().setSearchQuery(value);
                }
              },
              initialValue:
                  isItemsView
                      ? Get.find<InventoryItemController>().searchQuery.value
                      : Get.find<InventoryController>().searchQuery.value,
            ),
          ),

        // Category/Type filter
        if (isItemsView)
          SizedBox(width: 150.w, child: _buildCategoryFilter(context))
        else ...[
          SizedBox(width: 150.w, child: _buildInventoryTypeFilter(context)),
          SizedBox(width: 120.w, child: _buildMemberFilter(context)),
        ],

        // Date filters
        SizedBox(
          width: 130.w,
          child: _buildDateField(context, isStartDate: true),
        ),

        SizedBox(
          width: 130.w,
          child: _buildDateField(context, isStartDate: false),
        ),
      ],
    );
  }

  Widget buildTextFilter(
    BuildContext context, {
    required String label,
    required IconData icon,
    required Function(String) onChanged,
    required String initialValue,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textController = TextEditingController(text: initialValue);

    return CustomTextField(
      controller: textController,
      labelText: label,
      prefixIcon: Icon(icon, color: colorScheme.primary),
      suffixIcon:
          initialValue.isNotEmpty
              ? _buildClearIcon(() {
                textController.clear();
                onChanged('');
              })
              : null,
      onChanged: onChanged,
    );
  }

  Widget _buildCategoryFilter(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Obx(
      () => DropdownButtonFormField<String>(
        decoration: InputDecoration(
          labelText: 'Category',
          prefixIcon: Icon(IconlyLight.category, color: colorScheme.primary),
          border: _inputBorder(),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 14,
            horizontal: 16,
          ),
        ),
        value:
            Get.find<InventoryItemController>().categoryFilter.value.isEmpty
                ? 'all'
                : Get.find<InventoryItemController>().categoryFilter.value,
        items: [
          const DropdownMenuItem(value: 'all', child: Text('All Categories')),
          ...Get.find<InventoryItemController>().categoryOptions.map(
            (category) =>
                DropdownMenuItem(value: category, child: Text(category)),
          ),
        ],
        onChanged: (value) {
          if (value != null) {
            if (value == 'all') {
              Get.find<InventoryItemController>().setCategoryFilter('');
            } else {
              Get.find<InventoryItemController>().setCategoryFilter(value);
            }
            Get.find<InventoryItemController>().fetchInventoryItems();
          }
        },
        icon: Icon(Icons.arrow_drop_down, color: colorScheme.primary),
      ),
    );
  }

  Widget _buildInventoryTypeFilter(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Obx(
      () => DropdownButtonFormField<String>(
        decoration: InputDecoration(
          labelText: 'Type',
          prefixIcon: Icon(IconlyLight.swap, color: colorScheme.primary),
          border: _inputBorder(),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 14,
            horizontal: 16,
          ),
        ),
        value:
            Get.find<InventoryController>().inventoryTypeFilter.value.isEmpty
                ? 'all'
                : Get.find<InventoryController>().inventoryTypeFilter.value,
        items: [
          const DropdownMenuItem(value: 'all', child: Text('All Types')),
          const DropdownMenuItem(value: 'IN', child: Text('IN')),
          const DropdownMenuItem(value: 'OUT', child: Text('OUT')),
        ],
        onChanged: (value) {
          if (value != null) {
            if (value == 'all') {
              Get.find<InventoryController>().setInventoryTypeFilter('');
            } else {
              Get.find<InventoryController>().setInventoryTypeFilter(value);
            }
            Get.find<InventoryController>().fetchInventoryRecords();
          }
        },
        icon: Icon(Icons.arrow_drop_down, color: colorScheme.primary),
      ),
    );
  }

  Widget _buildMemberFilter(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return CustomTextField(
      controller: Get.find<InventoryController>().memberIdController,
      labelText: 'Member ID',
      prefixIcon: Icon(IconlyLight.profile, color: colorScheme.primary),
      onSubmitted: (value) {
        Get.find<InventoryController>().setMemberFilter(value);
        Get.find<InventoryController>().fetchInventoryRecords();
      },
    );
  }

  Widget _buildDateField(BuildContext context, {required bool isStartDate}) {
    final colorScheme = Theme.of(context).colorScheme;

    return Obx(() {
      final itemController = Get.find<InventoryItemController>();
      final recordController = Get.find<InventoryController>();

      final date =
          isStartDate
              ? (isItemsView
                  ? itemController.startDate.value
                  : recordController.startDate.value)
              : (isItemsView
                  ? itemController.endDate.value
                  : recordController.endDate.value);
      final label = isStartDate ? 'Start Date' : 'End Date';

      return CustomTextFormField(
        readOnly: true,
        labelText: label,
        prefixIcon: Icon(IconlyLight.calendar, color: colorScheme.primary),
        suffixIcon:
            date != null
                ? _buildClearIcon(() {
                  if (isItemsView) {
                    isStartDate
                        ? itemController.setDateRange(
                          null,
                          itemController.endDate.value,
                        )
                        : itemController.setDateRange(
                          itemController.startDate.value,
                          null,
                        );
                  } else {
                    isStartDate
                        ? recordController.setDateRange(
                          null,
                          recordController.endDate.value,
                        )
                        : recordController.setDateRange(
                          recordController.startDate.value,
                          null,
                        );
                  }
                })
                : null,
        hintText: 'Select $label',
        controller: TextEditingController(
          text: date != null ? DateFormat('MMM dd, yyyy').format(date) : null,
        ),
        onTap:
            () =>
                isStartDate
                    ? _selectStartDate(context)
                    : _selectEndDate(context),
      );
    });
  }

  Widget _buildActionButtons(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        onPressed: () {
          if (isItemsView) {
            Get.find<InventoryItemController>().fetchInventoryItems();
          } else {
            Get.find<InventoryController>().fetchInventoryRecords();
          }
        },
        icon: const Icon(IconlyLight.filter),
        label: const Text('Apply Filters'),
      ),
    );
  }

  Widget _buildClearIcon(VoidCallback onPressed, {bool showClear = true}) {
    if (!showClear) return const SizedBox.shrink();

    return IconButton(
      icon: const Icon(Icons.clear, size: 18),
      onPressed: onPressed,
      splashRadius: 20,
    );
  }

  InputBorder _inputBorder() {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: Colors.grey),
    );
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final itemController = Get.find<InventoryItemController>();
    final recordController = Get.find<InventoryController>();

    final currentDate =
        isItemsView
            ? itemController.startDate.value
            : recordController.startDate.value;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: currentDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      if (isItemsView) {
        itemController.setDateRange(picked, itemController.endDate.value);
      } else {
        recordController.setDateRange(picked, recordController.endDate.value);
      }
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final itemController = Get.find<InventoryItemController>();
    final recordController = Get.find<InventoryController>();

    final currentDate =
        isItemsView
            ? itemController.endDate.value
            : recordController.endDate.value;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: currentDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      // Set to end of day for inclusive filtering
      final endOfDay = DateTime(
        picked.year,
        picked.month,
        picked.day,
        23,
        59,
        59,
      );
      if (isItemsView) {
        itemController.setDateRange(itemController.startDate.value, endOfDay);
      } else {
        recordController.setDateRange(
          recordController.startDate.value,
          endOfDay,
        );
      }
    }
  }
}
