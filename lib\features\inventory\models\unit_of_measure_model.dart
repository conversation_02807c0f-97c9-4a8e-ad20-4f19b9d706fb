import 'package:get/get.dart';
import '../../../core/app/constants/enums.dart';
import '../../../core/app/utils/show_toast.dart';

class UnitOfMeasureModel {
  final String? id;
  final String? name;
  final String? abbreviation;
  final String? description;
  final String? category;
  final bool? isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  UnitOfMeasureModel({
    this.id,
    this.name,
    this.abbreviation,
    this.description,
    this.category,
    this.isActive,
    this.createdAt,
    this.updatedAt,
  });

  UnitOfMeasureModel copyWith({
    String? id,
    String? name,
    String? abbreviation,
    String? description,
    String? category,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) => UnitOfMeasureModel(
    id: id ?? this.id,
    name: name ?? this.name,
    abbreviation: abbreviation ?? this.abbreviation,
    description: description ?? this.description,
    category: category ?? this.category,
    isActive: isActive ?? this.isActive,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
  );

  factory UnitOfMeasureModel.fromJson(
    Map<String, dynamic> json,
  ) => UnitOfMeasureModel(
    id: json["id"],
    name: json["name"],
    abbreviation: json["abbreviation"],
    description: json["description"],
    category: json["category"],
    isActive: json["is_active"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "abbreviation": abbreviation,
    "description": description,
    "category": category,
    "is_active": isActive,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
  };

  // Helper methods
  String get displayName => name ?? 'Unknown Unit';
  String get displayAbbreviation => abbreviation ?? '';
  String get fullDisplay =>
      abbreviation != null ? '$name ($abbreviation)' : name ?? 'Unknown Unit';
}

// Common units of measure for inventory
class CommonUnitsOfMeasure {
  /// Get units of measure from EnumDataService
  static List<UnitOfMeasureModel> getUnitsFromEnums() {
    try {
      final enumService = Get.find<EnumDataService>();
      if (enumService.unitsOfMeasure.isNotEmpty) {
        return enumService.unitsOfMeasure
            .map(
              (unit) => UnitOfMeasureModel(
                id: unit['id']?.toString(),
                name: unit['name']?.toString(),
                abbreviation: unit['abbreviation']?.toString(),
                description: unit['description']?.toString(),
                category: unit['category']?.toString(),
                isActive: unit['is_active'] ?? true,
                createdAt:
                    unit['created_at'] != null
                        ? DateTime.tryParse(unit['created_at'].toString())
                        : null,
                updatedAt:
                    unit['updated_at'] != null
                        ? DateTime.tryParse(unit['updated_at'].toString())
                        : null,
              ),
            )
            .toList();
      }
    } catch (e) {
      ToastUtils.showErrorToast('Failed to load units of measure: $e', null);
    }
    return <UnitOfMeasureModel>[];
  }
}
