import 'package:get/get.dart';

class NotificationModel {
  final int id;
  final String title;
  final String message;
  final String type; // e.g., 'event', 'sermon', 'general', etc.
  final DateTime createdAt;
  final DateTime? readAt;
  final String? imageUrl;
  final Map<String, dynamic>? data; // Additional data related to the notification
  final RxBool isRead;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.createdAt,
    this.readAt,
    this.imageUrl,
    this.data,
  }) : isRead = (readAt != null).obs;

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'],
      title: json['title'],
      message: json['message'],
      type: json['type'],
      createdAt: DateTime.parse(json['created_at']),
      readAt: json['read_at'] != null ? DateTime.parse(json['read_at']) : null,
      imageUrl: json['image_url'],
      data: json['data'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type,
      'created_at': createdAt.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
      'image_url': imageUrl,
      'data': data,
    };
  }

  NotificationModel copyWith({
    int? id,
    String? title,
    String? message,
    String? type,
    DateTime? createdAt,
    DateTime? readAt,
    String? imageUrl,
    Map<String, dynamic>? data,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      imageUrl: imageUrl ?? this.imageUrl,
      data: data ?? this.data,
    );
  }
}