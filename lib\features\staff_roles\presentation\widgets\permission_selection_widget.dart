import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/permission_model.dart';

class PermissionSelectionWidget extends StatelessWidget {
  final RxList<PermissionModel> permissions;
  final RxList<PermissionModel> selectedPermissions;
  final Function(PermissionModel) onToggle;

  const PermissionSelectionWidget({
    super.key,
    required this.permissions,
    required this.selectedPermissions,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(
          () =>
              permissions.isEmpty
                  ? const Center(
                    child: Text(
                      'No permissions available',
                      style: TextStyle(fontStyle: FontStyle.italic),
                    ),
                  )
                  : SizedBox(
                    height: 300,
                    child: ListView.builder(
                      itemCount: permissions.length,
                      itemBuilder: (context, index) {
                        final permission = permissions[index];
                        return Obx(
                          () => CheckboxListTile(
                            title: Text(permission.name ?? 'Unknown'),
                            subtitle: Text(permission.description ?? ''),
                            secondary: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Theme.of(
                                  context,
                                ).primaryColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                permission.code ?? '',
                                style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            value: selectedPermissions.contains(permission),
                            onChanged: (value) {
                              onToggle(permission);
                            },
                          ),
                        );
                      },
                    ),
                  ),
        ),
        const SizedBox(height: 16),
        Obx(
          () => Text(
            'Selected permissions: ${selectedPermissions.length}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
      ],
    );
  }
}
