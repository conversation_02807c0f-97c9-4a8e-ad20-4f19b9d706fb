// To parse this JSON data, do
//
//     final subAccount = subAccountFromJson(jsonString);

import 'dart:convert';

SubAccount subAccountFromJson(String str) =>
    SubAccount.fromJson(json.decode(str));

String subAccountToJson(SubAccount data) => json.encode(data.toJson());

class SubAccount {
  String? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? title;
  String? description;
  String? username;
  bool? isPinned;
  int? orderNumber;
  SubAccountCategory? category;
  String? categoryId;
  String? account;
  String? organisationId;
  dynamic organisation;
  String? status;
  int? createdByUserId;
  CreatedByUser? createdByUser;

  SubAccount({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.description,
    this.username,
    this.isPinned,
    this.orderNumber,
    this.category,
    this.categoryId,
    this.account,
    this.organisationId,
    this.organisation,
    this.status,
    this.createdByUserId,
    this.createdByUser,
  });

  SubAccount copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? title,
    String? description,
    String? username,
    bool? isPinned,
    int? orderNumber,
    SubAccountCategory? category,
    String? categoryId,
    String? account,
    String? organisationId,
    dynamic organisation,
    String? status,
    int? createdByUserId,
    CreatedByUser? createdByUser,
  }) => SubAccount(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    title: title ?? this.title,
    description: description ?? this.description,
    username: username ?? this.username,
    isPinned: isPinned ?? this.isPinned,
    orderNumber: orderNumber ?? this.orderNumber,
    category: category ?? this.category,
    categoryId: categoryId ?? this.categoryId,
    account: account ?? this.account,
    organisationId: organisationId ?? this.organisationId,
    organisation: organisation ?? this.organisation,
    status: status ?? this.status,
    createdByUserId: createdByUserId ?? this.createdByUserId,
    createdByUser: createdByUser ?? this.createdByUser,
  );

  factory SubAccount.fromJson(Map<String, dynamic> json) => SubAccount(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    title: json["title"],
    description: json["description"],
    username: json["username"],
    isPinned: json["is_pinned"],
    orderNumber: json["order_number"],
    category:
        json["category"] == null
            ? null
            : SubAccountCategory.fromJson(json["category"]),
    categoryId: json["category_id"],
    account: json["account"],
    organisationId: json["organisation_id"],
    organisation: json["organisation"],
    status: json["status"],
    createdByUserId: json["created_by_user_id"],
    createdByUser:
        json["created_by_user"] == null
            ? null
            : CreatedByUser.fromJson(json["created_by_user"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "title": title,
    "description": description,
    "username": username,
    "is_pinned": isPinned,
    "order_number": orderNumber,
    "category": category?.toJson(),
    "category_id": categoryId,
    "account": account,
    "organisation_id": organisationId,
    "organisation": organisation,
    "status": status,
    "created_by_user_id": createdByUserId,
    "created_by_user": createdByUser?.toJson(),
  };
}

class SubAccountCategory {
  String? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? title;
  String? description;
  String? organisationId;
  dynamic organisation;
  bool? isGeneral;

  SubAccountCategory({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.description,
    this.organisationId,
    this.organisation,
    this.isGeneral,
  });

  SubAccountCategory copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? title,
    String? description,
    String? organisationId,
    dynamic organisation,
    bool? isGeneral,
  }) => SubAccountCategory(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    title: title ?? this.title,
    description: description ?? this.description,
    organisationId: organisationId ?? this.organisationId,
    organisation: organisation ?? this.organisation,
    isGeneral: isGeneral ?? this.isGeneral,
  );

  factory SubAccountCategory.fromJson(
    Map<String, dynamic> json,
  ) => SubAccountCategory(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    title: json["title"],
    description: json["description"],
    organisationId: json["organisation_id"],
    organisation: json["organisation"],
    isGeneral: json["is_general"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "title": title,
    "description": description,
    "organisation_id": organisationId,
    "organisation": organisation,
    "is_general": isGeneral,
  };
}

class CreatedByUser {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? phoneNumber;
  String? firstName;
  String? secondName;
  String? email;
  String? idNumber;
  int? balance;
  String? birthDate;
  String? countryCode;
  String? county;
  String? subCounty;
  String? ward;
  String? secondaryNumber;
  String? profileUrl;
  String? role;
  String? status;
  dynamic addresses;
  dynamic devices;

  CreatedByUser({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.phoneNumber,
    this.firstName,
    this.secondName,
    this.email,
    this.idNumber,
    this.balance,
    this.birthDate,
    this.countryCode,
    this.county,
    this.subCounty,
    this.ward,
    this.secondaryNumber,
    this.profileUrl,
    this.role,
    this.status,
    this.addresses,
    this.devices,
  });

  CreatedByUser copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? phoneNumber,
    String? firstName,
    String? secondName,
    String? email,
    String? idNumber,
    int? balance,
    String? birthDate,
    String? countryCode,
    String? county,
    String? subCounty,
    String? ward,
    String? secondaryNumber,
    String? profileUrl,
    String? role,
    String? status,
    dynamic addresses,
    dynamic devices,
  }) => CreatedByUser(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    firstName: firstName ?? this.firstName,
    secondName: secondName ?? this.secondName,
    email: email ?? this.email,
    idNumber: idNumber ?? this.idNumber,
    balance: balance ?? this.balance,
    birthDate: birthDate ?? this.birthDate,
    countryCode: countryCode ?? this.countryCode,
    county: county ?? this.county,
    subCounty: subCounty ?? this.subCounty,
    ward: ward ?? this.ward,
    secondaryNumber: secondaryNumber ?? this.secondaryNumber,
    profileUrl: profileUrl ?? this.profileUrl,
    role: role ?? this.role,
    status: status ?? this.status,
    addresses: addresses ?? this.addresses,
    devices: devices ?? this.devices,
  );

  factory CreatedByUser.fromJson(Map<String, dynamic> json) => CreatedByUser(
    id: json["ID"],
    createdAt:
        json["CreatedAt"] == null ? null : DateTime.parse(json["CreatedAt"]),
    updatedAt:
        json["UpdatedAt"] == null ? null : DateTime.parse(json["UpdatedAt"]),
    deletedAt: json["DeletedAt"],
    phoneNumber: json["phone_number"],
    firstName: json["first_name"],
    secondName: json["second_name"],
    email: json["email"],
    idNumber: json["id_number"],
    balance: json["balance"],
    birthDate: json["birth_date"],
    countryCode: json["country_code"],
    county: json["county"],
    subCounty: json["sub_county"],
    ward: json["ward"],
    secondaryNumber: json["secondary_number"],
    profileUrl: json["profile_url"],
    role: json["role"],
    status: json["status"],
    addresses: json["addresses"],
    devices: json["devices"],
  );

  Map<String, dynamic> toJson() => {
    "ID": id,
    "CreatedAt": createdAt?.toIso8601String(),
    "UpdatedAt": updatedAt?.toIso8601String(),
    "DeletedAt": deletedAt,
    "phone_number": phoneNumber,
    "first_name": firstName,
    "second_name": secondName,
    "email": email,
    "id_number": idNumber,
    "balance": balance,
    "birth_date": birthDate,
    "country_code": countryCode,
    "county": county,
    "sub_county": subCounty,
    "ward": ward,
    "secondary_number": secondaryNumber,
    "profile_url": profileUrl,
    "role": role,
    "status": status,
    "addresses": addresses,
    "devices": devices,
  };
}
