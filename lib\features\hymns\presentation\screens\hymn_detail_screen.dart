import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import '../../../../data/models/hymn_model.dart';
import '../../controllers/hymn_controller.dart';
import 'package:onechurch/core/app/widgets/circle_loading_animation.dart';
class HymnDetailScreen extends StatefulWidget {
  final String id;

  const HymnDetailScreen({super.key, required this.id});

  @override
  State<HymnDetailScreen> createState() => _HymnDetailScreenState();
}

class _HymnDetailScreenState extends State<HymnDetailScreen> {
  late HymnModel hymn;
  bool _isLoading = true;
  final HymnController controller = Get.find<HymnController>();

  @override
  void initState() {
    super.initState();
    // Get the hymn from arguments if available
    if (Get.arguments != null && Get.arguments is HymnModel) {
      hymn = Get.arguments;
      setState(() {
        _isLoading = false;
      });
    } else {
      loadHymn();
    }
  }

  Future<void> loadHymn() async {
    try {
      final hymnData = await controller.getHymnById(widget.id);
      if (hymnData != null) {
        setState(() {
          hymn = hymnData;
          _isLoading = false;
        });
      } else {
        ToastUtils.showErrorToast('Error', 'Failed to load hymn');
        Get.back();
      }
    } catch (e) {
      ToastUtils.showErrorToast('Error', 'Failed to load hymn');
      Get.back();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget _buildTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          hymn.hasTitle ? hymn.title! : 'Untitled Hymn',
          style: const TextStyle(fontSize: 24.0, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8.0),
        if (hymn.hasArtist)
          Text(
            'Artist: ${hymn.artist}',
            style: const TextStyle(fontSize: 16.0),
          ),
        if (hymn.hasAlbum)
          Text('Album: ${hymn.album}', style: const TextStyle(fontSize: 16.0)),
      ],
    );
  }

  Widget _buildLyricsSection() {
    if (hymn.lyrics.isEmpty) {
      return const Text('No lyrics available');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (hymn.lyrics.hasVerse) ...[
          const Text(
            'Verses',
            style: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8.0),
          ...hymn.lyrics.verse.map((verse) => _buildLyricItem(verse)),
          const SizedBox(height: 16.0),
        ],
        if (hymn.lyrics.hasChorus) ...[
          const Text(
            'Chorus',
            style: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8.0),
          ...hymn.lyrics.chorus.map(
            (chorus) => _buildLyricItem(chorus, isChorus: true),
          ),
        ],
      ],
    );
  }

  Widget _buildLyricItem(String text, {bool isChorus = false}) {
    if (text.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 16.0,
          fontStyle: isChorus ? FontStyle.italic : FontStyle.normal,
        ),
      ),
    );
  }

  Widget _buildCategoriesSection() {
    if (!hymn.hasCategories) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Categories',
          style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8.0),
        Wrap(
          spacing: 8.0,
          runSpacing: 8.0,
          children:
              hymn.categories
                  .where((category) => category.hasName)
                  .map(
                    (category) => Chip(
                      label: Text(category.name!),
                      backgroundColor: Theme.of(
                        context,
                      ).primaryColor.withOpacity(0.1),
                    ),
                  )
                  .toList(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Hymn Details')),
        body: const Center(child: CircleLoadingAnimation()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Hymn Details'),
        actions: [
          Obx(
            () => IconButton(
              icon: Icon(
                controller.isHymnBookmarked(hymn.id)
                    ? IconlyBold.bookmark
                    : IconlyLight.bookmark,
              ),
              onPressed: () async {
                final bookmarked = await controller.bookmarkHymn(hymn);
                ToastUtils.showInfoToast(
                  'Bookmark',
                  bookmarked ? 'Hymn bookmarked' : 'Bookmark removed',
                );
              },
            ),
          ),
          Obx(
            () => IconButton(
              icon: Icon(
                controller.isHymnDownloaded(hymn.id)
                    ? IconlyBold.download
                    : IconlyLight.download,
              ),
              onPressed: () async {
                final downloaded = await controller.downloadHymn(hymn);
                if (downloaded) {
                  ToastUtils.showSuccessToast(
                    'Download',
                    'Hymn downloaded successfully',
                  );
                }
              },
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTitleSection(),
            const SizedBox(height: 24.0),
            const Text(
              'Lyrics',
              style: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16.0),
            _buildLyricsSection(),
            const SizedBox(height: 24.0),
            _buildCategoriesSection(),
          ],
        ),
      ),
    );
  }
}
