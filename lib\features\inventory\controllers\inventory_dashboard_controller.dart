import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../../../core/app/constants/routes.dart';
import 'inventory_controller.dart';
import 'inventory_item_controller.dart';

class InventoryDashboardController extends GetxController
    with GetSingleTickerProviderStateMixin {
  // Tab controller for managing tabs
  late TabController tabController;

  // Reactive state variables
  final RxBool isItemsView = true.obs;
  final RxInt currentTabIndex = 0.obs;

  // Toggle states for UI sections
  final RxBool showDashboard = false.obs;
  final RxBool showQuickActions = false.obs;
  final RxBool showFilters = false.obs;

  // Dependencies
  late final InventoryController _inventoryController;
  late final InventoryItemController _inventoryItemController;

  @override
  void onInit() {
    super.onInit();
    _initializeTabController();
    _initializeControllers();
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  void _initializeTabController() {
    tabController = TabController(length: 2, vsync: this);
    tabController.addListener(_onTabChanged);
  }

  void _initializeControllers() {
    // Initialize controllers if not already registered
    if (!Get.isRegistered<InventoryItemController>()) {
      Get.put(InventoryItemController());
    }
    if (!Get.isRegistered<InventoryController>()) {
      Get.put(InventoryController());
    }

    _inventoryController = Get.find<InventoryController>();
    _inventoryItemController = Get.find<InventoryItemController>();
  }

  void _onTabChanged() {
    currentTabIndex.value = tabController.index;
    isItemsView.value = tabController.index == 0;
  }

  // Navigation methods
  void navigateToCreateItem(BuildContext context) {
    context.go(Routes.CREATE_INVENTORY_ITEM);
  }

  void navigateToRecordInventory(BuildContext context) {
    context.go(Routes.RECORD_INVENTORY);
  }

  void handleCreateAction(BuildContext context) {
    if (isItemsView.value) {
      navigateToCreateItem(context);
    } else {
      navigateToRecordInventory(context);
    }
  }

  // Toggle methods for UI sections
  void toggleDashboard() {
    showDashboard.value = !showDashboard.value;
  }

  void toggleQuickActions() {
    showQuickActions.value = !showQuickActions.value;
  }

  void toggleFilters() {
    showFilters.value = !showFilters.value;
  }

  // Toggle all sections at once
  void toggleAllSections() {
    final anyVisible =
        showDashboard.value || showQuickActions.value || showFilters.value;
    final newState = !anyVisible;

    showDashboard.value = newState;
    showQuickActions.value = newState;
    showFilters.value = newState;
  }

  // Check if any section is visible
  bool get hasVisibleSections =>
      showDashboard.value || showQuickActions.value || showFilters.value;

  // Getters for UI
  String get currentActionLabel => isItemsView.value ? 'Add Item' : 'Record';
  String get currentActionTooltip =>
      isItemsView.value ? 'Add New Item' : 'Record Inventory';
  IconData get currentActionIcon =>
      isItemsView.value ? Icons.add : Icons.edit_document;

  String get appBarTitle => 'Inventory Management';

  // Tab configuration
  List<Tab> get tabs => const [
    Tab(icon: Icon(Icons.inventory), text: 'Items'),
    Tab(icon: Icon(Icons.description), text: 'Records'),
  ];

  // Stats getters
  bool get hasControllers =>
      Get.isRegistered<InventoryItemController>() &&
      Get.isRegistered<InventoryController>();

  // Refresh data
  Future<void> refreshData() async {
    if (isItemsView.value) {
      await _inventoryItemController.fetchInventoryItems();
    } else {
      await _inventoryController.fetchInventoryRecords();
    }
  }

  // Search functionality
  void performSearch(String query) {
    if (isItemsView.value) {
      _inventoryItemController.setSearchQuery(query);
      _inventoryItemController.fetchInventoryItems();
    } else {
      _inventoryController.setSearchQuery(query);
      _inventoryController.fetchInventoryRecords();
    }
  }

  // Filter functionality
  void applyFilters() {
    if (isItemsView.value) {
      _inventoryItemController.fetchInventoryItems();
    } else {
      _inventoryController.fetchInventoryRecords();
    }
  }

  void clearFilters() {
    if (isItemsView.value) {
      _inventoryItemController.clearFilters();
    } else {
      _inventoryController.clearFilters();
    }
  }
}
