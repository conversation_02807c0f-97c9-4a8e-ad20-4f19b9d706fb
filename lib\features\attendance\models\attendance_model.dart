class AttendanceModel {
  final String? id;
  final String? createdAt;
  final String? updatedAt;
  final String? deletedAt;
  final String? organisationId;
  final int? createdByUserId;
  final String? eventId;
  final String? timeIn;
  final String? timeOut;
  final String? title;
  final String? description;
  final String? locationName;
  final double? latitude;
  final double? longitude;
  final bool? isVisitor;

  final String? attendeeName;
  final String? attendeeEmail;
  final String? attendeePhone;

  // Nested objects
  final Map<String, dynamic>? event;
  final Map<String, dynamic>? createdByUser;

  AttendanceModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.organisationId,
    this.createdByUserId,
    this.eventId,
    this.timeIn,
    this.timeOut,
    this.title,
    this.description,
    this.locationName,
    this.latitude,
    this.longitude,
    this.isVisitor,
    this.attendeeName,
    this.attendeeEmail,
    this.attendeePhone,
    this.event,
    this.createdByUser,
  });

  AttendanceModel copyWith({
    String? id,
    String? createdAt,
    String? updatedAt,
    String? deletedAt,
    String? organisationId,
    int? createdByUserId,
    String? eventId,
    String? timeIn,
    String? timeOut,
    String? title,
    String? description,
    String? locationName,
    double? latitude,
    double? longitude,
    bool? isVisitor,
    String? attendeeName,
    String? attendeeEmail,
    String? attendeePhone,
    Map<String, dynamic>? event,
    Map<String, dynamic>? createdByUser,
  }) {
    return AttendanceModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      organisationId: organisationId ?? this.organisationId,
      createdByUserId: createdByUserId ?? this.createdByUserId,
      eventId: eventId ?? this.eventId,
      timeIn: timeIn ?? this.timeIn,
      timeOut: timeOut ?? this.timeOut,
      title: title ?? this.title,
      description: description ?? this.description,
      locationName: locationName ?? this.locationName,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      isVisitor: isVisitor ?? this.isVisitor,
      attendeeName: attendeeName ?? this.attendeeName,
      attendeeEmail: attendeeEmail ?? this.attendeeEmail,
      attendeePhone: attendeePhone ?? this.attendeePhone,
      event: event ?? this.event,
      createdByUser: createdByUser ?? this.createdByUser,
    );
  }

  factory AttendanceModel.fromJson(Map<String, dynamic> json) {
    return AttendanceModel(
      id: json['id'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      deletedAt: json['deleted_at'],
      organisationId: json['organisation_id'],
      createdByUserId: json['created_by_user_id'],
      eventId: json['event_id'],
      timeIn: json['time_in'],
      timeOut: json['time_out'],
      title: json['title'],
      description: json['description'],
      locationName: json['location_name'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      isVisitor: json['is_visitor'],
      attendeeName: json['attendee_name'],
      attendeeEmail: json['attendee_email'],
      attendeePhone: json['attendee_phone'],
      event: json['event'] as Map<String, dynamic>?,
      createdByUser: json['created_by_user'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
      'organisation_id': organisationId,
      'created_by_user_id': createdByUserId,
      'event_id': eventId,
      'time_in': timeIn,
      'time_out': timeOut,
      'title': title,
      'description': description,
      'location_name': locationName,
      'latitude': latitude,
      'longitude': longitude,
      'is_visitor': isVisitor,
      'attendee_name': attendeeName,
      'attendee_email': attendeeEmail,
      'attendee_phone': attendeePhone,
      'event': event,
      'created_by_user': createdByUser,
    };
  }

  // Helper methods to get nested data
  String get eventTitle => event?['title'] ?? title ?? '';
  String get createdByUserName =>
      '${createdByUser?['first_name'] ?? ''} ${createdByUser?['second_name'] ?? ''}'
          .trim();
  String get createdByUserPhone => createdByUser?['phone_number'] ?? '';
}
