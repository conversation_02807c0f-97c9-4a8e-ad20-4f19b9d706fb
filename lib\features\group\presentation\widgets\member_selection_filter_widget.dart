import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import '../../../../core/app/widgets/custom_text_field.dart';
import '../../../members/controllers/member_controller.dart';

class MemberSelectionFilterWidget extends StatelessWidget {
  const MemberSelectionFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MemberController>();
    final dateFormat = DateFormat('yyyy-MM-dd');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'Filter Members',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () {
                  controller.clearFilters();
                },
                tooltip: 'Reset Filters',
              ),
            ],
          ),
          Gap(8.h),
          Wrap(
            spacing: 16.w,
            runSpacing: 16.h,
            children: [
              // Search field
              SizedBox(
                width: 200.w,
                child: buildSearchField(controller, context),
              ),

              // ID filter
              SizedBox(
                width: 200.w,
                child: CustomTextField(
                  label: 'Member ID',
                  controller: controller.idController,
                  hintText: 'Filter by ID',
                  onChanged: (value) {
                    controller.setIdFilter(value);
                  },
                ),
              ),

              // Date range filters
              SizedBox(
                width: 200.w,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Join Date From'),
                    Gap(4.h),
                    InkWell(
                      onTap: () async {
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate:
                              controller.startDate.value ?? DateTime.now(),
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2101),
                        );
                        if (picked != null) {
                          controller.setDateFilters(
                            picked,
                            controller.endDate.value,
                          );
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Obx(
                              () => Text(
                                controller.startDate.value != null
                                    ? dateFormat.format(
                                      controller.startDate.value!,
                                    )
                                    : 'Select date',
                              ),
                            ),
                            const Spacer(),
                            const Icon(IconlyLight.calendar, size: 18),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(
                width: 200.w,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Join Date To'),
                    Gap(4.h),
                    InkWell(
                      onTap: () async {
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate:
                              controller.endDate.value ?? DateTime.now(),
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2101),
                        );
                        if (picked != null) {
                          controller.setDateFilters(
                            controller.startDate.value,
                            picked,
                          );
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Obx(
                              () => Text(
                                controller.endDate.value != null
                                    ? dateFormat.format(
                                      controller.endDate.value!,
                                    )
                                    : 'Select date',
                              ),
                            ),
                            const Spacer(),
                            const Icon(IconlyLight.calendar, size: 18),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Apply filters button
              SizedBox(
                width: 200.w,
                child: CustomButton(
                  onPressed: () {
                    controller.fetchMembers();
                  },
                  text: 'Apply Filters',
                  icon: const Icon(IconlyLight.filter, size: 18),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildSearchField(MemberController controller, BuildContext context) {
    return CustomTextField(
      controller: controller.searchController,
      hintText: 'Search members...',
      prefixIcon: const Icon(IconlyLight.search),

      onSubmitted: (value) {
        controller.setSearchQuery(value);
      },
    );
  }
}
