// Build the profile header section
import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:onechurch/core/app/utils/screen_breakpoints.dart';
import 'package:onechurch/data/models/member_model.dart';

String formatDate(DateTime? date) {
  if (date == null) return 'N/A';
  return DateFormat('dd MMM yyyy').format(date);
}

class ProfileHeader extends StatelessWidget {
  final MemberModel m;
  const ProfileHeader({super.key, required this.m});
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet =
        screenWidth >= ScreenBreakpoints.mobile &&
        screenWidth < ScreenBreakpoints.tablet;
    final isDesktopSmall =
        screenWidth >= ScreenBreakpoints.tablet &&
        screenWidth < ScreenBreakpoints.desktopLarge;
    final isDesktopLarge = screenWidth >= ScreenBreakpoints.desktopLarge;
    final isDesktop =
        isDesktopSmall || isDesktopLarge; // Used in the if condition below

    // Enhanced profile for desktop view
    if (isDesktop) {
      return Column(
        children: [
          Stack(
            alignment: Alignment.bottomRight,
            children: [
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: theme.colorScheme.primary.withOpacity(0.2),
                      blurRadius: 12,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: CircleAvatar(
                  radius: isDesktopLarge ? 90.r : 80.r,
                  backgroundColor: theme.colorScheme.surface,
                  backgroundImage:
                      m.profileUrl != null && m.profileUrl!.isNotEmpty
                          ? NetworkImage(m.profileUrl!)
                          : null,
                  child:
                      m.profileUrl == null || m.profileUrl!.isEmpty
                          ? Icon(
                            IconlyLight.profile,
                            size: isDesktopLarge ? 80.r : 70.r,
                            color: theme.colorScheme.primary.withOpacity(0.7),
                          )
                          : null,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: m.status == 'ACTIVE' ? Colors.green : Colors.orange,
                  borderRadius: BorderRadius.circular(20.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  m.status ?? 'INACTIVE',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
          Gap(24.h),
          Text(
            '${m.firstName ?? ''} ${m.secondName ?? ''}',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 28,
            ),
            textAlign: TextAlign.center,
          ),
          Gap(8.h),
          Text(
            'Member ID: ${m.accountNumber ?? 'N/A'}',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
          Gap(16.h),
          Divider(color: theme.colorScheme.outline.withOpacity(0.2)),
          Gap(16.h),

          // Member quick info
          QuickInfoItem(
            icon: IconlyLight.call,
            label: 'Phone',
            value: m.phoneNumber ?? 'N/A',
          ),
          Gap(12.h),
          QuickInfoItem(
            icon: IconlyLight.message,
            label: 'Email',
            value: m.email ?? 'N/A',
          ),
          Gap(12.h),
          QuickInfoItem(
            icon: IconlyLight.calendar,
            label: 'Join Date',
            value: formatDate(m.joinDate),
          ),

          Gap(24.h),
        ],
      );
    }

    // Standard profile for mobile and tablet
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.primary.withOpacity(0.2),
                blurRadius: 12,
                spreadRadius: 2,
              ),
            ],
          ),
          child: CircleAvatar(
            radius: isTablet ? 70.r : 60.r,
            backgroundColor: theme.colorScheme.surface,
            backgroundImage:
                m.profileUrl != null && m.profileUrl!.isNotEmpty
                    ? NetworkImage(m.profileUrl!)
                    : null,
            child:
                m.profileUrl == null || m.profileUrl!.isEmpty
                    ? Icon(IconlyLight.profile, size: isTablet ? 70.r : 60.r)
                    : null,
          ),
        ),
        Gap(16.h),
        Text(
          '${m.firstName ?? ''} ${m.secondName ?? ''}',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        Gap(8.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
          decoration: BoxDecoration(
            color:
                m.status == 'ACTIVE'
                    ? Colors.green.shade100
                    : Colors.orange.shade100,
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Text(
            m.status ?? 'INACTIVE',
            style: TextStyle(
              color:
                  m.status == 'ACTIVE'
                      ? Colors.green.shade800
                      : Colors.orange.shade800,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Gap(8.h),
        Text(
          'Member ID: ${m.accountNumber ?? 'N/A'}',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }
}

// Helper method for desktop profile quick info items
class QuickInfoItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  const QuickInfoItem({
    super.key,
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Row(
      children: [
        Icon(icon, size: 18, color: theme.colorScheme.primary),
        Gap(12.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

// Build a section with a title and a list of info cards
class InfoSection extends StatelessWidget {
  final String title;
  final List<Widget> infoCards;
  const InfoSection({super.key, required this.title, required this.infoCards});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeader(title: title),
        Gap(16.h),
        ...infoCards.expand((card) => [card, Gap(12.h)]).toList()..removeLast(),
      ],
    );
  }
}

class SectionHeader extends StatelessWidget {
  final String title;
  const SectionHeader({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < ScreenBreakpoints.mobile;
    final isDesktop = screenWidth >= ScreenBreakpoints.tablet;
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 4.w,
              height: isDesktop ? 24.h : (isMobile ? 16.h : 20.h),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            Gap(8.w),
            Text(
              title,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: isMobile ? 18 : (isDesktop ? 22 : 20),
              ),
            ),
          ],
        ),
        Gap(4.h),
        Divider(
          color: theme.colorScheme.primary.withOpacity(isDesktop ? 0.3 : 0.5),
          thickness: isDesktop ? 1.5 : 1,
          height: 16.h,
        ),
      ],
    );
  }
}
