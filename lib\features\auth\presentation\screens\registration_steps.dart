import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onechurch/core/app/translations/app_translations.dart';

import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import '../../../../core/widgets/custom_phone_input.dart';
import '../../controllers/auth_controller.dart';

class PersonalInfoStep extends StatefulWidget {
  final AuthController controller;

  const PersonalInfoStep({super.key, required this.controller});

  @override
  State<PersonalInfoStep> createState() => _PersonalInfoStepState();
}

class _PersonalInfoStepState extends State<PersonalInfoStep> {
  // Separate controller for the full name field
  final TextEditingController _fullNameController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Pre-fill full name if first/second name are already set
    if (widget.controller.firstNameController.text.isNotEmpty ||
        widget.controller.secondNameController.text.isNotEmpty) {
      _fullNameController.text =
          '${widget.controller.firstNameController.text} ${widget.controller.secondNameController.text}'
              .trim();
    }
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    super.dispose();
  }

  // Split the full name and update controllers when leaving the field
  void _updateNameControllers() {
    final fullName = _fullNameController.text.trim();

    if (fullName.isNotEmpty) {
      final parts = fullName.split(' ');
      if (parts.isNotEmpty) {
        widget.controller.firstNameController.text = parts[0];
        if (parts.length > 1) {
          widget.controller.secondNameController.text = parts
              .sublist(1)
              .join(' ');
        } else {
          widget.controller.secondNameController.text = '';
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('personal_info'),
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 24),
        CustomTextField(
          label: 'Full Name',
          controller: _fullNameController,
          prefixIcon: Icon(IconlyLight.profile),
          hintText: 'Enter your full name',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your full name';
            }

            // Update the first/second name controllers when validating
            _updateNameControllers();
            return null;
          },
          onChanged: null, // Remove the problematic onChanged handler
        ),
        const SizedBox(height: 16),
        CustomTextField(
          label: 'ID Number',
          controller: widget.controller.idNumberController,
          prefixIcon: Icon(IconlyLight.document),
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your ID number';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          label: 'Referral Code (Optional)',
          controller: widget.controller.referredByCodeController,
          prefixIcon: Icon(IconlyLight.ticket),
        ),
      ],
    );
  }
}

class ContactInfoStep extends StatefulWidget {
  final AuthController controller;

  const ContactInfoStep({super.key, required this.controller});

  @override
  State<ContactInfoStep> createState() => _ContactInfoStepState();
}

class _ContactInfoStepState extends State<ContactInfoStep> {
  String? _phoneError;
  // ignore: unused_field
  String _fullPhoneNumber = '';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Contact Information',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'Please provide at least one contact method',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 16),
        CustomTextField(
          label: 'Email',
          controller: widget.controller.emailController,
          keyboardType: TextInputType.emailAddress,
          prefixIcon: Icon(IconlyLight.message),
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              if (!RegExp(
                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
              ).hasMatch(value)) {
                return 'Please enter a valid email';
              }
            } else if (widget.controller.phoneController.text.isEmpty) {
              return 'Please provide either email or phone number';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomPhoneInput(
          label: 'Phone Number',
          controller: widget.controller.phoneController,
          errorText: _phoneError,
          onPhoneNumberChanged: (PhoneNumber phoneNumber) {
            // Store the full phone number with country code
            _fullPhoneNumber = phoneNumber.phoneNumber ?? '';

            // Update the phone controller with just the number part (without country code)
            if (phoneNumber.phoneNumber != null &&
                phoneNumber.dialCode != null) {
              final numberOnly = phoneNumber.phoneNumber!.substring(
                phoneNumber.dialCode!.length,
              );
              widget.controller.phoneController.text = numberOnly;
            }

            // Update the country code controller with the selected country code
            widget.controller.countryCodeController.text =
                phoneNumber.dialCode ?? '';

            // Validate phone number
            if (phoneNumber.phoneNumber == null ||
                phoneNumber.phoneNumber!.isEmpty) {
              if (widget.controller.emailController.text.isEmpty) {
                setState(() {
                  _phoneError = 'Please provide either email or phone number';
                });
              } else {
                setState(() {
                  _phoneError = null;
                });
              }
            } else {
              setState(() {
                _phoneError = null;
              });
            }
          },
        ),
      ],
    );
  }
}

class LocationStep extends StatelessWidget {
  final AuthController controller;

  const LocationStep({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Location Information',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 24),
        CustomTextField(
          label: 'County',
          controller: controller.countyController,
          prefixIcon: Icon(IconlyLight.location),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your county';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          label: 'Sub County',
          controller: controller.subCountyController,
          prefixIcon: Icon(IconlyLight.location),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your sub county';
            }
            return null;
          },
        ),
      ],
    );
  }
}

class AccountSetupStep extends StatelessWidget {
  final AuthController controller;

  const AccountSetupStep({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account Setup',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'You\'re almost done! Please review your information.',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 24),
        // Display a summary of user's information
        _buildSummaryItem(
          context,
          icon: (IconlyLight.profile),
          label: 'Name',
          value:
              '${controller.firstNameController.text} ${controller.secondNameController.text}',
        ),
        if (controller.emailController.text.isNotEmpty)
          _buildSummaryItem(
            context,
            icon: (IconlyLight.message),
            label: 'Email',
            value: controller.emailController.text,
          ),
        if (controller.phoneController.text.isNotEmpty)
          _buildSummaryItem(
            context,
            icon: (IconlyLight.call),
            label: 'Phone',
            value:
                '${controller.countryCodeController.text} ${controller.phoneController.text}',
          ),
        if (controller.idNumberController.text.isNotEmpty)
          _buildSummaryItem(
            context,
            icon: (IconlyLight.document),
            label: 'ID Number',
            value: controller.idNumberController.text,
          ),
        const SizedBox(height: 16),
        Obx(
          () =>
              controller.errorMessage.isNotEmpty
                  ? Padding(
                    padding: const EdgeInsets.only(bottom: 10),
                    child: Text(
                      controller.errorMessage.value,
                      style: TextStyle(
                        color: colorScheme.error,
                        fontFamily: 'Alata',
                      ),
                    ),
                  )
                  : const SizedBox.shrink(),
        ),
      ],
    );
  }

  Widget _buildSummaryItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: colorScheme.primary, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class PasswordStep extends StatelessWidget {
  final AuthController controller;

  const PasswordStep({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account Summary',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'You\'re almost done! Please review your information.',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 24),
        // Display a summary of user's information
        _buildSummaryItem(
          context,
          icon: (IconlyLight.profile),
          label: 'Name',
          value:
              '${controller.firstNameController.text} ${controller.secondNameController.text}',
        ),
        if (controller.emailController.text.isNotEmpty)
          _buildSummaryItem(
            context,
            icon: (IconlyLight.message),
            label: 'Email',
            value: controller.emailController.text,
          ),
        if (controller.phoneController.text.isNotEmpty)
          _buildSummaryItem(
            context,
            icon: (IconlyLight.call),
            label: 'Phone',
            value:
                '${controller.countryCodeController.text} ${controller.phoneController.text}',
          ),
        if (controller.idNumberController.text.isNotEmpty)
          _buildSummaryItem(
            context,
            icon: (IconlyLight.document),
            label: 'ID Number',
            value: controller.idNumberController.text,
          ),
        const SizedBox(height: 16),
        Obx(
          () =>
              controller.errorMessage.isNotEmpty
                  ? Padding(
                    padding: const EdgeInsets.only(bottom: 10),
                    child: Text(
                      controller.errorMessage.value,
                      style: TextStyle(
                        color: colorScheme.error,
                        fontFamily: 'Alata',
                      ),
                    ),
                  )
                  : const SizedBox.shrink(),
        ),
      ],
    );
  }

  Widget _buildSummaryItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: colorScheme.primary, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
