import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import '../../staff/models/staff_model.dart';
import '../controllers/staff_roles_controller.dart';
import '../bindings/staff_roles_binding.dart';
import '../../../core/app/constants/routes.dart';

/// Provides integration points between the staff management and staff roles features
class StaffRolesIntegration {
  /// Navigate to the staff role assignments screen for a specific staff member
  static void navigateToStaffRoleAssignments(
    BuildContext context,
    StaffModel staff,
  ) {
    // Ensure controllers are initialized
    if (!Get.isRegistered<StaffRolesController>()) {
      Get.put(StaffRolesBinding());
    }

    // Navigate to the staff role assignments screen using GoRouter
    context.go(
      Routes.STAFF_ROLE_ASSIGNMENTS.replaceFirst(':id', staff.id ?? ''),
      extra: staff,
    );
  }

  static Widget buildStaffRolesWidget(StaffModel staff) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Staff Roles',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                Builder(
                  builder:
                      (context) => CustomButton(
                        onPressed:
                            () =>
                                navigateToStaffRoleAssignments(context, staff),
                        icon: const Icon(Icons.manage_accounts, size: 16),
                        label: const Text('Manage Roles'),
                        
                      ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (staff.roleAssignments != null &&
                staff.roleAssignments!.isNotEmpty)
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount:
                    staff.roleAssignments!.length > 3
                        ? 3
                        : staff.roleAssignments!.length,
                itemBuilder: (context, index) {
                  final assignment = staff.roleAssignments![index];
                  return ListTile(
                    dense: true,
                    title: Text(assignment['role_name'] ?? 'Unknown Role'),
                    subtitle: Text(assignment['description'] ?? ''),
                    trailing:
                        assignment['is_active'] == true
                            ? const Chip(
                              label: Text('Active'),
                              backgroundColor: Colors.green,
                              labelStyle: TextStyle(color: Colors.white),
                            )
                            : const Chip(
                              label: Text('Inactive'),
                              backgroundColor: Colors.red,
                              labelStyle: TextStyle(color: Colors.white),
                            ),
                  );
                },
              )
            else
              const Padding(
                padding: EdgeInsets.all(8.0),
                child: Text(
                  'No roles assigned to this staff member',
                  style: TextStyle(fontStyle: FontStyle.italic),
                ),
              ),
            if (staff.roleAssignments != null &&
                staff.roleAssignments!.length > 3)
              Builder(
                builder:
                    (context) => TextButton(
                      onPressed:
                          () => navigateToStaffRoleAssignments(context, staff),
                      child: Text(
                        'View all ${staff.roleAssignments!.length} roles',
                      ),
                    ),
              ),
          ],
        ),
      ),
    );
  }
}
