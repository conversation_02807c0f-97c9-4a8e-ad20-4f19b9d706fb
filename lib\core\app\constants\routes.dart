// ignore_for_file: constant_identifier_names

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/data/models/event_model.dart' show EventModel;
import 'package:onechurch/features/admin/presentation/screens/main_admin.dart';
import 'package:onechurch/features/announcements/presentation/screens/announcements_screen.dart';
import 'package:onechurch/features/dashboard/presentation/screens/dashboard_screen.dart';
import 'package:onechurch/features/finances/binding/finance_binding.dart';
import 'package:onechurch/features/finances/presentation/admin/create_subaccount.dart';
import 'package:onechurch/features/finances/presentation/admin/main_finance.dart';
import 'package:onechurch/features/finances/presentation/admin/transactions_view.dart';
import 'package:onechurch/features/finances/presentation/screens/account_categories_screen.dart';
import 'package:onechurch/features/finances/presentation/screens/create_category.dart';
import 'package:onechurch/features/finances/presentation/screens/edit_category_screen.dart';
import 'package:onechurch/features/finances/presentation/screens/view_category_screen.dart';
import 'package:onechurch/data/models/sub_account_model.dart';
import 'package:onechurch/features/hymns/presentation/screens/downloaded_hymns_screen.dart';
import 'package:onechurch/features/hymns/presentation/screens/hymn_detail_screen.dart';
import 'package:onechurch/features/hymns/presentation/screens/hymn_upload_screen.dart';
import 'package:onechurch/features/hymns/presentation/screens/hymns_screen.dart';
import 'package:onechurch/features/inventory/presentation/screens/inventory_records_screen.dart';
import 'package:onechurch/features/inventory/presentation/screens/inventory_categories/inventory_categories_screen.dart';
import 'package:onechurch/features/inventory/presentation/screens/inventory_categories/add_inventory_category_screen.dart';
import 'package:onechurch/features/inventory/presentation/screens/inventory_categories/edit_inventory_category_screen.dart';
import 'package:onechurch/features/inventory/bindings/inventory_category_binding.dart';
import 'package:onechurch/features/organization_settings/presentation/screens/organization_settings_screen.dart';
import 'package:onechurch/features/organization_settings/bindings/organization_settings_binding.dart';
import 'package:onechurch/features/members/presentation/screens/member_relationships/view_member_relations_screen.dart';
import 'package:onechurch/features/notifications/presentation/screens/notification_detail_screen.dart';
import 'package:onechurch/features/notifications/presentation/screens/notifications_screen.dart';
import 'package:onechurch/features/sermons/presentation/screens/create_sermon_screen.dart';
import 'package:onechurch/features/sermons/presentation/screens/edit_sermon_screen.dart';
import 'package:onechurch/features/sermons/presentation/screens/sermon_screens.dart';
import 'package:onechurch/features/sms/presentation/screens/sms_create_screen.dart';
import 'package:onechurch/features/sms/presentation/screens/sms_requests_screen.dart';
import 'package:onechurch/features/sms/presentation/screens/sms_screen.dart';
import 'package:onechurch/features/staff/bindings/staff_binding.dart';
import 'package:onechurch/features/staff/models/staff_model.dart';
import 'package:onechurch/features/staff/presentation/screens/create_staff_screen.dart';
import 'package:onechurch/features/staff/presentation/screens/edit_staff_screen.dart';
import 'package:onechurch/features/staff/presentation/screens/staff_screen.dart';
import 'package:onechurch/features/staff/presentation/screens/view_staff_screen.dart'
    show ViewStaffScreen;
import 'package:onechurch/features/staff_roles/staff_roles_routes.dart';
import '../../../features/members/presentation/screens/members_screen/members_screen.dart';
import '../../../features/members/presentation/screens/create_member/add_multiple_members_screen.dart';
import '../../../features/members/presentation/screens/view_member/view_member_screen.dart';
import '../../../features/members/presentation/screens/edit_member_screen.dart';
import '../../../features/members/presentation/screens/member_cateories/member_categories_screen.dart';
import '../../../features/announcements/presentation/screens/create_announcement_screen.dart';
import '../../../features/announcements/presentation/screens/view_announcement_screen.dart';
import '../../../features/announcements/presentation/screens/edit_announcement_screen.dart';
import '../../../data/models/announcement_model.dart';
import '../../../features/members/presentation/screens/member_cateories/add_member_category_screen.dart';
import '../../../features/members/presentation/screens/member_cateories/edit_member_category_screen.dart';
import '../../../features/members/presentation/screens/helper_screen.dart';
import 'package:onechurch/features/splash/splash.dart';
import '../../../data/models/sermon_model.dart';
import 'package:onechurch/features/group/presentation/screens/group_detail_screen.dart'
    show GroupDetailScreen;
import '../../../data/models/group_model.dart';
import 'package:onechurch/features/sermons/presentation/screens/sermon_view_screen.dart'
    show SermonViewScreen;
import '../../../features/group/presentation/screens/create_group_screen.dart';
import '../../../features/group/presentation/screens/edit_group_screen.dart';
import '../../../features/group/presentation/view_groups.dart';
import '../services/auth_service.dart';
import '../../../features/auth/presentation/screens/login_screen.dart';
import '../../../features/auth/presentation/screens/signup_screen.dart';
import '../../../features/auth/presentation/screens/otp_verification.dart';
import '../../../features/auth/presentation/screens/set_pin_screen.dart';
import '../../../features/attendance/attendance_routes.dart';
import '../../../features/inventory/bindings/inventory_binding.dart';
import '../../../features/inventory/presentation/screens/inventory_dashboard_screen.dart';
import '../../../features/inventory/presentation/screens/inventory_items_screen.dart';
import '../../../features/inventory/presentation/screens/create_inventory_item_screen.dart';
import '../../../features/inventory/presentation/screens/record_inventory_screen.dart';
import '../../../features/inventory/presentation/screens/view_inventory_item_screen.dart';
import '../../../features/events/presentation/screens/events_screen.dart';
import '../../../features/events/presentation/screens/view_event_screen.dart';
import '../../../features/events/presentation/admin/create_event.dart';
import '../../../features/events/presentation/admin/edit_event.dart';
import '../../../features/giving/presentation/screens/giving_screen.dart';
import '../../../features/profile/presentation/screens/profile_screen.dart';
import '../../../features/settings/presentation/screens/settings_screen.dart';
import '../../../features/communications/presentation/screens/communications_screen.dart';
import '../../../features/finances/presentation/screens/finances_screen.dart';
import '../../../features/auth/presentation/screens/forgot_password.dart';
import '../../../features/giving/bindings/giving_binding.dart';
import '../../../features/profile/bindings/profile_binding.dart';
import '../../../features/settings/bindings/settings_binding.dart';
import '../../../features/splash/bindings/splash_binding.dart';
import '../../../features/media_upload/models/media_model.dart';
import '../../../features/media_upload/presentation/screens/media_confirmation_screen.dart';
import '../screens/not_found_page.dart';
import 'package:go_router/go_router.dart';

// Routes path constants
class Routes {
  static const SPLASH = '/';
  static const LOGIN = '/login';
  static const SIGNUP = '/signup';
  static const FORGOT_PASSWORD = '/forgot-password';
  static const OTP_VERIFICATION = '/otp-verification';
  static const SET_PIN = '/set-pin';
  static const HOME = '/home';
  static const ADMIN = '/admin';
  static const MAIN_ADMIN = '/main-admin';
  static const MEMBERS = '/members';
  static const ADD_MEMBER = '/members/add';
  static const VIEW_MEMBER = '/members/:id';
  static const VIEW_MEMBER_RELATIONS = '/member-relations/:id';
  static const EDIT_MEMBER = '/members/:id/edit';
  static const GIVING = '/giving';
  static const COMMUNITY = '/community';
  static const EVENTS = '/events';
  static const EVENT_DETAIL = '/events/:id';
  static const EVENT_CREATE = '/events/create';
  static const EVENT_EDIT = '/events/:id/edit';
  static const PROFILE = '/profile';
  static const SETTINGS = '/settings';
  static const COMMUNICATIONS = '/communications';
  static const FINANCES = '/finances';
  static const GROUPS = '/groups';
  static const GROUP_DETAIL = '/groups/:id';
  static const CREATE_GROUP = '/groups/create';
  static const GROUP_EDIT = '/groups/:id/edit';
  static const SERMONS = '/sermons';
  static const SERMONS_GRID = '/sermons/all';
  static const SERMON_CREATE = '/sermons/create';
  static const SERMON_EDIT = '/sermons/:id/edit';
  static const SERMON_DETAIL = '/sermons/:id';
  static const ANNOUNCEMENTS = '/announcements';
  static const CREATE_ANNOUNCEMENT = '/announcements/create';
  static const VIEW_ANNOUNCEMENT = '/announcements/:id';
  static const EDIT_ANNOUNCEMENT = '/announcements/:id/edit';
  static const NOTIFICATIONS = '/notifications';
  static const NOTIFICATION_DETAIL = '/notifications/:id';
  static const ADMIN_SCREEN = '/admin-screen';
  static const NOT_FOUND = '/not-found';
  static const DASHBOARD = '/dashboard';

  // Legacy routes for backward compatibility
  static const STATISTICS_DASHBOARD = '/dashboard';
  static const MEMBERS_CONFIG = '/members';
  static const GROUP_CREATE = '/groups/create';

  // finance
  static const FINANCE_ADMIN = '/finance';
  static const CREATE_SUB_ACCOUNT = '/finance/accounts/create';
  static const SUB_ACCOUNTS_VIEW = '/finance/accounts';
  static const TRANSACTIONS_VIEW = '/finance/transactions';
  static const ACCOUNT_CATEGORIES = '/finance/categories';
  static const CREATE_ACCOUNT_CATEGORY = '/finance/categories/create';
  static const VIEW_ACCOUNT_CATEGORY = '/finance/categories/:id';
  static const EDIT_ACCOUNT_CATEGORY = '/finance/categories/:id/edit';

  // SMS routes
  static const SMS = '/sms';
  static const SMS_CREATE = '/sms/create';
  static const SMS_REQUESTS = '/sms/requests';

  // HYMNS routes
  static const DOWNLOADED_HYMNS = '/downloaded-hymns';
  static const HYMN_DETAIL = '/downloaded-hymns';
  static const UPLOAD_HYMN = '/downloaded-hymns';
  static const HYMNS = '/hymns';

  // Member routes
  static const MEMBER_IMPORT_HELPER = '/members/import-helper';
  static const MEMBER_CATEGORIES = '/member-categories';
  static const ADD_MEMBER_CATEGORY = '/member-categories/add';
  static const EDIT_MEMBER_CATEGORY = '/member-categories/:id/edit';
  static const ADD_MULTIPLE_MEMBERS = '/members/add-multiple';

  // Media routes
  static const MEDIA_CONFIRMATION = '/media/confirmation';

  // Staff routes
  static const STAFF = '/staff';
  static const CREATE_STAFF = '/staff/create';
  static const EDIT_STAFF = '/staff/:id/edit';

  // Staff Roles routes
  static const STAFF_ROLES = '/staff-roles';
  static const CREATE_STAFF_ROLE = '/staff-roles/create';
  static const EDIT_STAFF_ROLE = '/staff-roles/:id/edit';
  static const STAFF_ROLE_ASSIGNMENTS = '/staff/:id/role-assignments';

  // Attendance routes
  static const ATTENDANCE = '/attendance';
  static const MARK_ATTENDANCE = '/attendance/mark';
  static const VIEW_ATTENDANCE = '/attendance/:id';
  static const EDIT_ATTENDANCE = '/attendance/:id/mark';

  // Inventory routes
  static const INVENTORY = '/inventory';
  static const INVENTORY_ITEMS = '/inventory/items';
  static const INVENTORY_RECORDS = '/inventory/records';
  static const CREATE_INVENTORY_ITEM = '/inventory/items/create';
  static const EDIT_INVENTORY_ITEM = '/inventory/items/:id/edit';
  static const VIEW_INVENTORY_ITEM = '/inventory/items/:id';
  static const RECORD_INVENTORY = '/inventory/records/create';
  static const EDIT_INVENTORY_RECORD = '/inventory/records/:id/edit';
  static const VIEW_INVENTORY_RECORD = '/inventory/records/:id';

  // Inventory Category routes
  static const INVENTORY_CATEGORIES = '/inventory-categories';
  static const ADD_INVENTORY_CATEGORY = '/inventory-categories/add';
  static const EDIT_INVENTORY_CATEGORY = '/inventory-categories/:id/edit';

  // Organization Settings routes
  static const ORGANIZATION_SETTINGS = '/organization-settings';
}

class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  static GlobalKey<NavigatorState> get shellNavigatorKey => _shellNavigatorKey;

  // Authentication guard for protected routes
  static String? _guardRoutes(BuildContext context, GoRouterState state) {
    final authService = Get.find<AuthService>();
    final isLoggedIn = authService.isLoggedIn;

    // List of routes that don't require authentication
    final publicRoutes = [
      Routes.SPLASH,
      Routes.LOGIN,
      Routes.SIGNUP,
      Routes.FORGOT_PASSWORD,
      Routes.OTP_VERIFICATION,
      Routes.SET_PIN,
    ];

    // Check if the current route is a public route
    final isPublicRoute = publicRoutes.contains(state.matchedLocation);

    // If not logged in and trying to access a protected route, redirect to login
    if (!isLoggedIn && !isPublicRoute && !kDebugMode) {
      return Routes.LOGIN;
    }

    // If logged in and trying to access auth routes, redirect to dashboard
    if (isLoggedIn && isPublicRoute && state.matchedLocation != Routes.SPLASH) {
      return Routes.DASHBOARD;
    }

    // Allow navigation to proceed
    return null;
  }

  static GoRouter createRouter() {
    return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: Routes.SPLASH,
      debugLogDiagnostics: true,
      redirect: _guardRoutes,
      errorBuilder: (context, state) {
        Logger().e(state.error);
        return const NotFoundPage();
      },
      routes: [
        // Splash and Auth routes
        GoRoute(
          path: Routes.SPLASH,
          builder: (context, state) {
            // Initialize splash binding
            SplashBinding().dependencies();
            return const SplashScreenView();
          },
        ),
        GoRoute(
          path: Routes.LOGIN,
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: Routes.SIGNUP,
          builder: (context, state) => const SignupScreen(),
        ),
        GoRoute(
          path: Routes.FORGOT_PASSWORD,
          builder: (context, state) => const ForgotPasswordScreen(),
        ),
        GoRoute(
          path: Routes.OTP_VERIFICATION,
          builder: (context, state) => const OtpVerificationScreen(),
        ),
        GoRoute(
          path: Routes.SET_PIN,
          builder: (context, state) => const SetPinScreen(),
        ),
        GoRoute(
          path: Routes.NOT_FOUND,
          builder: (context, state) => const NotFoundPage(),
        ),

        // Main application shell
        StatefulShellRoute.indexedStack(
          builder:
              (context, state, navigationShell) =>
                  AdminDrawerScreen(navKey: navigationShell),
          branches: [
            // Dashboard & Members branch
            StatefulShellBranch(
              routes: [
                GoRoute(
                  path: Routes.HOME,
                  builder: (context, state) => const DashboardScreen(),
                ),
                GoRoute(
                  path: Routes.DASHBOARD,
                  builder: (context, state) => const DashboardScreen(),
                ),
                GoRoute(
                  path: Routes.MEMBERS,
                  builder: (context, state) {
                    final isSelect =
                        state.extra != null &&
                                state.extra is Map &&
                                (state.extra as Map).containsKey('isSelect')
                            ? (state.extra as Map)['isSelect'] as bool
                            : false;
                    return MemberViewScreen(isSelect: isSelect);
                  },
                ),
                GoRoute(
                  path: Routes.ADD_MEMBER,
                  builder: (context, state) => const AddMultipleMembersScreen(),
                ),
                GoRoute(
                  path: Routes.VIEW_MEMBER,
                  builder: (context, state) {
                    final memberId = state.pathParameters['id'] ?? '';
                    return ViewMemberScreen(memberId: memberId);
                  },
                ),
                GoRoute(
                  path: Routes.VIEW_MEMBER_RELATIONS,
                  builder: (context, state) {
                    final memberId = state.pathParameters['id'] ?? '';
                    return ViewMemberRelationsScreen(memberId: memberId);
                  },
                ),
                GoRoute(
                  path: Routes.STAFF,
                  builder: (context, state) {
                    StaffBinding().dependencies();
                    return const StaffScreen();
                  },
                ),
                GoRoute(
                  path: Routes.CREATE_STAFF,
                  builder: (context, state) {
                    StaffBinding().dependencies();
                    return const CreateStaffScreen();
                  },
                ),
                GoRoute(
                  path: Routes.EDIT_STAFF,
                  builder: (context, state) {
                    StaffBinding().dependencies();
                    final staffId = state.pathParameters['id'] ?? '';
                    final staffModel = state.extra as StaffModel?;
                    return EditStaffScreen(staffId: staffId, staff: staffModel);
                  },
                ),

                GoRoute(
                  path: Routes.EDIT_MEMBER,
                  builder: (context, state) {
                    final memberId = state.pathParameters['id'] ?? '';
                    return EditMemberScreen(memberId: memberId);
                  },
                ),

                GoRoute(
                  path: Routes.STAFF,
                  builder: (context, state) {
                    // Initialize the staff binding
                    StaffBinding().dependencies();
                    return const StaffScreen();
                  },
                ),
                GoRoute(
                  path: Routes.CREATE_STAFF,
                  builder: (context, state) {
                    // Make sure binding is initialized
                    StaffBinding().dependencies();
                    return const CreateStaffScreen();
                  },
                ),
                GoRoute(
                  path: Routes.EDIT_STAFF,
                  builder: (context, state) {
                    // Make sure binding is initialized
                    StaffBinding().dependencies();
                    final staffId = state.pathParameters['id'] ?? '';
                    return EditStaffScreen(staffId: staffId);
                  },
                ),
                GoRoute(
                  path: '${Routes.STAFF}/:id/view',
                  builder: (context, state) {
                    // Make sure binding is initialized
                    StaffBinding().dependencies();
                    final staffId = state.pathParameters['id'] ?? '';
                    return ViewStaffScreen(staffId: staffId);
                  },
                ),
                ...getStaffRolesGoRoutes(),
                ...AttendanceRoutes.routes,
                GoRoute(
                  path: Routes.MEMBER_CATEGORIES,
                  builder: (context, state) => const MemberCategoriesScreen(),
                ),
                GoRoute(
                  path: Routes.ADD_MEMBER_CATEGORY,
                  builder: (context, state) => const AddMemberCategoryScreen(),
                ),
                GoRoute(
                  path: Routes.EDIT_MEMBER_CATEGORY,
                  builder: (context, state) {
                    final categoryId = state.pathParameters['id'] ?? '';
                    return EditMemberCategoryScreen(categoryId: categoryId);
                  },
                ),
                GoRoute(
                  path: Routes.MEMBER_IMPORT_HELPER,
                  builder: (context, state) => const MemberImportHelperScreen(),
                ),
                GoRoute(
                  path: Routes.ADD_MULTIPLE_MEMBERS,
                  builder: (context, state) => const AddMultipleMembersScreen(),
                ),
                GoRoute(
                  path: Routes.MEDIA_CONFIRMATION,
                  builder: (context, state) {
                    final extra = state.extra as Map<String, dynamic>?;
                    final selectedMedia =
                        extra?['selectedMedia'] as List<MediaModel>? ?? [];
                    final onConfirm =
                        extra?['onConfirm'] as Function(List<MediaModel>)?;

                    return MediaConfirmationScreen(
                      selectedMedia: selectedMedia,
                      onConfirm: onConfirm ?? (media) {},
                    );
                  },
                ),
              ],
            ),

            // Groups & Announcements branch
            StatefulShellBranch(
              routes: [
                GoRoute(
                  path: Routes.GROUPS,
                  builder: (context, state) => const ViewGroupsGrid(),
                ),
                GoRoute(
                  path: Routes.CREATE_GROUP,
                  builder: (context, state) => const CreateGroupScreen(),
                ),
                GoRoute(
                  path: Routes.GROUP_DETAIL,
                  builder: (context, state) {
                    final groupId = state.pathParameters['id'] ?? '';

                    // Handle different types of extra data
                    GroupModel? group;
                    try {
                      if (state.extra != null) {
                        if (state.extra is GroupModel) {
                          group = state.extra as GroupModel;
                        } else if (state.extra is Map<String, dynamic>) {
                          // Handle the case where extra contains group data
                          final extraMap = state.extra as Map<String, dynamic>;
                          if (extraMap.containsKey('group') &&
                              extraMap['group'] is GroupModel) {
                            group = extraMap['group'] as GroupModel;
                          } else if (extraMap.containsKey('title') ||
                              extraMap.containsKey('description')) {
                            group = GroupModel.fromJson(extraMap);
                          }
                        }
                      }
                    } catch (e) {
                      // If any parsing fails, group remains null and will be fetched by ID
                      group = null;
                    }

                    return GroupDetailScreen(groupId: groupId, group: group);
                  },
                ),
                GoRoute(
                  path: Routes.GROUP_EDIT,
                  builder: (context, state) {
                    final groupId = state.pathParameters['id'] ?? '';

                    // Extract group from extra data - group is required for edit screen
                    if (state.extra == null || state.extra is! GroupModel) {
                      // If no group object is passed, redirect to groups list
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        context.go(Routes.GROUPS);
                      });
                      return const Scaffold(
                        body: Center(
                          child: Text('Group data required for editing'),
                        ),
                      );
                    }

                    final group = state.extra as GroupModel;
                    return EditGroupScreen(groupId: groupId, group: group);
                  },
                ),
                GoRoute(
                  path: Routes.ANNOUNCEMENTS,
                  builder: (context, state) => const AnnouncementsScreen(),
                ),
                GoRoute(
                  path: Routes.CREATE_ANNOUNCEMENT,
                  builder: (context, state) => const CreateAnnouncementScreen(),
                ),
                GoRoute(
                  path: Routes.VIEW_ANNOUNCEMENT,
                  builder: (context, state) {
                    final announcementId = state.pathParameters['id'] ?? '';
                    final announcement = state.extra as AnnouncementModel?;
                    return ViewAnnouncementScreen(
                      announcementId: announcementId,
                      initialAnnouncement: announcement,
                    );
                  },
                ),
                GoRoute(
                  path: Routes.EDIT_ANNOUNCEMENT,
                  builder: (context, state) {
                    final announcementId = state.pathParameters['id'] ?? '';
                    final announcement = state.extra as AnnouncementModel?;
                    return EditAnnouncementScreen(
                      announcementId: announcementId,
                      initialAnnouncement: announcement,
                    );
                  },
                ),
              ],
            ),

            // Sermons & Communications branch
            StatefulShellBranch(
              routes: [
                GoRoute(
                  path: Routes.SERMONS,
                  builder: (context, state) => const ViewSermonsGrid(),
                ),
                GoRoute(
                  path: Routes.SERMONS_GRID,
                  builder: (context, state) => const ViewSermonsGrid(),
                ),
                GoRoute(
                  path: Routes.SERMON_CREATE,
                  builder: (context, state) => const CreateSermonScreen(),
                ),
                GoRoute(
                  path: Routes.SERMON_EDIT,
                  builder: (context, state) {
                    final sermonId = state.pathParameters['id'] ?? '';
                    final sermon = state.extra as SermonModel?;
                    return EditSermonScreen(
                      sermonId: sermonId,
                      initialSermon: sermon,
                    );
                  },
                ),
                GoRoute(
                  path: Routes.DOWNLOADED_HYMNS,
                  builder: (context, state) => const DownloadedHymnsScreen(),
                ),
                GoRoute(
                  path: Routes.UPLOAD_HYMN,
                  builder: (context, state) => const HymnUploadScreen(),
                ),
                GoRoute(
                  path: Routes.HYMN_DETAIL,
                  builder: (context, state) => const HymnDetailScreen(id: ''),
                ),
                GoRoute(
                  path: Routes.HYMNS,
                  builder: (context, state) => const HymnsScreen(),
                ),

                GoRoute(
                  path: Routes.SERMON_DETAIL,
                  builder: (context, state) {
                    final sermonId = state.pathParameters['id'] ?? '';

                    // Handle different types of extra data
                    SermonModel? sermon;
                    if (state.extra != null) {
                      if (state.extra is SermonModel) {
                        sermon = state.extra as SermonModel;
                      } else if (state.extra is Map<String, dynamic>) {
                        final extraMap = state.extra as Map<String, dynamic>;
                        sermon = extraMap['sermon'] as SermonModel?;
                      }
                    }

                    return SermonViewScreen(
                      sermonId: sermonId,
                      initialSermon: sermon,
                    );
                  },
                ),
                GoRoute(
                  path: Routes.SMS,
                  builder: (context, state) => const SmsScreen(),
                ),
                GoRoute(
                  path: Routes.SMS_REQUESTS,
                  builder: (context, state) => const SmsRequestsScreen(),
                ),
                GoRoute(
                  path: Routes.SMS_CREATE,
                  builder: (context, state) => const SmsCreateScreen(),
                ),
              ],
            ),

            // Finance branch
            StatefulShellBranch(
              routes: [
                GoRoute(
                  path: Routes.FINANCE_ADMIN,
                  builder: (context, state) => const SubAccountsView(),
                ),
                GoRoute(
                  path: Routes.NOTIFICATIONS,
                  builder: (context, state) => const NotificationsScreen(),
                ),
                GoRoute(
                  path: Routes.NOTIFICATION_DETAIL,
                  builder: (context, state) => const NotificationDetailScreen(),
                ),
                GoRoute(
                  path: Routes.TRANSACTIONS_VIEW,
                  builder: (context, state) => const TransactionsView(),
                ),
                // Other standalone routes
                GoRoute(
                  path: Routes.EVENTS,
                  builder: (context, state) => const EventsScreen(),
                ),
                GoRoute(
                  path: Routes.EVENT_CREATE,
                  builder: (context, state) => const CreateEventScreen(),
                ),
                GoRoute(
                  path: Routes.EVENT_EDIT,
                  builder: (context, state) {
                    final eventId = state.pathParameters['id'] ?? '';
                    final event = state.extra as EventModel?;
                    return EditEventScreen(
                      eventId: eventId,
                      initialEvent: event,
                    );
                  },
                ),
                GoRoute(
                  path: Routes.EVENT_DETAIL,
                  builder: (context, state) {
                    final eventId = state.pathParameters['id'] ?? '';
                    final event = state.extra as EventModel?;
                    return ViewEventScreen(
                      eventId: eventId,
                      initialEvent: event,
                    );
                  },
                ),

                GoRoute(
                  path: Routes.SUB_ACCOUNTS_VIEW,
                  builder: (context, state) => const SubAccountsView(),
                ),
                GoRoute(
                  path: Routes.CREATE_SUB_ACCOUNT,
                  builder: (context, state) => const CreateSubaccount(),
                ),
                GoRoute(
                  path: Routes.ACCOUNT_CATEGORIES,
                  builder: (context, state) {
                    // Initialize finance binding
                    FinanceBinding().dependencies();
                    return const AccountCategoriesScreen();
                  },
                ),
                GoRoute(
                  path: Routes.CREATE_ACCOUNT_CATEGORY,
                  builder: (context, state) {
                    // Initialize finance binding
                    FinanceBinding().dependencies();
                    return const CreateCategoryScreen();
                  },
                ),
                GoRoute(
                  path: Routes.VIEW_ACCOUNT_CATEGORY,
                  builder: (context, state) {
                    // Initialize finance binding
                    FinanceBinding().dependencies();
                    final categoryId = state.pathParameters['id'] ?? '';
                    final category = state.extra as SubAccountCategory?;
                    return ViewCategoryScreen(
                      categoryId: categoryId,
                      initialCategory: category,
                    );
                  },
                ),
                GoRoute(
                  path: Routes.EDIT_ACCOUNT_CATEGORY,
                  builder: (context, state) {
                    // Initialize finance binding
                    FinanceBinding().dependencies();
                    final categoryId = state.pathParameters['id'] ?? '';
                    final category = state.extra as SubAccountCategory?;
                    return EditCategoryScreen(
                      categoryId: categoryId,
                      initialCategory: category,
                    );
                  },
                ),
                // Inventory routes
                GoRoute(
                  path: Routes.INVENTORY,
                  builder: (context, state) {
                    // Initialize inventory binding
                    InventoryBinding().dependencies();
                    return const InventoryDashboardScreen();
                  },
                ),
                GoRoute(
                  path: Routes.CREATE_INVENTORY_ITEM,
                  builder: (context, state) {
                    // Initialize inventory binding
                    InventoryBinding().dependencies();
                    return const CreateInventoryItemScreen();
                  },
                ),
                GoRoute(
                  path: Routes.RECORD_INVENTORY,
                  builder: (context, state) {
                    // Initialize inventory binding
                    InventoryBinding().dependencies();
                    return const RecordInventoryScreen();
                  },
                ),
                GoRoute(
                  path: Routes.INVENTORY_ITEMS,
                  builder: (context, state) {
                    // Initialize inventory binding
                    InventoryBinding().dependencies();
                    return InventoryItemsScreen();
                  },
                ),
                GoRoute(
                  path: Routes.INVENTORY_RECORDS,
                  builder: (context, state) {
                    // Initialize inventory binding
                    InventoryBinding().dependencies();
                    return const InventoryRecordsScreen();
                  },
                ),
                GoRoute(
                  path: Routes.VIEW_INVENTORY_ITEM,
                  builder: (context, state) {
                    // Initialize inventory binding
                    InventoryBinding().dependencies();
                    final itemId = state.pathParameters['id'] ?? '';
                    return ViewInventoryItemScreen(itemId: itemId);
                  },
                ),
                // Inventory Category routes
                GoRoute(
                  path: Routes.INVENTORY_CATEGORIES,
                  builder: (context, state) {
                    // Initialize inventory category binding
                    InventoryCategoryBinding().dependencies();
                    return const InventoryCategoriesScreen();
                  },
                ),
                GoRoute(
                  path: Routes.ADD_INVENTORY_CATEGORY,
                  builder: (context, state) {
                    // Initialize inventory category binding
                    InventoryCategoryBinding().dependencies();
                    return const AddInventoryCategoryScreen();
                  },
                ),
                GoRoute(
                  path: Routes.EDIT_INVENTORY_CATEGORY,
                  builder: (context, state) {
                    // Initialize inventory category binding
                    InventoryCategoryBinding().dependencies();
                    final categoryId = state.pathParameters['id'] ?? '';
                    return EditInventoryCategoryScreen(categoryId: categoryId);
                  },
                ),
              ],
            ),

            // Settings & Profile branch
            StatefulShellBranch(
              routes: [
                GoRoute(
                  path: Routes.SETTINGS,
                  builder: (context, state) {
                    // Initialize settings binding
                    SettingsBinding().dependencies();
                    return const SettingsScreen();
                  },
                ),
                GoRoute(
                  path: Routes.PROFILE,
                  builder: (context, state) {
                    // Initialize profile binding
                    ProfileBinding().dependencies();
                    return const ProfileScreen();
                  },
                ),
                GoRoute(
                  path: Routes.ORGANIZATION_SETTINGS,
                  builder: (context, state) {
                    // Initialize organization settings binding
                    OrganizationSettingsBinding().dependencies();
                    return const OrganizationSettingsScreen();
                  },
                ),
              ],
            ),
          ],
        ),

        GoRoute(
          path: Routes.GIVING,
          builder: (context, state) {
            // Initialize giving binding
            GivingBinding().dependencies();
            return const GivingScreen();
          },
        ),
        GoRoute(
          path: Routes.COMMUNICATIONS,
          builder: (context, state) => const CommunicationsScreen(),
        ),
        GoRoute(
          path: Routes.FINANCES,
          builder: (context, state) => const FinancesScreen(),
        ),
      ],
    );
  }
}
