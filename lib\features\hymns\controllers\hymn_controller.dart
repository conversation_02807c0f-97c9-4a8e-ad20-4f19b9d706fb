import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../data/models/hymn_model.dart';
import '../../../core/app/services/storage_service.dart';
import '../services/hymn_service.dart';
import '../../../features/auth/controllers/auth_controller.dart';
import '../../../core/app/services/auth_service.dart';

class HymnController extends GetxController {
  final HymnService _hymnService = HymnService();
  final AuthService _authService = Get.find<AuthService>();
  final StorageService _storage = Get.find<StorageService>();
  final logger = Get.find<Logger>();

  // State variables
  final RxBool isLoading = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<HymnModel> hymns = <HymnModel>[].obs;
  final RxList<HymnModel> bookmarkedHymns = <HymnModel>[].obs;
  final RxList<HymnModel> downloadedHymns = <HymnModel>[].obs;
  // Date filter variables
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);
  final RxInt currentPage = 0.obs;
  final RxBool hasMoreData = true.obs;
  final RxInt totalPages = 0.obs;
  final RxInt totalItems = 0.obs;
  final RxInt pageSize = 10.obs;
  final RxBool isLastPage = false.obs;
  final RxBool isFirstPage = true.obs;
  final RxString searchQuery = ''.obs;
  final TextEditingController searchController = TextEditingController();
  @override
  void onInit() {
    super.onInit();
    _loadSavedHymns();
    fetchHymns();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  Future<void> _loadSavedHymns() async {
    try {
      // Load downloaded hymns
      final downloadedJson =
          _storage.read<List<dynamic>>('downloaded_hymns') ?? [];
      downloadedHymns.value =
          downloadedJson.map((json) => HymnModel.fromJson(json)).toList();

      // Load bookmarked hymns
      final bookmarkedJson =
          _storage.read<List<dynamic>>('bookmarked_hymns') ?? [];
      bookmarkedHymns.value =
          bookmarkedJson.map((json) => HymnModel.fromJson(json)).toList();
    } catch (e) {
      logger.e('Error loading saved hymns: $e');
    }
  }

  Future<void> _saveHymns() async {
    try {
      // Save downloaded hymns
      await _storage.write(
        'downloaded_hymns',
        downloadedHymns.map((hymn) => hymn.toJson()).toList(),
      );

      // Save bookmarked hymns
      await _storage.write(
        'bookmarked_hymns',
        bookmarkedHymns.map((hymn) => hymn.toJson()).toList(),
      );
    } catch (e) {
      logger.e('Error saving hymns: $e');
    }
  }

  // Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    currentPage.value = 0; // Reset to first page when searching
    hasMoreData.value = true; // Reset pagination
    fetchHymns();
  }

  // Set date filters
  void setDateFilters(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
    currentPage.value = 0; // Reset to first page when filtering
    hasMoreData.value = true; // Reset pagination
    fetchHymns();
  }

  // Clear all filters
  void clearFilters() {
    searchQuery.value = '';
    searchController.clear();
    startDate.value = null;
    endDate.value = null;
    currentPage.value = 0;
    hasMoreData.value = true; // Reset pagination state
    hymns.clear(); // Clear existing hymns
    fetchHymns();
  }

  // Navigate to next page
  void nextPage() {
    if (!isLastPage.value) {
      currentPage.value++;
      fetchHymns();
    }
  }

  // Navigate to previous page
  void previousPage() {
    if (currentPage.value > 0) {
      currentPage.value--;
      fetchHymns();
    }
  }

  // Fetch hymns with pagination and filters
  Future<void> fetchHymns({bool loadMore = false}) async {
    if (loadMore) {
      if (isLoadingMore.value || !hasMoreData.value) return;
      isLoadingMore.value = true;
    } else {
      if (isLoading.value) return;
      isLoading.value = true;
      hymns.clear();
    }
    errorMessage.value = '';

    try {
      final response = await _hymnService.fetchHymns(
        page: currentPage.value,
        size: pageSize.value,
        searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
        startDate: startDate.value,
        endDate: endDate.value,
      );

      if (response["status"] ?? false) {
        final data = response["data"];

        // Parse pagination data
        currentPage.value = data["page"] ?? 0;
        totalPages.value = data["total_pages"] ?? 0;
        totalItems.value = data["total_items"] ?? 0;
        pageSize.value = data["size"] ?? 10;

        // Update pagination state
        isLastPage.value = currentPage.value >= totalPages.value - 1;
        isFirstPage.value = currentPage.value == 0; // Parse hymns
        final items = data["items"] as List<dynamic>;
        final newHymns = _hymnService.parseHymns(items);

        if (loadMore) {
          hymns.addAll(newHymns);
        } else {
          hymns.value = newHymns;
        }

        // Check if we have more data to load
        hasMoreData.value = currentPage.value < (totalPages.value - 1);
      } else {
        errorMessage.value = response["message"] ?? "Failed to fetch hymns";
        if (!loadMore) hymns.clear();
      }
    } catch (e) {
      logger.e('Error in fetchHymns: $e');
      errorMessage.value = "An error occurred while fetching hymns";
      if (!loadMore) hymns.clear();
    } finally {
      if (loadMore) {
        isLoadingMore.value = false;
      } else {
        isLoading.value = false;
      }
    }
  }

  // Get hymn by ID
  Future<HymnModel?> getHymnById(String id) async {
    try {
      final response = await _hymnService.getHymnById(id);
      if (response["status"] ?? false) {
        final data = response["data"];
        return HymnModel.fromJson(data);
      }
      return null;
    } catch (e) {
      logger.e('Error in getHymnById: $e');
      return null;
    }
  }

  // Create a new hymn
  Future<bool> createHymn({
    required String title,
    required String artist,
    required String album,
    required List<String> verses,
    required List<String> chorus,
    required String mediaUrl,
    required List<String> categoryIds,
  }) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      final currentOrg = Get.find<AuthController>().currentOrg.value;
      final userData = _authService.userData;

      final hymn = HymnModel(
        id: '',
        title: title,
        artist: artist,
        album: album,
        username: '',
        lyrics: Lyrics(verse: verses, chorus: chorus),
        mediaUrl: mediaUrl,
        organisationId: currentOrg?.id,
        createdByUserId: userData['ID'] ?? "",
        updatedByUserId: userData['ID'] ?? "",
        categories: [],
      );

      final response = await _hymnService.createHymn(hymn);
      if (response["status"] ?? false) {
        // Add categories if hymn creation was successful
        final hymnId = response["data"]["id"];
        await _hymnService.addHymnCategories(hymnId, categoryIds);

        // Refresh the hymns list
        await refreshHymns();
        return true;
      }

      errorMessage.value = response["message"] ?? "Failed to create hymn";
      return false;
    } catch (e) {
      logger.e('Error in createHymn: $e');
      errorMessage.value = "An error occurred while creating the hymn";
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Refresh hymns
  Future<void> refreshHymns() async {
    currentPage.value = 0;
    await fetchHymns();
  }

  // Update an existing hymn
  Future<bool> downloadHymn(HymnModel hymn) async {
    try {
      if (isHymnDownloaded(hymn.id)) {
        downloadedHymns.removeWhere((h) => h.id == hymn.id);
        await _saveHymns();
        return false;
      }

      downloadedHymns.add(hymn);
      await _saveHymns();
      return true;
    } catch (e) {
      logger.e('Error downloading hymn: $e');
      errorMessage.value = "Failed to download hymn";
      return false;
    }
  }

  Future<bool> bookmarkHymn(HymnModel hymn) async {
    try {
      if (isHymnBookmarked(hymn.id)) {
        bookmarkedHymns.removeWhere((h) => h.id == hymn.id);
        await _saveHymns();
        return false;
      }

      bookmarkedHymns.add(hymn);
      await _saveHymns();
      return true;
    } catch (e) {
      logger.e('Error bookmarking hymn: $e');
      errorMessage.value = "Failed to bookmark hymn";
      return false;
    }
  }

  bool isHymnBookmarked(String? hymnId) {
    return hymnId != null && bookmarkedHymns.any((h) => h.id == hymnId);
  }

  bool isHymnDownloaded(String? hymnId) {
    return hymnId != null && downloadedHymns.any((h) => h.id == hymnId);
  }
}
