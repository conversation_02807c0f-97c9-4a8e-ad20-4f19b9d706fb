import 'package:get/get.dart';
import '../controllers/inventory_controller.dart';
import '../controllers/inventory_item_controller.dart';
import '../controllers/create_inventory_item_controller.dart';
import '../controllers/record_inventory_controller.dart';
import '../services/inventory_service.dart';
import '../services/inventory_item_service.dart';

class InventoryBinding extends Bindings {
  @override
  void dependencies() {
    // Register services
    Get.lazyPut<InventoryService>(() => InventoryService());
    Get.lazyPut<InventoryItemService>(() => InventoryItemService());

    // Register controllers
    Get.lazyPut<InventoryController>(() => InventoryController());
    Get.lazyPut<InventoryItemController>(() => InventoryItemController());
    Get.lazyPut<CreateInventoryItemController>(
      () => CreateInventoryItemController(),
    );
    Get.lazyPut<RecordInventoryController>(() => RecordInventoryController());
  }
}
