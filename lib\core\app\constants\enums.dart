// ignore_for_file: deprecated_member_use

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../services/http_service.dart';
import '../services/api_urls.dart';

/// User roles enum
enum UserRole { admin, staff, member }

/// Business types enum
enum BusinessType {
  limitedCompany,
  soleProprietorship,
  individual,
  nonGovernmentalOrganization,
  communityBasedOrganization,
  church,
  religiousBasedInstitution,
}

/// Frequency enum
enum Frequency {
  onetime,
  annually,
  biannually,
  bimonthly,
  custom,
  daily,
  fortnightly,
  monthly,
  quarterly,
  weekly,
}

/// Group member roles enum
enum GroupMemberRole { admin, treasurer, secretary, member }

/// Invoice categories enum
enum InvoiceCategory { water, meterTest, installation }

/// Location types enum
enum LocationType {
  country,
  county,
  subcounty,
  ward,
  location,
  sublocation,
  estate,
  road,
}

/// Staff roles enum
enum StaffRole { admin, accountant, customerSupport, manager }

/// Asset ownership enum
enum AssetOwnership { none, land, livestock, business, vehicle, other }

/// Disability enum
enum Disability { none, physical, visual, hearing, mental, multiple, other }

/// Education level enum
enum EducationLevel {
  none,
  primary,
  secondary,
  tertiary,
  vocational,
  postgraduate,
  other,
  certificate,
  diploma,
  phd,
}

/// Employment status enum
enum EmploymentStatus {
  employed,
  selfEmployed,
  unemployed,
  student,
  retired,
  homemaker,
  other,
}

/// Gender enum
enum Gender { male, female, other, unknown }

/// Housing type enum
enum HousingType {
  permanent,
  semiPermanent,
  temporary,
  rental,
  informalSettlement,
  other,
}

/// Income bracket enum
enum IncomeBracket {
  none,
  lessThan10k,
  k10To30k,
  k30To60k,
  k60To100k,
  k100To200k,
  k200To300k,
  moreThan300k,
  unknown,
}

/// Marital status enum
enum MaritalStatus {
  single,
  married,
  divorced,
  widowed,
  separated,
  engaged,
  unknown,
}

/// Member relationship types categories enum
enum MemberRelationshipCategory {
  family,
  marital,
  guardianship,
  friendship,
  residential,
  work,
  other,
}

/// Location permission status enum
enum LocationPermissionStatus {
  granted,
  denied,
  deniedForever,
  whileInUse,
  always,
  unableToDetermine,
}

/// Location service status enum
enum LocationServiceStatus { enabled, disabled }

/// Location accuracy enum
enum LocationAccuracy { lowest, low, medium, high, best, bestForNavigation }

/// Location tracking status enum
enum LocationTrackingStatus { stopped, starting, tracking, paused, error }

/// Methods for handling enum values
extension BusinessTypeExtension on BusinessType {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case BusinessType.limitedCompany:
        return 'Limited Company';
      case BusinessType.soleProprietorship:
        return 'Sole proprietorship';
      case BusinessType.individual:
        return 'Individual';
      case BusinessType.nonGovernmentalOrganization:
        return 'Non-governmental organization';
      case BusinessType.communityBasedOrganization:
        return 'Community Based Organization';
      case BusinessType.church:
        return 'Church';
      case BusinessType.religiousBasedInstitution:
        return 'Religious Based Institution';
    }
  }

  static BusinessType? fromString(String value) {
    return BusinessType.values.firstWhereOrNull(
      (type) => type.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

/// Extension for Frequency enum
extension FrequencyExtension on Frequency {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case Frequency.annually:
        return 'ANNUALLY';
      case Frequency.biannually:
        return 'BIANNUALLY';
      case Frequency.bimonthly:
        return 'BIMONTHLY';
      case Frequency.custom:
        return 'CUSTOM';
      case Frequency.daily:
        return 'DAILY';
      case Frequency.fortnightly:
        return 'FORTNIGHTLY';
      case Frequency.monthly:
        return 'MONTHLY';
      case Frequency.quarterly:
        return 'QUARTERLY';
      case Frequency.weekly:
        return 'WEEKLY';
      case Frequency.onetime:
        return 'ONETIME';
    }
  }

  String get description {
    switch (this) {
      case Frequency.annually:
        return 'Occurs once a year';
      case Frequency.biannually:
        return 'Occurs once every two years';
      case Frequency.bimonthly:
        return 'Occurs once every two months';
      case Frequency.custom:
        return 'Custom Period';
      case Frequency.daily:
        return 'Occurs every day';
      case Frequency.fortnightly:
        return 'Occurs once every two weeks';
      case Frequency.monthly:
        return 'Occurs once a month';
      case Frequency.quarterly:
        return 'Occurs once every three months';
      case Frequency.weekly:
        return 'Occurs once a week';
      case Frequency.onetime:
        return 'Occurs once';
    }
  }

  static Frequency? fromString(String value) {
    return Frequency.values.firstWhereOrNull(
      (freq) => freq.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

/// Extension for GroupMemberRole enum
extension GroupMemberRoleExtension on GroupMemberRole {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case GroupMemberRole.admin:
        return 'ADMIN';
      case GroupMemberRole.treasurer:
        return 'TREASURER';
      case GroupMemberRole.secretary:
        return 'SECRETARY';
      case GroupMemberRole.member:
        return 'MEMBER';
    }
  }

  static GroupMemberRole? fromString(String value) {
    return GroupMemberRole.values.firstWhereOrNull(
      (role) => role.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

/// Extension for InvoiceCategory enum
extension InvoiceCategoryExtension on InvoiceCategory {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case InvoiceCategory.water:
        return 'water';
      case InvoiceCategory.meterTest:
        return 'meter_test';
      case InvoiceCategory.installation:
        return 'installation';
    }
  }

  static InvoiceCategory? fromString(String value) {
    return InvoiceCategory.values.firstWhereOrNull(
      (category) => category.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

/// Extension for LocationType enum
extension LocationTypeExtension on LocationType {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case LocationType.country:
        return 'COUNTRY';
      case LocationType.county:
        return 'COUNTY';
      case LocationType.subcounty:
        return 'SUBCOUNTY';
      case LocationType.ward:
        return 'WARD';
      case LocationType.location:
        return 'LOCATION';
      case LocationType.sublocation:
        return 'SUBLOCATION';
      case LocationType.estate:
        return 'ESTATE';
      case LocationType.road:
        return 'ROAD';
    }
  }

  static LocationType? fromString(String value) {
    return LocationType.values.firstWhereOrNull(
      (type) => type.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

/// Extension for StaffRole enum
extension StaffRoleExtension on StaffRole {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case StaffRole.admin:
        return 'ADMIN';
      case StaffRole.accountant:
        return 'ACCOUNTANT';
      case StaffRole.customerSupport:
        return 'CUSTOMER SUPPORT';
      case StaffRole.manager:
        return 'MANAGER';
    }
  }

  static StaffRole? fromString(String value) {
    return StaffRole.values.firstWhereOrNull(
      (role) => role.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

extension AssetOwnershipExtension on AssetOwnership {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case AssetOwnership.none:
        return 'NONE';
      case AssetOwnership.land:
        return 'LAND';
      case AssetOwnership.livestock:
        return 'LIVESTOCK';
      case AssetOwnership.business:
        return 'BUSINESS';
      case AssetOwnership.vehicle:
        return 'VEHICLE';
      case AssetOwnership.other:
        return 'OTHER';
    }
  }

  static AssetOwnership? fromString(String value) {
    return AssetOwnership.values.firstWhereOrNull(
      (type) => type.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

extension DisabilityExtension on Disability {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case Disability.none:
        return 'NONE';
      case Disability.physical:
        return 'PHYSICAL';
      case Disability.visual:
        return 'VISUAL';
      case Disability.hearing:
        return 'HEARING';
      case Disability.mental:
        return 'MENTAL';
      case Disability.multiple:
        return 'MULTIPLE';
      case Disability.other:
        return 'OTHER';
    }
  }

  static Disability? fromString(String value) {
    return Disability.values.firstWhereOrNull(
      (type) => type.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

extension EducationLevelExtension on EducationLevel {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case EducationLevel.none:
        return 'NONE';
      case EducationLevel.primary:
        return 'PRIMARY';
      case EducationLevel.secondary:
        return 'SECONDARY';
      case EducationLevel.tertiary:
        return 'TERTIARY';
      case EducationLevel.vocational:
        return 'VOCATIONAL';
      case EducationLevel.postgraduate:
        return 'POSTGRADUATE';
      case EducationLevel.other:
        return 'OTHER';
      case EducationLevel.certificate:
        return 'CERTIFICATE';
      case EducationLevel.diploma:
        return 'DIPLOMA';
      case EducationLevel.phd:
        return 'PHD';
    }
  }

  static EducationLevel? fromString(String value) {
    return EducationLevel.values.firstWhereOrNull(
      (type) => type.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

extension EmploymentStatusExtension on EmploymentStatus {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case EmploymentStatus.employed:
        return 'EMPLOYED';
      case EmploymentStatus.selfEmployed:
        return 'SELF_EMPLOYED';
      case EmploymentStatus.unemployed:
        return 'UNEMPLOYED';
      case EmploymentStatus.student:
        return 'STUDENT';
      case EmploymentStatus.retired:
        return 'RETIRED';
      case EmploymentStatus.homemaker:
        return 'HOMEMAKER';
      case EmploymentStatus.other:
        return 'OTHER';
    }
  }

  static EmploymentStatus? fromString(String value) {
    return EmploymentStatus.values.firstWhereOrNull(
      (type) => type.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

extension GenderExtension on Gender {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case Gender.male:
        return 'MALE';
      case Gender.female:
        return 'FEMALE';
      case Gender.other:
        return 'OTHER';
      case Gender.unknown:
        return 'UNKNOWN';
    }
  }

  static Gender? fromString(String value) {
    return Gender.values.firstWhereOrNull(
      (type) => type.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

extension HousingTypeExtension on HousingType {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case HousingType.permanent:
        return 'PERMANENT';
      case HousingType.semiPermanent:
        return 'SEMI_PERMANENT';
      case HousingType.temporary:
        return 'TEMPORARY';
      case HousingType.rental:
        return 'RENTAL';
      case HousingType.informalSettlement:
        return 'INFORMAL_SETTLEMENT';
      case HousingType.other:
        return 'OTHER';
    }
  }

  static HousingType? fromString(String value) {
    return HousingType.values.firstWhereOrNull(
      (type) => type.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

extension IncomeBracketExtension on IncomeBracket {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case IncomeBracket.none:
        return 'NONE';
      case IncomeBracket.lessThan10k:
        return 'LESS_THAN_10K';
      case IncomeBracket.k10To30k:
        return '10K_TO_30K';
      case IncomeBracket.k30To60k:
        return '30K_TO_60K';
      case IncomeBracket.k60To100k:
        return '60K_TO_100K';
      case IncomeBracket.k100To200k:
        return '100K_TO_200K';
      case IncomeBracket.k200To300k:
        return '200K_TO_300K';
      case IncomeBracket.moreThan300k:
        return 'MORE_THAN_300K';
      case IncomeBracket.unknown:
        return 'UNKNOWN';
    }
  }

  static IncomeBracket? fromString(String value) {
    return IncomeBracket.values.firstWhereOrNull(
      (type) => type.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

extension MaritalStatusExtension on MaritalStatus {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case MaritalStatus.single:
        return 'SINGLE';
      case MaritalStatus.married:
        return 'MARRIED';
      case MaritalStatus.divorced:
        return 'DIVORCED';
      case MaritalStatus.widowed:
        return 'WIDOWED';
      case MaritalStatus.separated:
        return 'SEPARATED';
      case MaritalStatus.engaged:
        return 'ENGAGED';
      case MaritalStatus.unknown:
        return 'UNKNOWN';
    }
  }

  static MaritalStatus? fromString(String value) {
    return MaritalStatus.values.firstWhereOrNull(
      (type) => type.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

extension MemberRelationshipCategoryExtension on MemberRelationshipCategory {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case MemberRelationshipCategory.family:
        return 'FAMILY';
      case MemberRelationshipCategory.marital:
        return 'MARITAL';
      case MemberRelationshipCategory.guardianship:
        return 'GUARDIANSHIP';
      case MemberRelationshipCategory.friendship:
        return 'FRIENDSHIP';
      case MemberRelationshipCategory.residential:
        return 'RESIDENTIAL';
      case MemberRelationshipCategory.work:
        return 'WORK';
      case MemberRelationshipCategory.other:
        return 'OTHER';
    }
  }

  static MemberRelationshipCategory? fromString(String value) {
    return MemberRelationshipCategory.values.firstWhereOrNull(
      (type) => type.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

/// Extension for LocationPermissionStatus enum
extension LocationPermissionStatusExtension on LocationPermissionStatus {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case LocationPermissionStatus.granted:
        return 'GRANTED';
      case LocationPermissionStatus.denied:
        return 'DENIED';
      case LocationPermissionStatus.deniedForever:
        return 'DENIED_FOREVER';
      case LocationPermissionStatus.whileInUse:
        return 'WHILE_IN_USE';
      case LocationPermissionStatus.always:
        return 'ALWAYS';
      case LocationPermissionStatus.unableToDetermine:
        return 'UNABLE_TO_DETERMINE';
    }
  }

  static LocationPermissionStatus? fromString(String value) {
    return LocationPermissionStatus.values.firstWhereOrNull(
      (status) => status.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

/// Extension for LocationServiceStatus enum
extension LocationServiceStatusExtension on LocationServiceStatus {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case LocationServiceStatus.enabled:
        return 'ENABLED';
      case LocationServiceStatus.disabled:
        return 'DISABLED';
    }
  }

  static LocationServiceStatus? fromString(String value) {
    return LocationServiceStatus.values.firstWhereOrNull(
      (status) => status.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

/// Extension for LocationAccuracy enum
extension LocationAccuracyExtension on LocationAccuracy {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case LocationAccuracy.lowest:
        return 'LOWEST';
      case LocationAccuracy.low:
        return 'LOW';
      case LocationAccuracy.medium:
        return 'MEDIUM';
      case LocationAccuracy.high:
        return 'HIGH';
      case LocationAccuracy.best:
        return 'BEST';
      case LocationAccuracy.bestForNavigation:
        return 'BEST_FOR_NAVIGATION';
    }
  }

  static LocationAccuracy? fromString(String value) {
    return LocationAccuracy.values.firstWhereOrNull(
      (accuracy) => accuracy.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

/// Extension for LocationTrackingStatus enum
extension LocationTrackingStatusExtension on LocationTrackingStatus {
  String get name => describeEnum(this);

  String get displayName {
    switch (this) {
      case LocationTrackingStatus.stopped:
        return 'STOPPED';
      case LocationTrackingStatus.starting:
        return 'STARTING';
      case LocationTrackingStatus.tracking:
        return 'TRACKING';
      case LocationTrackingStatus.paused:
        return 'PAUSED';
      case LocationTrackingStatus.error:
        return 'ERROR';
    }
  }

  static LocationTrackingStatus? fromString(String value) {
    return LocationTrackingStatus.values.firstWhereOrNull(
      (status) => status.displayName.toLowerCase() == value.toLowerCase(),
    );
  }
}

/// Class to fetch and store enum data from API
class EnumDataService {
  static final EnumDataService _instance = EnumDataService._internal();
  final HttpService _httpService = HttpService();
  final logger = Get.put<Logger>(Logger());

  // Cached enum data
  List<String> assetOwnership = [];
  List<String> businessTypes = [];
  Map<String, dynamic>? defaultSuperOrganisation;
  List<String> disability = [];
  List<String> educationLevel = [];
  List<String> employmentStatus = [];
  Map<String, String> frequencies = {};
  List<String> gender = [];
  List<String> generalLocations = [];
  List<String> groupMemberRoles = [];
  List<String> housingType = [];
  List<String> incomeBracket = [];
  List<String> invoiceCategories = [];
  List<String> locationTypes = [];
  List<String> maritalStatus = [];
  List<Map<String, dynamic>> memberCategoriesGeneral = [];
  List<String> memberRelationshipTypesCategories = [];
  List<Map<String, dynamic>> memberRelationshipTypesGeneral = [];
  List<Map<String, dynamic>> paymentChannels = [];
  List<Map<String, dynamic>> permissions = [];
  List<String> staffRoles = [];
  List<Map<String, dynamic>> unitsOfMeasure = [];

  // Singleton constructor
  factory EnumDataService() {
    return _instance;
  }

  EnumDataService._internal();

  /// Initialize and fetch enum data from API
  Future<bool> fetchEnumData() async {
    try {
      final response = await _httpService.request(
        url: ApiUrls.getEnums,
        method: Method.GET,
      );

      if (response.data["status"] == true) {
        final data = response.data["data"];

        // Parse asset ownership
        if (data["asset_ownership"] != null) {
          assetOwnership = List<String>.from(data["asset_ownership"]);
        }

        // Parse business types
        if (data["business_types"] != null) {
          businessTypes = List<String>.from(data["business_types"]);
        }

        // Parse default super organisation
        if (data["default_super_organisation"] != null) {
          defaultSuperOrganisation = Map<String, dynamic>.from(
            data["default_super_organisation"],
          );
        }

        // Parse disability
        if (data["disability"] != null) {
          disability = List<String>.from(data["disability"]);
        }

        // Parse education level
        if (data["education_level"] != null) {
          educationLevel = List<String>.from(data["education_level"]);
        }

        // Parse employment status
        if (data["employment_status"] != null) {
          employmentStatus = List<String>.from(data["employment_status"]);
        }

        // Parse frequencies
        if (data["frequencies"] != null) {
          frequencies = Map<String, String>.from(data["frequencies"]);
        }

        // Parse gender
        if (data["gender"] != null) {
          gender = List<String>.from(data["gender"]);
        }

        // Parse general locations
        if (data["general_locations"] != null) {
          generalLocations = List<String>.from(data["general_locations"]);
        }

        // Parse group member roles
        if (data["group_member_roles"] != null) {
          groupMemberRoles = List<String>.from(data["group_member_roles"]);
        }

        // Parse housing type
        if (data["housing_type"] != null) {
          housingType = List<String>.from(data["housing_type"]);
        }

        // Parse income bracket
        if (data["income_bracket"] != null) {
          incomeBracket = List<String>.from(data["income_bracket"]);
        }

        // Parse invoice categories
        if (data["invoice_categories"] != null) {
          invoiceCategories = List<String>.from(data["invoice_categories"]);
        }

        // Parse location types
        if (data["location_types"] != null) {
          locationTypes = List<String>.from(data["location_types"]);
        }

        // Parse marital status
        if (data["marital_status"] != null) {
          maritalStatus = List<String>.from(data["marital_status"]);
        }

        // Parse member categories general
        if (data["member_categories_general"] != null) {
          memberCategoriesGeneral = List<Map<String, dynamic>>.from(
            data["member_categories_general"].map(
              (item) => Map<String, dynamic>.from(item),
            ),
          );
        }

        // Parse member relationship types categories
        if (data["member_relationship_types_categories"] != null) {
          memberRelationshipTypesCategories = List<String>.from(
            data["member_relationship_types_categories"],
          );
        }

        // Parse member relationship types general
        if (data["member_relationship_types_general"] != null) {
          memberRelationshipTypesGeneral = List<Map<String, dynamic>>.from(
            data["member_relationship_types_general"].map(
              (item) => Map<String, dynamic>.from(item),
            ),
          );
        }

        // Parse payment channels
        if (data["payment_channels"] != null) {
          paymentChannels = List<Map<String, dynamic>>.from(
            data["payment_channels"].map(
              (item) => Map<String, dynamic>.from(item),
            ),
          );
        }

        // Parse permissions
        if (data["permissions"] != null) {
          permissions = List<Map<String, dynamic>>.from(
            data["permissions"].map((item) => Map<String, dynamic>.from(item)),
          );
        }

        // Parse staff roles
        if (data["staff_roles"] != null) {
          staffRoles = List<String>.from(data["staff_roles"]);
        }

        // Parse units of measure
        if (data["units_of_measure"] != null) {
          unitsOfMeasure = List<Map<String, dynamic>>.from(
            data["units_of_measure"].map(
              (item) => Map<String, dynamic>.from(item),
            ),
          );
        }

        return true;
      } else {
        logger.e("Failed to fetch enum data: ${response.data["message"]}");
        return false;
      }
    } catch (e) {
      logger.e("Error fetching enum data: $e");
      return false;
    }
  }
}
