import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/utils/general_utils.dart';
import 'package:onechurch/core/app/utils/show_toast.dart';
import 'package:onechurch/core/app/utils/size_config.dart';
import 'package:onechurch/core/app/widgets/custom_text_field.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import 'package:onechurch/core/widgets/html/markup_editor_widget.dart';
import 'package:onechurch/data/models/sub_account_model.dart';
import 'package:onechurch/features/finances/controllers/finance_controller.dart';

class CreateSubaccount extends StatefulWidget {
  const CreateSubaccount({super.key});

  @override
  State<CreateSubaccount> createState() => _CreateSubaccountState();
}

class _CreateSubaccountState extends State<CreateSubaccount> {
  final _formKey = GlobalKey<FormState>();
  TextEditingController titleController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();
  TextEditingController acctcontroller = TextEditingController();
  String _description = '';
  SubAccountCategory? category;
  FinanceController financeController = Get.find();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: SizeConfig.screenWidth * 0.13,
        vertical: SizeConfig.screenHeight * 0.1,
      ),
      child: Center(
        child: Card(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("Create Sub Account", style: theme.textTheme.titleLarge),
                  Gap(10.h),
                  Text(
                    "Title of the sub account *",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  TextFormField(
                    controller: titleController,
                    decoration: InputDecoration(
                      labelText: "Title",
                      hintText: "Enter sub account title",
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter a title';
                      }
                      return null;
                    },
                  ),
                  MarkupEditorWidget(
                    label: "Description",
                    isRequired: true,
                    hintText:
                        "Enter sub account description with rich formatting",
                    height: 200.h,
                    onChanged: (content) {
                      _description = content;
                    },
                  ),
                  Text(
                    "Category *",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Obx(
                    () => DropdownButtonFormField<SubAccountCategory>(
                      validator: (value) {
                        if (value == null) {
                          return 'Please select a category';
                        }
                        return null;
                      },
                      decoration: InputDecoration(
                        labelText: "Category",
                        labelStyle: TextStyle(
                          fontFamily: 'Alata',
                          color: colorScheme.onSurface.withValues(alpha: 0.6),
                        ),

                        filled: true,
                        fillColor: colorScheme.onSurface.withValues(
                          alpha: 0.05,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(color: colorScheme.primary),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide.none,
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(color: colorScheme.error),
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(color: colorScheme.error),
                        ),
                        errorStyle: TextStyle(
                          color: colorScheme.error,
                          fontFamily: 'Alata',
                        ),
                      ),
                      items:
                          financeController.categories
                              .map(
                                (e) => DropdownMenuItem<SubAccountCategory>(
                                  value: e,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("${e.title}"),
                                      Gap(5.h),
                                      Text(
                                        "${e.description}",
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          color: colorScheme.onSurface
                                              .withValues(alpha: 0.6),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                              .toList(),

                      value: category,

                      onChanged: (value) {
                        setState(() {
                          category = value;
                        });
                      },
                    ),
                  ),
                  Container(
                    alignment: Alignment.center,
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Text(
                              "Account Number",
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Spacer(),
                            IconButton(
                              icon: Icon(Icons.info_outline),
                              onPressed:
                                  () => GeneralUtils.showTooltip(
                                    "Preffered Account Number Reference, It Should be Unique",
                                    context,
                                  ),
                            ),
                          ],
                        ),
                        CustomTextField(
                          label: "Account(optional)",
                          controller: acctcontroller,
                        ),
                      ],
                    ),
                  ),

                  Obx(
                    () => Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: SizeConfig.screenWidth * 0.16,
                      ),
                      child: CustomButton(
                        text: "Create",
                        isLoading: financeController.isLoading.value,
                        onPressed:
                            financeController.isLoading.value
                                ? null
                                : () async {
                                  // Validate form
                                  if (!_formKey.currentState!.validate()) {
                                    return;
                                  }

                                  // Check if description is provided
                                  if (_description.trim().isEmpty) {
                                    ToastUtils.showToast(
                                      "Please enter a description",
                                      description: "Validation Error",
                                      toastType: ToastType.error,
                                    );
                                    return;
                                  }

                                  // Proceed with creation
                                  await financeController.createSubAccount(
                                    title: titleController.text.trim(),
                                    description: _description,
                                    category: category?.id,
                                    account: acctcontroller.text.trim(),
                                  );

                                  if (financeController.apiStatus.isFalse) {
                                    ToastUtils.showToast(
                                      financeController.message.value,
                                      description: "Error",
                                      toastType: ToastType.error,
                                    );
                                  } else {
                                    ToastUtils.showToast(
                                      financeController.message.value,
                                      description: "Success",
                                      toastType: ToastType.success,
                                    );
                                    Navigator.pop(context);
                                  }
                                },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
