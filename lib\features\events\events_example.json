{"status": true, "message": "success", "data": {"items": [{"id": "e389ddcf-df71-44f1-aad4-5c8b2dab0346", "created_at": "2025-04-26T04:36:51.231582+02:00", "updated_at": "2025-04-26T04:36:51.231582+02:00", "deleted_at": null, "title": "Sample Event Title", "start_date": "2025-05-01T10:00", "end_date": "2025-05-01T18:00", "status": "ACTIVE", "username": "sample-event-title", "description": "This is a sample description for the event.", "organisation_id": 1, "organisation": null, "created_by_user_id": 1, "created_by_user": {"ID": 1, "CreatedAt": "2025-04-25T19:51:44.784327+02:00", "UpdatedAt": "2025-04-25T19:52:14.207184+02:00", "DeletedAt": null, "phone_number": "254757015999", "first_name": "<PERSON>", "second_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "id_number": "39940307", "balance": 0, "birth_date": "", "country_code": "+254", "county": "<PERSON><PERSON><PERSON>", "sub_county": "<PERSON><PERSON>", "ward": "", "secondary_number": "", "profile_url": "", "role": "USER", "status": "AWAITING_PIN", "addresses": null, "devices": null}, "media": null}, {"id": "051155db-72c9-4520-983e-1df8304a1792", "created_at": "2025-04-26T04:43:15.750958+02:00", "updated_at": "2025-04-26T04:43:15.750958+02:00", "deleted_at": null, "title": "Youth fundraising event", "start_date": "2025-06-15T09:00", "end_date": "2025-06-15T17:00", "status": "ACTIVE", "username": "youth-fundraising-event", "description": "Join us for a special fundraising event to support our community outreach programs.", "organisation_id": 1, "organisation": null, "created_by_user_id": 1, "created_by_user": {"ID": 1, "CreatedAt": "2025-04-25T19:51:44.784327+02:00", "UpdatedAt": "2025-04-25T19:52:14.207184+02:00", "DeletedAt": null, "phone_number": "254757015999", "first_name": "<PERSON>", "second_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "id_number": "39940307", "balance": 0, "birth_date": "", "country_code": "+254", "county": "<PERSON><PERSON><PERSON>", "sub_county": "<PERSON><PERSON>", "ward": "", "secondary_number": "", "profile_url": "", "role": "USER", "status": "AWAITING_PIN", "addresses": null, "devices": null}, "media": null}], "page": 0, "size": 10, "max_page": 0, "total_pages": 1, "total": 2, "last": true, "first": true, "visible": 2}, "errors": null}