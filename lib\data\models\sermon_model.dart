// To parse this JSON data, do
//
//     final sermonModel = sermonModelFromJson(jsonString);

import 'dart:convert';

import 'package:onechurch/data/models/user_model.dart';
import 'package:onechurch/features/media_upload/models/media_model.dart';

List<SermonModel> sermonModelFromJson(String str) => List<SermonModel>.from(
  json.decode(str).map((x) => SermonModel.fromJson(x)),
);

String sermonModelToJson(List<SermonModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class SermonModel {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final String? description;
  final String? organisationId;
  final dynamic organisation;
  final String? username;
  final bool? isPinned;
  final String? category;
  final int? orderNumber;
  final String? status;
  final int? createdByUserId;
  final UserModel? createdByUser;
  final List<MediaModel>? media;

  SermonModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.description,
    this.organisationId,
    this.organisation,
    this.username,
    this.isPinned,
    this.category,
    this.orderNumber,
    this.status,
    this.createdByUserId,
    this.createdByUser,
    this.media,
  });

  SermonModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? title,
    String? description,
    String? organisationId,
    dynamic organisation,
    String? username,
    bool? isPinned,
    String? category,
    int? orderNumber,
    String? status,
    int? createdByUserId,
    UserModel? createdByUser,
    List<MediaModel>? media,
  }) => SermonModel(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
    deletedAt: deletedAt ?? this.deletedAt,
    title: title ?? this.title,
    description: description ?? this.description,
    organisationId: organisationId ?? this.organisationId,
    organisation: organisation ?? this.organisation,
    username: username ?? this.username,
    isPinned: isPinned ?? this.isPinned,
    category: category ?? this.category,
    orderNumber: orderNumber ?? this.orderNumber,
    status: status ?? this.status,
    createdByUserId: createdByUserId ?? this.createdByUserId,
    createdByUser: createdByUser ?? this.createdByUser,
    media: media ?? this.media,
  );

  factory SermonModel.fromJson(Map<String, dynamic> json) => SermonModel(
    id: json["id"],
    createdAt:
        json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt:
        json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    title: json["title"],
    description: json["description"],
    organisationId: json["organisation_id"],
    organisation: json["organisation"],
    username: json["username"],
    isPinned: json["is_pinned"],
    category: json["category"],
    orderNumber: json["order_number"],
    status: json["status"],
    createdByUserId: json["created_by_user_id"],
    createdByUser:
        json["created_by_user"] == null
            ? null
            : UserModel.fromJson(json["created_by_user"]),
    media:
        json["media"] == null
            ? []
            : List<MediaModel>.from(
              json["media"]!.map((x) => MediaModel.fromJson(x)),
            ),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "deleted_at": deletedAt,
    "title": title,
    "description": description,
    "organisation_id": organisationId,
    "organisation": organisation,
    "username": username,
    "is_pinned": isPinned,
    "category": category,
    "order_number": orderNumber,
    "status": status,
    "created_by_user_id": createdByUserId,
    "created_by_user": createdByUser?.toJson(),
    "media":
        media == null ? [] : List<dynamic>.from(media!.map((x) => x.toJson())),
  };
}
