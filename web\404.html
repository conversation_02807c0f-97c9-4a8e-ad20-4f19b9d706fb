<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Page Not Found - OneChurch</title>
  <link rel="icon" type="image/png" href="favicon.png"/>
  
  <!-- SPA Routing Fix: Redirect to index with the current path -->
  <script>
    // Store the current path for Flutter to use
    sessionStorage.setItem('flutter_route', window.location.pathname);
    // Redirect to the main app
    window.location.href = '/';
  </script>
  <style>
    body {
      font-family: 'Roboto', sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      text-align: center;
      padding: 2rem;
      max-width: 500px;
    }
    .error-icon {
      width: 120px;
      height: 120px;
      background-color: #ffebee;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto 2rem;
    }
    .error-icon svg {
      width: 60px;
      height: 60px;
      fill: #e53935;
    }
    h1 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: #333;
    }
    p {
      font-size: 1.1rem;
      margin-bottom: 2rem;
      color: #666;
      line-height: 1.5;
    }
    .btn {
      display: inline-block;
      background-color: #1e88e5;
      color: white;
      padding: 0.8rem 1.5rem;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.3s;
      margin-bottom: 1rem;
    }
    .btn:hover {
      background-color: #1565c0;
    }
    .btn-back {
      display: block;
      color: #1e88e5;
      text-decoration: none;
    }
    .btn-back:hover {
      text-decoration: underline;
    }
  </style>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
  
  <!-- Redirect to app router for handling -->
  <script>
    // Save the attempted URL to sessionStorage and redirect to the root
    window.onload = function() {
      var path = window.location.pathname;
      // Store the attempted path for retrieval
      sessionStorage.setItem('flutter_route', path);
      
      // Redirect to root immediately, don't show this page
      window.location.replace('/');
    };
  </script>
</head>
<body>
  <div class="container">
    <div class="error-icon">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
      </svg>
    </div>
    <h1>Page Not Found</h1>
    <p>The page you are looking for doesn't exist or has been moved.</p>
    <p>Redirecting you to home page...</p>
    <a href="/" class="btn">Go to Home</a>
    <a href="javascript:history.back()" class="btn-back">Go Back</a>
  </div>
</body>
</html>