import 'package:flutter/material.dart';
import 'dart:async';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/constants/routes.dart';
import 'package:onechurch/core/app/utils/size_config.dart';
import 'package:onechurch/core/app/widgets/loading_animations.dart';
import 'package:onechurch/features/dashboard/presentation/widgets/announcement_card.dart';
import 'package:onechurch/features/dashboard/presentation/widgets/statistic_card.dart';
import 'package:onechurch/features/dashboard/presentation/widgets/upcoming_event_card.dart';
import 'package:onechurch/features/events/controllers/event_controller.dart';
import 'package:onechurch/features/announcements/controllers/announcement_controller.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final EventController _eventController = Get.put(EventController());
  final AnnouncementController _announcementController = Get.put(
    AnnouncementController(),
  );

  late StreamSubscription _eventErrorSubscription;
  late StreamSubscription _announcementErrorSubscription;

  @override
  void initState() {
    super.initState();
    // Explicitly fetch data for both controllers with error handling
    _fetchDashboardData();

    // Add listeners for error handling
    _eventErrorSubscription = _eventController.errorMessage.listen((message) {
      if (message.isNotEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(message), backgroundColor: Colors.red),
        );
      }
    });

    _announcementErrorSubscription = _announcementController.errorMessage
        .listen((message) {
          if (message.isNotEmpty) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(message), backgroundColor: Colors.red),
            );
          }
        });
  }

  @override
  void dispose() {
    // Cancel subscriptions to prevent memory leaks
    _eventErrorSubscription.cancel();
    _announcementErrorSubscription.cancel();
    super.dispose();
  }

  void _fetchDashboardData() {
    // Explicitly fetch data for both controllers
    _eventController.fetchEvents();
    _announcementController.fetchAnnouncements();
  }

  @override
  Widget build(BuildContext context) {
    SizeConfig().init(context);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isTablet =
        MediaQuery.of(context).size.width >= 768 &&
        MediaQuery.of(context).size.width < 1024;
    final isDesktop = MediaQuery.of(context).size.width >= 1024;

    // Refresh controllers if needed
    _eventController.fetchEvents();
    _announcementController.fetchAnnouncements();

    return Scaffold(
      body: Obx(() {
        // Check if any controller is loading
        final isLoading =
            _eventController.isLoading.value ||
            _announcementController.isLoading.value;

        // Check if there are any error messages
        final hasErrors =
            _eventController.errorMessage.value.isNotEmpty ||
            _announcementController.errorMessage.value.isNotEmpty;

        if (isLoading) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                LoadingAnimations(),
                SizedBox(height: 16.h),
                Text('Loading dashboard...', style: theme.textTheme.bodyLarge),
              ],
            ),
          );
        }

        if (hasErrors) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, color: Colors.red, size: 64),
                SizedBox(height: 16.h),
                Text(
                  'Failed to load dashboard',
                  style: theme.textTheme.headlineSmall,
                ),
                Text(
                  _eventController.errorMessage.value.isNotEmpty
                      ? _eventController.errorMessage.value
                      : _announcementController.errorMessage.value,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.red,
                  ),
                ),
                SizedBox(height: 16.h),
                CustomButton(
                  onPressed: () {
                    // Clear previous error messages
                    _eventController.errorMessage.value = '';
                    _announcementController.errorMessage.value = '';

                    // Retry loading
                    _eventController.fetchEvents();
                    _announcementController.fetchAnnouncements();
                  },
                  icon: Icon(Icons.refresh),
                  label: Text('Retry'),
                 
                ),
              ],
            ),
          );
        }

        return Padding(
          padding: EdgeInsets.all(16.0.r),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Admin greeting
                Text(
                  'Welcome, ${'Admin'}',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Manage your church with ease',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                SizedBox(height: 24.h),

                // Quick action buttons
                isDesktop || isTablet
                    ? Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildQuickActionButton(
                          context,
                          icon: IconlyBold.user3,
                          label: 'Members',
                          onTap: () => context.go(Routes.MEMBERS),
                        ),
                        _buildQuickActionButton(
                          context,
                          icon: IconlyBold.calendar,
                          label: 'Events',
                          onTap: () => context.go(Routes.EVENTS),
                        ),
                        _buildQuickActionButton(
                          context,
                          icon: IconlyBold.chart,
                          label: 'Analytics',
                          onTap: () {},
                        ),
                        _buildQuickActionButton(
                          context,
                          icon: IconlyBold.wallet,
                          label: 'Finances',
                          onTap: () => context.go(Routes.FINANCE_ADMIN),
                        ),
                      ],
                    )
                    : Wrap(
                      alignment: WrapAlignment.spaceEvenly,
                      spacing: 16.w,
                      runSpacing: 16.h,
                      children: [
                        _buildQuickActionButton(
                          context,
                          icon: IconlyBold.user3,
                          label: 'Members',
                          onTap: () => context.go(Routes.MEMBERS),
                        ),
                        _buildQuickActionButton(
                          context,
                          icon: IconlyBold.calendar,
                          label: 'Events',
                          onTap: () => context.go(Routes.EVENTS),
                        ),
                        _buildQuickActionButton(
                          context,
                          icon: IconlyBold.chart,
                          label: 'Analytics',
                          onTap: () {},
                        ),
                        _buildQuickActionButton(
                          context,
                          icon: IconlyBold.wallet,
                          label: 'Finances',
                          onTap: () => context.go(Routes.FINANCE_ADMIN),
                        ),
                      ],
                    ),
                SizedBox(height: 24.h),

                // Statistics
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Statistics',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () {},
                      child: Text(
                        'View All',
                        style: TextStyle(color: colorScheme.primary),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
                isDesktop
                    ? Row(
                      children: [
                        Expanded(
                          child: StatisticCard(
                            title: 'Total Members',
                            value:
                                _eventController.isLoading.value
                                    ? 'Loading...'
                                    : 0.toString(),
                            icon: IconlyBold.user3,
                            color: Colors.blue,
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: StatisticCard(
                            title: 'Groups',
                            value:
                                _eventController.isLoading.value
                                    ? 'Loading...'
                                    : 0.toString(),
                            icon: IconlyBold.category,
                            color: Colors.orange,
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: StatisticCard(
                            title: 'Events',
                            value:
                                _eventController.isLoading.value
                                    ? 'Loading...'
                                    : _eventController.events.length.toString(),
                            icon: IconlyBold.calendar,
                            color: Colors.green,
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: StatisticCard(
                            title: 'Donations',
                            value: 'Ksh.0',
                            icon: IconlyBold.wallet,
                            color: Colors.purple,
                          ),
                        ),
                      ],
                    )
                    : Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: StatisticCard(
                                title: 'Total Members',
                                value: 0.toString(),
                                icon: IconlyBold.user3,
                                color: Colors.blue,
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Expanded(
                              child: StatisticCard(
                                title: 'Groups',
                                value: 0.toString(),
                                icon: IconlyBold.category,
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),
                        Row(
                          children: [
                            Expanded(
                              child: StatisticCard(
                                title: 'Events',
                                value:
                                    _eventController.events.length.toString(),
                                icon: IconlyBold.calendar,
                                color: Colors.green,
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Expanded(
                              child: StatisticCard(
                                title: 'Donations',
                                value: 'Ksh.${0}',
                                icon: IconlyBold.wallet,
                                color: Colors.purple,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                SizedBox(height: 24.h),

                // Upcoming events
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Upcoming Events',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () => context.go(Routes.EVENTS),
                      child: Text(
                        'View All',
                        style: TextStyle(color: colorScheme.primary),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
                _eventController.events.isEmpty
                    ? Center(
                      child: Text(
                        'No upcoming events',
                        style: theme.textTheme.bodyLarge,
                      ),
                    )
                    : SizedBox(
                      height: 180.h,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: _eventController.getUpcomingEvents().length,
                        itemBuilder: (context, index) {
                          final event =
                              _eventController.getUpcomingEvents()[index];
                          return UpcomingEventCard(event: event);
                        },
                      ),
                    ),
                SizedBox(height: 24.h),

                // Recent announcements
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Recent Announcements',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () => context.go(Routes.ANNOUNCEMENTS),
                      child: Text(
                        'View All',
                        style: TextStyle(color: colorScheme.primary),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
                _announcementController.announcements.isEmpty
                    ? Center(
                      child: Text(
                        'No announcements',
                        style: theme.textTheme.bodyLarge,
                      ),
                    )
                    : isDesktop
                    ? GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: _getAnnouncementCrossAxisCount(context),
                        childAspectRatio: _getAnnouncementAspectRatio(context),
                        crossAxisSpacing: 12.w,
                        mainAxisSpacing: 12.h,
                      ),
                      itemCount:
                          _announcementController.announcements.length > 5
                              ? 5
                              : _announcementController.announcements.length,
                      itemBuilder: (context, index) {
                        final announcement =
                            _announcementController.announcements[index];
                        return AnnouncementCard(announcement: announcement);
                      },
                    )
                    : ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount:
                          _announcementController.announcements.length > 5
                              ? 5
                              : _announcementController.announcements.length,
                      itemBuilder: (context, index) {
                        final announcement =
                            _announcementController.announcements[index];
                        return AnnouncementCard(announcement: announcement);
                      },
                    ),
              ],
            ),
          ),
        );
      }),
    );
  }

  int _getAnnouncementCrossAxisCount(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 1400) {
      return 4; // Large desktop screens
    } else if (screenWidth >= 1024) {
      return 3; // Standard desktop screens
    } else if (screenWidth >= 768) {
      return 2; // Tablet screens
    } else {
      return 1; // Mobile screens
    }
  }

  double _getAnnouncementAspectRatio(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 1400) {
      return 2.2; // More compact cards for large screens
    } else if (screenWidth >= 1024) {
      return 2.0; // Balanced ratio for desktop
    } else if (screenWidth >= 768) {
      return 2.5; // Slightly wider for tablets
    } else {
      return 3.0; // Wider for mobile
    }
  }

  Widget _buildQuickActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDesktop = MediaQuery.of(context).size.width >= 1024;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.all(isDesktop ? 12.r : 10.r),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(
                  icon,
                  color: colorScheme.primary,
                  size: isDesktop ? 26 : 22,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                label,
                style: theme.textTheme.labelLarge?.copyWith(
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
