import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart';
import '../services/staff_role_service.dart';
import '../models/staff_role_model.dart';
import '../models/permission_model.dart';

class StaffRolesController extends GetxController {
  final StaffRoleService _staffRoleService = StaffRoleService();
  final logger = Get.find<Logger>();

  // Observable variables
  final RxList<StaffRoleModel> roles = <StaffRoleModel>[].obs;
  final RxList<PermissionModel> permissions = <PermissionModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isCreating = false.obs;
  final RxBool isUpdating = false.obs;
  final RxBool isDeleting = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;

  // Form controllers
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();

  // Selected role for editing
  final Rx<StaffRoleModel?> selectedRole = Rx<StaffRoleModel?>(null);

  // Selected permissions for role creation/editing
  final RxList<PermissionModel> selectedPermissions = <PermissionModel>[].obs;

  @override
  void onInit() {
    super.onInit();
    fetchRoles();
    fetchPermissions();
  }

  @override
  void onClose() {
    nameController.dispose();
    descriptionController.dispose();
    super.onClose();
  }

  // Fetch all staff roles
  Future<void> fetchRoles() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final fetchedRoles = await _staffRoleService.fetchStaffRoles();

      // Debug log to see what data we're getting
      logger.d('Fetched ${fetchedRoles.length} roles');
      for (var role in fetchedRoles) {
        logger.d(
          'Role: ${role.name}, ID: ${role.id}, Permissions: ${role.permissions?.length ?? 0}',
        );
      }

      roles.assignAll(fetchedRoles);

      // Debug log after assignment
      logger.d('Assigned ${roles.length} roles to the observable list');

      isLoading.value = false;
    } catch (e) {
      isLoading.value = false;
      errorMessage.value = 'Failed to fetch roles: ${e.toString()}';
      logger.e('Error fetching roles: $e');
    }
  }

  // Fetch all permissions
  Future<void> fetchPermissions() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final fetchedPermissions = await _staffRoleService.getAllPermissions();
      permissions.assignAll(fetchedPermissions);

      isLoading.value = false;
    } catch (e) {
      isLoading.value = false;
      errorMessage.value = 'Failed to fetch permissions: ${e.toString()}';
      logger.e('Error fetching permissions: $e');
    }
  }

  // Create a new staff role
  Future<bool> createRole() async {
    try {
      isCreating.value = true;
      errorMessage.value = '';

      // Prepare permissions list
      final List<Map<String, dynamic>> permissionsList =
          selectedPermissions
              .map(
                (permission) => {
                  'id': permission.id,
                  'code': permission.code,
                  'name': permission.name,
                  'description': permission.description,
                },
              )
              .toList();

      // Create role
      await _staffRoleService.createStaffRole(
        name: nameController.text,
        description: descriptionController.text,
        organisationId: Get.find<AuthController>().currentOrg.value?.id,
        permissions: permissionsList,
      );

      // Refresh roles list
      await fetchRoles();

      // Reset form
      resetForm();

      isCreating.value = false;
      successMessage.value = 'Role created successfully';
      return true;
    } catch (e) {
      isCreating.value = false;
      errorMessage.value = 'Failed to create role: ${e.toString()}';
      logger.e('Error creating role: $e');
      return false;
    }
  }

  // Update an existing staff role
  Future<bool> updateRole() async {
    try {
      if (selectedRole.value == null) {
        errorMessage.value = 'No role selected for update';
        return false;
      }

      isUpdating.value = true;
      errorMessage.value = '';

      // Prepare permissions list
      final List<Map<String, dynamic>> permissionsList =
          selectedPermissions
              .map(
                (permission) => {
                  'id': permission.id,
                  'code': permission.code,
                  'name': permission.name,
                  'description': permission.description,
                },
              )
              .toList();

      // Update role
      await _staffRoleService.updateStaffRole(
        id: selectedRole.value!.id!,
        name: nameController.text,
        description: descriptionController.text,
        organisationId: selectedRole.value!.organisationId,
        permissions: permissionsList,
      );

      // Refresh roles list
      await fetchRoles();

      // Reset form
      resetForm();

      isUpdating.value = false;
      successMessage.value = 'Role updated successfully';
      return true;
    } catch (e) {
      isUpdating.value = false;
      errorMessage.value = 'Failed to update role: ${e.toString()}';
      logger.e('Error updating role: $e');
      return false;
    }
  }

  // Delete a staff role
  Future<bool> deleteRole(String id) async {
    try {
      isDeleting.value = true;
      errorMessage.value = '';

      await _staffRoleService.deleteStaffRole(id);

      // Refresh roles list
      await fetchRoles();

      isDeleting.value = false;
      successMessage.value = 'Role deleted successfully';
      return true;
    } catch (e) {
      isDeleting.value = false;
      errorMessage.value = 'Failed to delete role: ${e.toString()}';
      logger.e('Error deleting role: $e');
      return false;
    }
  }

  // Add permission to role
  Future<bool> addPermissionToRole(String roleId, String permissionId) async {
    try {
      isUpdating.value = true;
      errorMessage.value = '';

      await _staffRoleService.addPermissionToRole(
        roleId: roleId,
        permissionId: permissionId,
      );

      // Refresh roles list
      await fetchRoles();

      isUpdating.value = false;
      successMessage.value = 'Permission added to role successfully';
      return true;
    } catch (e) {
      isUpdating.value = false;
      errorMessage.value = 'Failed to add permission to role: ${e.toString()}';
      logger.e('Error adding permission to role: $e');
      return false;
    }
  }

  // Remove permission from role
  Future<bool> removePermissionFromRole(
    String roleId,
    String permissionId,
  ) async {
    try {
      isUpdating.value = true;
      errorMessage.value = '';

      await _staffRoleService.removePermissionFromRole(
        roleId: roleId,
        permissionId: permissionId,
      );

      // Refresh roles list
      await fetchRoles();

      isUpdating.value = false;
      successMessage.value = 'Permission removed from role successfully';
      return true;
    } catch (e) {
      isUpdating.value = false;
      errorMessage.value =
          'Failed to remove permission from role: ${e.toString()}';
      logger.e('Error removing permission from role: $e');
      return false;
    }
  }

  // Set selected role for editing
  void setSelectedRole(StaffRoleModel role) {
    selectedRole.value = role;
    nameController.text = role.name ?? '';
    descriptionController.text = role.description ?? '';

    // Set selected permissions
    if (role.permissions != null) {
      selectedPermissions.assignAll(role.permissions!);
    } else {
      selectedPermissions.clear();
    }
  }

  // Toggle permission selection
  void togglePermissionSelection(PermissionModel permission) {
    if (selectedPermissions.contains(permission)) {
      selectedPermissions.remove(permission);
    } else {
      selectedPermissions.add(permission);
    }
  }

  // Reset form
  void resetForm() {
    nameController.clear();
    descriptionController.clear();
    selectedRole.value = null;
    selectedPermissions.clear();
  }
}
