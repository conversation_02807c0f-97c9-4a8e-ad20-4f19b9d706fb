import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../controllers/announcement_controller.dart';
import '../widgets/announcement_filter_widget.dart';
import '../../../../core/app/utils/show_toast.dart';
import '../../../../core/app/widgets/loading_animations.dart';
import '../../../../data/models/announcement_model.dart';
import '../../../../core/app/constants/routes.dart';
import 'package:go_router/go_router.dart';

class AnnouncementsScreen extends StatefulWidget {
  const AnnouncementsScreen({super.key});

  @override
  State<AnnouncementsScreen> createState() => _AnnouncementsScreenState();
}

class _AnnouncementsScreenState extends State<AnnouncementsScreen> {
  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  final controller = Get.put(AnnouncementController());

  @override
  void initState() {
    super.initState();
    setColumns();
  }

  List<PlutoRow> setupRows(List<AnnouncementModel> announcements) {
    return announcements.map((announcement) {
      return PlutoRow(
        cells: {
          'id': PlutoCell(value: announcement.id ?? ''),
          'title': PlutoCell(value: announcement.title ?? ''),
          'description': PlutoCell(value: announcement.description ?? ''),
          'username': PlutoCell(value: announcement.username ?? ''),
          'category': PlutoCell(value: announcement.category ?? ''),
          'type': PlutoCell(value: announcement.type ?? ''),
          'status': PlutoCell(value: announcement.status ?? 'active'),
          'media': PlutoCell(
            value: announcement.media != null ? announcement.media!.length : 0,
          ),
          'items': PlutoCell(
            value: announcement.items != null ? announcement.items!.length : 0,
          ),
          'created_at': PlutoCell(
            value:
                announcement.createdAt != null
                    ? DateFormat(
                      'dd MMM yyyy HH:mm',
                    ).format(announcement.createdAt!)
                    : 'N/A',
          ),
          'updated_at': PlutoCell(
            value:
                announcement.updatedAt != null
                    ? DateFormat(
                      'dd MMM yyyy HH:mm',
                    ).format(announcement.updatedAt!)
                    : 'N/A',
          ),
          'created_by': PlutoCell(
            value:
                announcement.createdByUser?.firstName != null &&
                        announcement.createdByUser?.secondName != null
                    ? '${announcement.createdByUser!.firstName} ${announcement.createdByUser!.secondName}'
                    : announcement.createdByUser?.firstName ??
                        announcement.createdByUser?.username ??
                        announcement.createdByUser?.email ??
                        'N/A',
          ),
          'actions': PlutoCell(value: announcement.id),
        },
      );
    }).toList();
  }

  void setColumns() {
    columns = [
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.text(),
        width: 80,
        minWidth: 80,
        enableRowChecked: true,
        hide: true, // Hidden but available for filtering
      ),
      PlutoColumn(
        title: 'Title',
        field: 'title',
        type: PlutoColumnType.text(),
        width: 200,
        minWidth: 150,
        frozen: PlutoColumnFrozen.none,
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        width: 300,
        minWidth: 200,
        frozen: PlutoColumnFrozen.none,
        renderer: (rendererContext) {
          final description = rendererContext.cell.value as String;
          // Strip HTML tags for display in grid
          final strippedDescription = description.replaceAll(
            RegExp(r'<[^>]*>'),
            '',
          );
          return Tooltip(
            message: strippedDescription,
            child: Text(
              strippedDescription.length > 100
                  ? '${strippedDescription.substring(0, 100)}...'
                  : strippedDescription,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          );
        },
      ),
      PlutoColumn(
        title: 'Username',
        field: 'username',
        type: PlutoColumnType.text(),
        width: 120,
        minWidth: 100,
      ),
      PlutoColumn(
        title: 'Category',
        field: 'category',
        type: PlutoColumnType.text(),
        width: 120,
        minWidth: 100,
      ),
      PlutoColumn(
        title: 'Type',
        field: 'type',
        type: PlutoColumnType.text(),
        width: 100,
        minWidth: 80,
      ),
      PlutoColumn(
        title: 'Media',
        field: 'media',
        type: PlutoColumnType.number(),
        width: 80,
        minWidth: 70,
        renderer: (rendererContext) {
          final count = rendererContext.cell.value as int;
          return count > 0
              ? Chip(
                label: Text('$count', style: const TextStyle(fontSize: 12)),
                backgroundColor: Colors.blue.shade100.withOpacity(0.2),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              )
              : const Text('None', style: TextStyle(color: Colors.grey));
        },
      ),
      PlutoColumn(
        title: 'Items',
        field: 'items',
        type: PlutoColumnType.number(),
        width: 80,
        minWidth: 70,
        renderer: (rendererContext) {
          final count = rendererContext.cell.value as int;
          return count > 0
              ? Chip(
                label: Text('$count', style: const TextStyle(fontSize: 12)),
                backgroundColor: Colors.green.shade100.withOpacity(0.2),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              )
              : const Text('None', style: TextStyle(color: Colors.grey));
        },
      ),
      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
        minWidth: 90,
        renderer: (rendererContext) {
          final status = rendererContext.cell.value as String;
          Color backgroundColor;
          Color textColor;

          switch (status.toLowerCase()) {
            case 'active':
              backgroundColor = Colors.green.shade100.withOpacity(0.2);
              textColor = Colors.green;
              break;
            case 'inactive':
              backgroundColor = Colors.grey.shade100.withOpacity(0.2);
              textColor = Colors.grey;
              break;
            case 'draft':
              backgroundColor = Colors.orange.shade100.withOpacity(0.2);
              textColor = Colors.orange;
              break;
            default:
              backgroundColor = Colors.blue.shade100.withOpacity(0.2);
              textColor = Colors.blue;
          }

          return Chip(
            backgroundColor: backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
              side: BorderSide(color: textColor, width: 0.6),
            ),
            label: Text(
              status.toUpperCase(),
              style: TextStyle(
                color: textColor,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          );
        },
      ),
      PlutoColumn(
        title: 'Created At',
        field: 'created_at',
        type: PlutoColumnType.text(),
        width: 150,
        minWidth: 130,
      ),
      PlutoColumn(
        title: 'Updated At',
        field: 'updated_at',
        type: PlutoColumnType.text(),
        width: 150,
        minWidth: 130,
      ),
      PlutoColumn(
        title: 'Created By',
        field: 'created_by',
        type: PlutoColumnType.text(),
        width: 150,
        minWidth: 120,
      ),
      PlutoColumn(
        title: 'Actions',
        field: 'actions',
        type: PlutoColumnType.text(),
        width: 150,
        minWidth: 140,
        frozen: PlutoColumnFrozen.end,
        renderer: (rendererContext) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.visibility, size: 20),
                onPressed: () {
                  final id = rendererContext.cell.value as String;
                  // Find the announcement object from the controller
                  final announcement = controller.announcements
                      .firstWhereOrNull((a) => a.id == id);
                  context.go(
                    '${Routes.ANNOUNCEMENTS}/$id',
                    extra: announcement,
                  );
                },
                tooltip: 'View',
              ),
              IconButton(
                icon: const Icon(Icons.edit, size: 20),
                onPressed: () {
                  final id = rendererContext.cell.value as String;
                  // Find the announcement object from the controller
                  final announcement = controller.announcements
                      .firstWhereOrNull((a) => a.id == id);
                  context.go(
                    '${Routes.ANNOUNCEMENTS}/$id/edit',
                    extra: announcement,
                  );
                },
                tooltip: 'Edit',
              ),
              IconButton(
                icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                onPressed: () {
                  final id = rendererContext.cell.value as String;
                  _showDeleteConfirmationDialog(id);
                },
                tooltip: 'Delete',
              ),
            ],
          );
        },
      ),
    ];
  }

  void _showDeleteConfirmationDialog(String id) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirm Delete'),
            content: const Text(
              'Are you sure you want to delete this announcement? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  controller.deleteAnnouncement(id);
                },
                child: const Text(
                  'Delete',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final showFilter = false.obs;
    return Scaffold(
      appBar: AppBar(
        title: const Text('Announcements'),
        actions: [
          if (MediaQuery.of(context).size.width > 600) ...[
            SizedBox(
              width: 180.w,
              child: AnnouncementFilterWidget().buildSearchField(
                Get.find<AnnouncementController>(),
                context,
              ),
            ),
            const SizedBox(width: 12),
          ],
          Obx(
            () => IconButton(
              onPressed: () {
                showFilter.value = !showFilter.value;
              },
              icon:
                  showFilter.value
                      ? Icon(Icons.filter_alt)
                      : Icon(Icons.filter_alt_off_outlined),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Filters section
          Obx(
            () =>
                showFilter.value
                    ? const AnnouncementFilterWidget()
                    : SizedBox(),
          ),

          // Stats and info
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Obx(
                  () => Text(
                    "Total Announcements: ${controller.totalItems}",
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                const Spacer(),
                Obx(
                  () =>
                      controller.isLoading.value
                          ? const LoadingAnimations()
                          : const SizedBox.shrink(),
                ),
              ],
            ),
          ),
          Gap(8.h),

          // Announcements list with PlutoGrid
          Expanded(
            child: Card(
              color: Theme.of(context).secondaryHeaderColor,
              margin: const EdgeInsets.all(12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              elevation: 4,
              child: PlutoGrid(
                mode: PlutoGridMode.selectWithOneTap,
                columns: columns,
                rows: rows,
                onLoaded: (PlutoGridOnLoadedEvent event) {
                  event.stateManager.setShowColumnFilter(true);
                  if (kDebugMode) {
                    debugPrint("Grid loaded");
                  }
                },
                onSelected: (event) {
                  if (kDebugMode) {
                    debugPrint("Selected: ${event.row?.cells['id']?.value}");
                  }
                },
                configuration: PlutoGridConfiguration(
                  style: PlutoGridStyleConfig(
                    activatedColor: const Color.fromARGB(255, 165, 205, 253),
                    cellTextStyle: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Color.fromARGB(255, 64, 64, 64),
                    ),
                    columnTextStyle: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blueGrey,
                    ),
                  ),
                  columnSize: const PlutoGridColumnSizeConfig(
                    autoSizeMode: PlutoAutoSizeMode.scale,
                    resizeMode: PlutoResizeMode.normal,
                  ),
                  scrollbar: const PlutoGridScrollbarConfig(
                    isAlwaysShown: false,
                    scrollbarThickness: 8,
                    scrollbarThicknessWhileDragging: 10,
                  ),
                ),
                createFooter: (stateManager) {
                  return PlutoLazyPagination(
                    initialPage: 0,
                    initialFetch: true,
                    fetchWithSorting: true,
                    fetchWithFiltering: true,
                    pageSizeToMove: null,
                    fetch: (pagReq) async {
                      controller.currentPage.value = pagReq.page;
                      debugPrint("Fetching page: ${pagReq.page}");

                      await controller.fetchAnnouncements();
                      if (controller.errorMessage.isNotEmpty) {
                        ToastUtils.showErrorToast(
                          controller.errorMessage.value,
                          null,
                        );
                      }

                      return Future.value(
                        PlutoLazyPaginationResponse(
                          totalPage: controller.totalPages.value,
                          rows: setupRows(controller.announcements),
                        ),
                      );
                    },
                    stateManager: stateManager,
                  );
                },
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          context.go(Routes.CREATE_ANNOUNCEMENT);
        },
        icon: const Icon(Icons.add),
        label: const Text('Create'),
      ),
    );
  }
}
