import 'package:flutter/material.dart';
import 'package:onechurch/core/app/widgets/right_sidebar.dart';
import '../../../../../core/app/utils/screen_breakpoints.dart';
import '../../../controllers/relationship_controller.dart';
import 'add_relationship_content.dart';

/// Shows the add relationship UI based on screen size
/// Uses right sidebar for screens > 600 and bottom sheet for smaller screens
void showAddRelationship(
  BuildContext context,
  RelationshipController relationshipController,
  String memberId,
) {
  final mediaQuery = MediaQuery.of(context);
  final isLargeScreen = mediaQuery.size.width > ScreenBreakpoints.tablet;

  if (isLargeScreen) {
    // Show right sidebar for larger screens
    RightSidebar.show(
      context: context,
      title: 'Add Relationships',
      onClose: () {},
      child: AddRelationshipContent(
        relationshipController: relationshipController,
        memberId: memberId,
        onClose: () {
          Navigator.of(context).pop();
        },
      ),
    );
  } else {
    // Show bottom sheet for smaller screens
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          expand: false,
          initialChildSize: 0.9,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Bottom sheet header
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      Text(
                        'Add Relationships',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                        tooltip: 'Close',
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),
                
                // Content
                Expanded(
                  child: AddRelationshipContent(
                    relationshipController: relationshipController,
                    memberId: memberId,
                    onClose: () {
                      Navigator.pop(context);
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
