import 'package:flutter/foundation.dart' show debugPrint;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_storage/get_storage.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../constants/enums.dart';

class AuthService extends GetxService {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final GetStorage _getStorage = GetStorage();
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  // User authentication state
  final RxBool _isLoggedIn = false.obs;
  bool get isLoggedIn => _isLoggedIn.value;

  // User data
  final Rx<Map<String, dynamic>> _userData = Rx<Map<String, dynamic>>({});
  Map<String, dynamic> get userData => _userData.value;

  // User role
  final Rx<UserRole> _userRole = Rx<UserRole>(UserRole.member);
  UserRole get userRole => _userRole.value;

  // User role - new property to determine if user is admin
  final RxBool _isAdmin = false.obs;

  // Keys for storage
  static const String _tokenKey = 'auth_token';
  static const String _userDataKey = 'user_data';
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _userRoleKey = 'user_role';

  // Initialize the service
  Future<AuthService> init() async {
    await GetStorage.init();
    await _loadAuthState();
    return this;
  }

  // Load authentication state from storage
  Future<void> _loadAuthState() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user != null) {
        // Get fresh token
        final token = await user.getIdToken(true);

        // Load user data from storage
        final userData = _getStorage.read(_userDataKey);
        final userRoleIndex = _getStorage.read(_userRoleKey) ?? 0;

        if (userData != null) {
          _userData.value = Map<String, dynamic>.from(userData);
          _isLoggedIn.value = true;
          _userRole.value = UserRole.values[userRoleIndex];

          // Store fresh token
          await _secureStorage.write(key: _tokenKey, value: token);

          // Set isAdmin based on user role
          _isAdmin.value = _userData.value['role'] == 'admin';
          _isAdmin.value = _isAdmin.value;
        }
      }
    } catch (e) {
      debugPrint('Error loading auth state: $e');
      await logout();
    }
  }

  // Login user and store details
  Future<void> login(
    String token,
    Map<String, dynamic> userData, {
    UserRole role = UserRole.member,
  }) async {
    try {
      // Store token securely
      await _secureStorage.write(key: _tokenKey, value: token);

      // Store user data
      _getStorage.write(_userDataKey, userData);
      _getStorage.write(_isLoggedInKey, true);
      _getStorage.write(_userRoleKey, role.index);

      // Update reactive variables
      _userData.value = userData;
      _isLoggedIn.value = true;
      _userRole.value = role;

      // Set isAdmin based on user role
      _isAdmin.value = role == UserRole.admin;
      _isAdmin(true);
    } catch (e) {
      debugPrint('Error during login: $e');
      throw Exception('Failed to store user data');
    }
  }

  // Logout user
  Future<void> logout() async {
    try {
      // Sign out from Firebase
      await _firebaseAuth.signOut();

      // Clear secure storage
      await _secureStorage.delete(key: _tokenKey);

      // Clear local storage
      _getStorage.remove(_userDataKey);
      _getStorage.write(_isLoggedInKey, false);

      // Reset reactive variables
      _userData.value = {};
      _isLoggedIn.value = false;
      _userRole.value = UserRole.member;
      _isAdmin.value = false;
    } catch (e) {
      debugPrint('Error during logout: $e');
    }
  }

  // Get current Firebase user
  User? get currentUser => _firebaseAuth.currentUser;

  // Get fresh token
  Future<String?> getFreshToken() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user != null) {
        final token = await user.getIdToken(true);
        await _secureStorage.write(key: _tokenKey, value: token);
        return token;
      }
      return null;
    } catch (e) {
      debugPrint('Error getting fresh token: $e');
      return null;
    }
  }

  // Check if user is admin
  bool get isAdmin => _isAdmin.value;
}
