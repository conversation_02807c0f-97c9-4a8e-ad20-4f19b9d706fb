import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../services/staff_role_service.dart';
import '../models/staff_role_assignment_model.dart';
import '../models/staff_role_model.dart';
import '../../staff/models/staff_model.dart';

class StaffRoleAssignmentController extends GetxController {
  final StaffRoleService _staffRoleService = StaffRoleService();
  final logger = Get.find<Logger>();

  // Observable variables
  final RxList<StaffRoleAssignmentModel> assignments = <StaffRoleAssignmentModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isAssigning = false.obs;
  final RxBool isUpdating = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;

  // Form controllers
  final TextEditingController descriptionController = TextEditingController();
  final Rx<DateTime> assignedAtDate = DateTime.now().obs;
  final Rx<DateTime?> expiresAtDate = Rx<DateTime?>(null);
  
  // Selected staff and role
  final Rx<StaffModel?> selectedStaff = Rx<StaffModel?>(null);
  final Rx<StaffRoleModel?> selectedRole = Rx<StaffRoleModel?>(null);
  final Rx<StaffRoleAssignmentModel?> selectedAssignment = Rx<StaffRoleAssignmentModel?>(null);
  final RxBool isActive = true.obs;

  @override
  void onClose() {
    descriptionController.dispose();
    super.onClose();
  }

  // Fetch staff role assignments
  Future<void> fetchStaffRoleAssignments(String staffId) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      final fetchedAssignments = await _staffRoleService.getStaffRoleAssignments(staffId);
      assignments.assignAll(fetchedAssignments);
      
      isLoading.value = false;
    } catch (e) {
      isLoading.value = false;
      errorMessage.value = 'Failed to fetch role assignments: ${e.toString()}';
      logger.e('Error fetching role assignments: $e');
    }
  }

  // Assign role to staff
  Future<bool> assignRoleToStaff() async {
    try {
      if (selectedStaff.value == null || selectedRole.value == null) {
        errorMessage.value = 'Staff and role must be selected';
        return false;
      }
      
      isAssigning.value = true;
      errorMessage.value = '';
      
      await _staffRoleService.assignRoleToStaff(
        roleId: selectedRole.value!.id!,
        staffId: selectedStaff.value!.id!,
        assignedAt: assignedAtDate.value,
        description: descriptionController.text,
        expiresAt: expiresAtDate.value,
      );
      
      // Refresh assignments list
      await fetchStaffRoleAssignments(selectedStaff.value!.id!);
      
      // Reset form
      resetForm();
      
      isAssigning.value = false;
      successMessage.value = 'Role assigned to staff successfully';
      return true;
    } catch (e) {
      isAssigning.value = false;
      errorMessage.value = 'Failed to assign role to staff: ${e.toString()}';
      logger.e('Error assigning role to staff: $e');
      return false;
    }
  }

  // Update staff role assignment
  Future<bool> updateStaffRoleAssignment() async {
    try {
      if (selectedAssignment.value == null || selectedStaff.value == null || selectedRole.value == null) {
        errorMessage.value = 'Assignment, staff, and role must be selected';
        return false;
      }
      
      isUpdating.value = true;
      errorMessage.value = '';
      
      await _staffRoleService.updateStaffRoleAssignment(
        id: selectedAssignment.value!.id!,
        staffId: selectedStaff.value!.id!,
        roleId: selectedRole.value!.id!,
        assignedAt: assignedAtDate.value,
        description: descriptionController.text,
        isActive: isActive.value,
        expiresAt: expiresAtDate.value,
      );
      
      // Refresh assignments list
      await fetchStaffRoleAssignments(selectedStaff.value!.id!);
      
      // Reset form
      resetForm();
      
      isUpdating.value = false;
      successMessage.value = 'Role assignment updated successfully';
      return true;
    } catch (e) {
      isUpdating.value = false;
      errorMessage.value = 'Failed to update role assignment: ${e.toString()}';
      logger.e('Error updating role assignment: $e');
      return false;
    }
  }

  // Set selected assignment for editing
  void setSelectedAssignment(StaffRoleAssignmentModel assignment) {
    selectedAssignment.value = assignment;
    descriptionController.text = assignment.description ?? '';
    assignedAtDate.value = assignment.assignedAt ?? DateTime.now();
    expiresAtDate.value = assignment.expiresAt;
    isActive.value = assignment.isActive ?? true;
  }

  // Set selected staff
  void setSelectedStaff(StaffModel staff) {
    selectedStaff.value = staff;
    // Fetch staff role assignments
    fetchStaffRoleAssignments(staff.id!);
  }

  // Set selected role
  void setSelectedRole(StaffRoleModel role) {
    selectedRole.value = role;
  }

  // Reset form
  void resetForm() {
    descriptionController.clear();
    assignedAtDate.value = DateTime.now();
    expiresAtDate.value = null;
    selectedAssignment.value = null;
    isActive.value = true;
  }
}
